# buildall.sh Linux Execution Fixes

## Issues Identified and Fixed

### 1. **Line Ending Problem (CRITICAL)**

**Issue**: The script had Windows line endings (CRLF - `\r\n`) instead of Unix line endings (LF - `\n`).
**Impact**: This causes Linux shells to fail with errors like:

- `/bin/bash^M: bad interpreter: No such file or directory`
- `syntax error near unexpected token`

**Fix Applied**: Converted all line endings from CRLF to LF using PowerShell:

```powershell
(Get-Content buildall.sh -Raw) -replace '`r`n', '`n' | Set-Content buildall.sh -NoNewline
```

### 2. **Bash Compatibility Issues**

**Issue**: Used `==` for string comparison which is not POSIX compliant.
**Fix Applied**: Changed `[ "$1" == "musl" ]` to `[ "$1" = "musl" ]`

### 3. **Error Handling**

**Issue**: No error handling - script would continue even if builds failed.
**Fix Applied**:

- Added `set -e` to exit on any error
- Added `set -u` to exit on undefined variables
- Added explicit error checking for each build command
- Added informative error messages

### 4. **Argument Validation**

**Issue**: <PERSON>ript didn't handle cases where no arguments were provided properly.
**Fix Applied**: Added `[ "$#" -gt 0 ]` check before accessing `$1`

### 5. **Variable Quoting**

**Issue**: Variables weren't properly quoted, which could cause issues with spaces.
**Fix Applied**: Quoted all variable references: `"$package"`, `"$TARGET"`

### 6. **Improved User Experience**

**Enhancements Added**:

- Progress messages for each package build
- Clear success/failure indicators
- Better formatting with separators
- Consolidated commented packages with explanatory text

## Fixed Script Features

### Error Handling

```bash
set -e  # Exit on any error
set -u  # Exit on undefined variables

if ! cargo build -p "$package" --release --target="$TARGET"; then
    echo "ERROR: Failed to build package: $package with target: $TARGET"
    exit 1
fi
```

### Robust Argument Parsing

```bash
if [ "$#" -gt 0 ] && [ "$1" = "musl" ]; then
    TARGET="x86_64-unknown-linux-musl"
elif [ "$#" -gt 0 ] && [ "$1" = "gnu" ]; then
    TARGET="x86_64-unknown-linux-gnu"
else
    echo "Building with default target (no --target specified)"
fi
```

### Enhanced Build Function

```bash
build_package() {
    local package=$1
    echo "Building package: $package"
    
    if [ -n "$TARGET" ]; then
        if ! cargo build -p "$package" --release --target="$TARGET"; then
            echo "ERROR: Failed to build package: $package with target: $TARGET"
            exit 1
        fi
    else
        if ! cargo build -p "$package" --release; then
            echo "ERROR: Failed to build package: $package"
            exit 1
        fi
    fi
    
    echo "Successfully built: $package"
}
```

## Usage

### Basic Usage

```bash
# Default target (native)
./buildall.sh

# MUSL target (static linking)
./buildall.sh musl

# GNU target (dynamic linking)
./buildall.sh gnu
```

### Prerequisites on Linux

1. **Make executable**: `chmod +x buildall.sh`
2. **Rust/Cargo installed**: Ensure `cargo` is in PATH
3. **MUSL target** (if using musl): `rustup target add x86_64-unknown-linux-musl`
4. **GNU target** (if using gnu): `rustup target add x86_64-unknown-linux-gnu`

## Verification Steps

### 1. Check Line Endings

```bash
# Should show only LF (0A), no CR (0D)
hexdump -C buildall.sh | head -5
```

### 2. Test Syntax

```bash
bash -n buildall.sh  # Should return no errors
```

### 3. Check Executable Permission

```bash
ls -la buildall.sh  # Should show 'x' permission
```

### 4. Verify Shebang

```bash
head -1 buildall.sh  # Should show "#!/bin/bash"
```

## Troubleshooting

### Common Issues and Solutions

1. **Permission Denied**

   ```bash
   chmod +x buildall.sh
   ```

2. **Line Ending Issues**

   ```bash
   # Using dos2unix (if available)
   dos2unix buildall.sh
   
   # Using sed
   sed -i 's/\r$//' buildall.sh
   ```

3. **Bash Not Found**

   ```bash
   # Check bash location
   which bash
   
   # Update shebang if needed
   #!/usr/bin/env bash
   ```

4. **Cargo Not Found**

   ```bash
   # Install Rust
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   source ~/.cargo/env
   ```

5. **Missing Target**

   ```bash
   # Add MUSL target
   rustup target add x86_64-unknown-linux-musl
   
   # Add GNU target  
   rustup target add x86_64-unknown-linux-gnu
   ```

## Helper Script

Use the provided `fix-buildall.sh` script to automatically fix common issues:

```bash
chmod +x fix-buildall.sh
./fix-buildall.sh
```

This script will:

- Convert line endings
- Make the script executable
- Verify syntax
- Check dependencies
- Provide usage instructions

## Performance Notes

The fixed script now includes:

- **Parallel builds**: Cargo's built-in parallelization
- **Early failure detection**: Stops on first error
- **Progress reporting**: Shows build status for each package
- **Clear error messages**: Helps identify which package failed

## Security Considerations

- Uses `set -e` and `set -u` for safer execution
- Properly quotes all variables to prevent injection
- Validates arguments before use
- Uses explicit error handling instead of ignoring failures

This ensures the build script is robust, secure, and compatible with Linux environments.
