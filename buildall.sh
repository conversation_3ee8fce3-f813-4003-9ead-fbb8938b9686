#!/bin/bash

# Usage: ./buildall.sh [musl|gnu]
#   musl - Build with x86_64-unknown-linux-musl target
#   gnu  - Build with x86_64-unknown-linux-gnu target 
#   (no args) - Build with default target (no --target specified)

set -e  # Exit on any error
set -u  # Exit on undefined variables

TARGET=""
if [ "$#" -gt 0 ] && [ "$1" = "musl" ]; then
    TARGET="x86_64-unknown-linux-musl"
    echo "Building with musl target: $TARGET"
elif [ "$#" -gt 0 ] && [ "$1" = "gnu" ]; then
    TARGET="x86_64-unknown-linux-gnu"
    echo "Building with gnu target: $TARGET"
else
    echo "Building with default target (no --target specified)"
fi

# Build function that handles target parameter
build_package() {
    local package=$1
    echo "Building package: $package"
    
    if [ -n "$TARGET" ]; then
        if ! cargo build -p "$package" --release --target="$TARGET"; then
            echo "ERROR: Failed to build package: $package with target: $TARGET"
            exit 1
        fi
    else
        if ! cargo build -p "$package" --release; then
            echo "ERROR: Failed to build package: $package"
            exit 1
        fi
    fi
    
    echo "Successfully built: $package"
}

echo "Starting build process..."
echo "=============================="

# Build all packages
build_package phoenix_accountriskcenter
build_package phoenix_akacenter
build_package phoenix_algorithmcenter
build_package phoenix_assetscenter
build_package phoenix_blackscholes
build_package phoenix_exchanger
build_package phoenix_matchserver
build_package phoenix_ordercenter
build_package phoenix_orderrouter
build_package phoenix_riskcenter
build_package phoenix_satcenter
build_package phoenix_settlecenter

echo "=============================="
echo "All packages built successfully!"

# Commented out packages - uncomment if needed:
# build_package phoenix_rustomsgateway
# build_package phoenix_logcenter
# build_package phoenix_hqcenter
# build_package phoenix_sockethqcenter
# build_package phoenix_stockaid
# build_package phoenix_tickcenter
# build_package phoenix_fiuhqsource
# build_package phoenix_hqcalccenter
