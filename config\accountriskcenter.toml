# [database]
# uri = "mysql://root:29481aaa61a8bc76@***********:13301/flower_stock"

[application]
apphost = "0.0.0.0"
appport = 7401
httpport = 7101
machineid = 14
nodeid = 7401

[system]
ignore_user_account = "40005"                #忽略的用户账户,如果多个账户，用","分割，注意中英文
ignore_fee_account = "40005,40016,200844"    #忽略费用的账户 运营贰:40016 运营叁:40385
persist_interval = 10                        #数据保存时间间隔，单位秒
position_interval = 10                       #计算持仓数据的间隔,单位秒
account_total = "*********"                  #总账户的unit_id
risk_restore = 0.02                          #风险率低于预警线多少点，恢复正常交易状态
assets_server = "http://************:7403"
aka_server = "http://************:7402"
sat_server = "http://************:7603"
log_server = "http://************:8420"
hqcenter_server = "http://************:8416"
manager_server = "http://************:7305"
mainnode = 1
loglevel = "info"
# unitrisk_interval_start = 16:30:00
# [redis]
# #redis集群，通过逗号分隔
# #The URL format is redis://[<username>][:<password>@]<hostname>[:port][/<db>]
# uri = "**********************************************************************************************/,**********************************************************************************************/,**********************************************************************************************/"

# [redis]
# prefix="winner" # winner:sotck_dkdkdk_dkdk;
# uri = "**********************************************************************************************/,**********************************************************************************************/,**********************************************************************************************/"
# on_off = false

[messagecenter]
vhost = "flower"
exchanger = "notification_center"
#key = "msg.order.*,msg.exec.*,mc.assets.*,mc.aka.*,mc.positions.*,stock_info_change.*,exchange_rate_change,account_info_change.*,unit_stock_margin_change,stock_suspension" #多个key，通过英文逗>号分割，全部订阅，用"*"
key = "notification.order.exec.*,notification.assets.#,notification.aka.#,notification.settlement.*,notification.dvd_settle.*,notification.rq_notify"
userclientkey = "notification.accountrisk.client.asseets.*,notification.accountrisk.client.positions.*,notification.accountrisk.client.marginrate"    #推送用户消息

[quotation]
vhost = "%2f"
# amqpaddr = "amqp://pp:pp123@***********:5672" #"amqp://cc:<EMAIL>:55520/%2f"  #"amqp://pp:<EMAIL>:5672/%2f" #
exchanger = "StockLive"
