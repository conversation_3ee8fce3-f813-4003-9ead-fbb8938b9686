# 系统风控配置文件

[database]
customer_uri = "mysql://root:29481aaa61a8bc76@***********:13301/flower_stock_customer"
finances_uri = "mysql://root:29481aaa61a8bc76@***********:13301/flower_stock_finances_new"
# stock_uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock"

[application]
apphost = "0.0.0.0"
appport = 7402

[system]
# adminserver = "http://finances.metestsvr.com/"
# wtserver = "finances.metestsvr.com:50072"
# # cacheshort = 3600                              #单位秒
# # cachelong = 3600                               #单位秒
# persist_interval = 3600                        #数据保存时间间隔，单位秒
# account_total = "1000001"                      #总账户的unit_id
# basic_currency = "HKD"                         #基币
# logserver = "http://************:8420"
default_margin_rate = 3.0
loglevel = "info"

[notification]
vhost = "flower"
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "notification_center"
queue_name = "phoenix_akacenter_queue_develop"
router_key = "notification.aka.#"

[redis]
# uri = "**********************************************************************************************/,**********************************************************************************************/,**********************************************************************************************/"
on_off = true
