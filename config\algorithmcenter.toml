# 算法单配置文件

# [database]
# uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock"

[application]
apphost = "0.0.0.0"
appport = 7400
machineid=1
nodeid=7400

[system]
logserver = "http://***************:50090"
orderserver = "http://**************:8405"
hqcenterserver= "http://**************:8416"
mangerunitserver= "http://**************:8302"
unitrisk_interval_start="9:30:00"
loglevel = "info"

# akaserver = "finances.metestsvr.com:8998"
# cacheshort = 100000                              #单位秒
# cachelong = 3600                               #单位秒
# persist_interval = 3600                        #数据保存时间间隔，单位秒
# account_total = "1000001"                      #总账户的unit_id
# basic_currency = "USD"                         #基币

# 消息中心
[notification]
# addr = "amqp://pp:<EMAIL>:5672/%2f"
vhost = "flower"
exchanger = "notification_center"
#key = "msg.order.*,msg.exec.*,mc.assets.*,mc.aka.*,mc.positions.*,stock_info_change.*,exchange_rate_change,account_info_change.*,unit_stock_margin_change,stock_suspension" #多个key，通过英文逗号分割，全部订阅，用"*"
router_key="notification.order.exec.*,notification.assets.#,notification.aka.#,notification.settlement.*"
userclientkey="notification.accountrisk.client.asseets.*,notification.accountrisk.client.positions.*,notification.accountrisk.client.marginrate"  #推送用户消息


# [redis]
# # 在common文件中
# #The URL format is redis://[<username>][:<password>@]<hostname>[:port][/<db>]
# uri = "redis://:<EMAIL>:7000/,redis://:<EMAIL>:7001/,redis://:<EMAIL>:7002/,redis://:<EMAIL>:7003/,redis://:<EMAIL>:7004/,redis://:<EMAIL>:7005/"

[quotation]
vhost = "%2f"
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f" # "amqp://pp:<EMAIL>:5672/%2f" #"amqp://pp:<EMAIL>:5672/%2f" #
exchanger = "StockLive"