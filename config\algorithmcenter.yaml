# log4rs.yaml
# d, data 日期, 默认为 ISO 9601 格式, 可以通过 {d(%Y-%m-%d %H:%M:%S.%f)} 这种方式改变日期格
# 检查配置文件变动的时间间隔
refresh_rate: 30 seconds
# appender 负责将日志收集到控制台或文件, 可配置多个
appenders:
  stdout:
    kind: console
    encoder:
      # pattern: "{d} {l} {t} {L} - {m}{n}"
      pattern: "{d(%Y-%m-%d %H:%M:%S.%6f)} {l} {t} {L} — {m}{n}"
  # errorlog:
  #   kind: rolling_file
  #   path: "log/error.log"
  #   encoder:
  #     # log 信息模式
  #     #pattern: "{d} - {m}{n}"
  #     # pattern: "{d(%+)(local)} {h({l})} {M}:{m}{n}"
  #     pattern: "{d(%Y-%m-%d %H:%M:%S.%6f)} {l} {t} {L} — {m}{n}"
  #   policy:
  #     kind: compound
  #     trigger:
  #       kind: size
  #       limit: 100 mb
  #     roller:
  #       kind: fixed_window
  #       pattern: "log/error.log.{}"
  #       base: 1
  #       count: 5
  dblog:
    kind: rolling_file
    path: "log/algorithmcenter_dblog.log"
    encoder:
      # log 信息模式
      #pattern: "{d} - {m}{n}"
      # pattern: "{d(%+)(local)} {h({l})} {M}:{m}{n}"
      pattern: "{d(%Y-%m-%d %H:%M:%S.%6f)} {l} {t} {L} — {m}{n}"
    policy:
      kind: compound
      trigger:
        kind: size
        limit: 100 mb
      roller:
        kind: fixed_window
        pattern: "log/algorithmcenter_dblog.log.{}"
        base: 1
        count: 5
  msglog:
    kind: rolling_file
    encoder:
      # pattern: "{d} {l} {t} {L} - {m}{n}" #"{d} {l} {t}  {L} - {m}{n}"
      pattern: "{d(%Y-%m-%d %H:%M:%S.%6f)} {l} {t} {L} — {m}{n}"
    path: "log/algorithmcenter_msglog.log"
    policy:
      kind: compound
      trigger:
        kind: size
        limit: 100 mb
      roller:
        kind: fixed_window
        pattern: "log/algorithmcenter_msglog.log.{}"
        base: 1
        count: 5
  roll: #定义rooling_file的appenders
    kind: rolling_file
    encoder:
      # pattern: "{d(%Y-%m-%d %H:%M:%S)} | {({l}):5.5} | {f}:{L} — {m}{n}"
      pattern: "{d(%Y-%m-%d %H:%M:%S.%6f)} {l} {t} {L} — {m}{n}"
    path: log/algorithmcenter.log
    policy:
      kind: compound
      trigger:
        kind: size
        limit: 100 mb
      roller:
        kind: fixed_window
        pattern: "log/algorithmcenter.log.{}"
        base: 1
        count: 5
# 对全局 log 进行配置
root:
  level: info
  appenders:
    - stdout
    - roll
loggers:
  sqlx:
    level: info
    appenders:
      - dblog
    additive: false
  messagecenter:
    level: info
    appenders:
      - msglog
    additive: false
