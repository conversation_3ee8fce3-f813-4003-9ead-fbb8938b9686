
# [database]
# uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock"

[application]
apphost = "127.0.0.1"
appport = 7403
machineid = 1
nodeid = 7403

[system]
akaserver = "http://**************:8402"
# cachetime = 3000                                 #缓存时间，单位秒
# accountriskserver = "http://**************:8401" #风控
log_server = "http://***************:50090" #日志
special_lion_unitid = 0
loglevel = "info"

# [redis]
# #redis集群，通过逗号分隔
# #The URL format is redis://[<username>][:<password>@]<hostname>[:port][/<db>]
# uri = "redis://:pl9rz1L0hbOoacesE3Jh@**************:7000/,****************************************************************************************************/,redis://:pl9rz1L0hbOoacesE3Jh@**************:7003/,****************************************************************************************************/" 
# #uri = "****************************************************************************************************"

[msgcenter]
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
vhost = "flower"
assets_exchange = "notification_center"
routing_key = ""                             # 订阅的key
settlement_key = "notification.settlement.*"
