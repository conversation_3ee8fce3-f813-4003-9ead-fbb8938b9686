[database]
stock_uri = "mysql://root:29481aaa61a8bc76@***********:13301/flower_stock"
customer_uri = "mysql://root:29481aaa61a8bc76@***********:13301/flower_stock_customer"
finances_uri = "mysql://root:29481aaa61a8bc76@***********:13301/flower_stock_finances_new"

[redis]
prefix = "flower"                                                                                                                                                                                                                                                                                       # winner:sotck_dkdkdk_dkdk;
uri = "**********************************************************************************************/,redis://:pl9rz1L0hbOoacesE3Jh@***********:8002/,redis://:pl9rz1L0hbOoacesE3Jh@***********:8003/,**********************************************************************************************/"
on_off = false

[mq]
amqpaddr = "amqp://pp:pp123@***********:5672/"


[notification]
vhost = "flower"
notification_exchanger = "notification_center"
accountriskcenter_routing_key = "notification.order.exec.*,notification.assets.#,notification.aka.#,notification.settlement.*,notification.dvd_settle.*,notification.rq_notify"
akacenter_routing_key = "notification.aka.#"
algorithmcenter_routing_key = "notification.order.exec.*,notification.assets.#,notification.aka.#,notification.settlement.*"
assetscenter_routing_key = "notification.settlement.*,notification.aka.#"
exchanger_routing_key = "notification.aka.#"
matchserver_routing_key = "notification.assets.positions.*"
ordercenter_routing_key = "notification.aka.#,notification.settlement.*,notification.order.status.*,notification.order.exec.*,notification.order.info.*"
riskcenter_routing_key = "notification.order.exec.*,notification.assets.#,notification.aka.#,notification.settlement.*"
rustomsgateway_routing_key = "notification.order.info.*,notification.accountrisk.client.marginrate,notification.assets.data.*,notification.rq_notify,notification.order.status.*,notification.accountrisk.client.asseets.*,notification.assets.#"
satcenter_routing_key = "notification.settlement.#"
settlecenter_routing_key = "notification.aka.#,notification.settlement.#"

[quotation]
vhost = "%2f"
stocklive_exchanger = "StockLive"
stockdelay_exchanger = "Stock"

[servers]
accountriskserver = "http://************:7401"
akacenterserver = "http://************:7402"
algorithmcenterserver = "http://************:7427"
assetscenterserver = "http://************:7403"
managerserver = "http://************:7305"
matchserver = "http://************:7701"
hqcenterserver = "http://************:8416"
ordercenterserver = "http://************:7405"
orderrouterserver = "http://************:7406"
riskcenterserver = "http://************:7407"
satcenterserver = "http://************:7603"
settlecenterserver = "http://************:7408"
logcenterserver = "http://************:8420"
