# 系统风控配置文件

[system]

channel_id = 16 #通道id(柜台通道)

akacenterserver = "http://************:7402"
logserver = "http://************:8420"
node_id = 8404
machine_id = 1
loglevel = "info"

# 行情
[quotation]
vhost = "%2f"
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "StockLive"

# 消息中心
[notification]
vhost = "flower"
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
# amqpaddr = "amqp://pp:pp123@***********:5672"
exchanger = "notification_center"

# [redis]
# uri = "**********************************************************************************************/,**********************************************************************************************/,**********************************************************************************************/"

# 消息中心
[grpcclient]
orderrouterclient = "http://************:7406"

[quotaiontime.exchange]
XSHG = ["09:15:00-11:31:00", "13:00:00-15:01:00"] #上交所
XSHE = ["09:15:00-11:31:00", "13:00:00-15:01:00"] #深交所
HS = ["09:15:00-11:31:00", "13:00:00-15:01:00"]   #A股板块
# XSHG = "09:30:00-11:30:00,13:00:00-15:00:00"        #A股指数
# XSHE = "09:30:00-11:30:00,13:00:00-15:00:00"        #A股指数
XHKG = ["09:00:00-12:01:00", "13:00:00-16:10:00"] #港交所
HK = ["09:00:00-12:01:00", "13:00:00-16:10:00"]   #港股板块
# SE_HKI = "09:30:00-12:00:00,13:00:00-16:00:00"      #港股指数
XASE = ["09:30:00-16:00:00"] #美交所
XNYS = ["09:30:00-16:00:00"] #纽交所
XNAS = ["09:30:00-16:00:00"] #纳斯达克
US = ["09:30:00-16:00:00"]   #美股板块
