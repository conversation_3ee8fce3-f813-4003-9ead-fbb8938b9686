# [database]
# uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock"

[application]
apphost = "0.0.0.0"
appport = 7701
machineid = 14
nodeid = 7701

[system]
loglevel = "info"
# cacheshort = 3600       #单位秒
# cachelong = 3600        #单位秒
# persist_interval = 3600 #数据保存时间间隔，单位秒
# # channelid = 16          #柜台通道

# 消息中心
[grpcserver]
ordercenterserver = "http://**************:8405"
logserver = "http://************:8420"
akaserver = "http://**************:8402"
accountassetsserver = "http://**************:8401"

[messagecenter]
vhost = "flower"
# addr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "notification_center"
key = "notification.assets.positions.*" #多个key，通过英文逗号分割，全部订阅，用"*"
