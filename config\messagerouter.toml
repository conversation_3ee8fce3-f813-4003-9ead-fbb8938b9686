# 系统风控配置文件

#[database]
#uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock"

[application]
apphost = "0.0.0.0"
appport = 8418
machineid = 1
nodeid = 6000

[system]
#adminserver = "http://finances.metestsvr.com/"
cacheshort = 3600       #单位秒
cachelong = 3600        #单位秒
persist_interval = 3600 #数据保存时间间隔，单位秒
loglevel = "info"
#channleid = 123
[grpcserver]
ordercenterserver = "http://**************:8405"
orderrouterserver = "http://**************:8406"
logserver = "http://************:8420"
#account_total = "1000001"                      #总账户的unit_id
#basic_currency = "HKD"                         #基币

## 行情
#[quotation]
#amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
#exchanger = "Stock"
#
## 消息中心
#[notification]
#amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
#exchanger = "oms_msg"
#
#[redis]
#uri = "****************************************************************************************************/,****************************************************************************************************/,****************************************************************************************************/"
