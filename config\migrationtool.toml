# 数据迁移工具配置文件

[database]
dst_uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock_test"
src_uri = "mysql://companytest:<EMAIL>:13301/stock_test"
customer_uri = "mysql://companytest:<EMAIL>:13301/customer"

[application]
apphost = "0.0.0.0"
appport = 12345
machineid=1
nodeid=12345

[system]
logserver = "http://************:8420"
akaserver = "finances.metestsvr.com:8998"
cacheshort = 100000                              #单位秒
cachelong = 3600                               #单位秒
persist_interval = 3600                        #数据保存时间间隔，单位秒
account_total = "1000001"                      #总账户的unit_id
basic_currency = "USD"                         #基币

# 消息中心
[notification]
# dev-data.chinaeast2.cloudapp.chinacloudapi.cn
# uat-stock-data.chinaeast2.cloudapp.chinacloudapi.cn
amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "notification_center"
#queue_name = "fee_setting_queue"
#router_key = "fee_setting_change"

[redis]
#redis集群，通过逗号分隔
#The URL format is redis://[<username>][:<password>@]<hostname>[:port][/<db>]
uri = "redis://:pl9rz1L0hbOoacesE3Jh@**************:7000/,****************************************************************************************************/,redis://:pl9rz1L0hbOoacesE3Jh@**************:7003/,****************************************************************************************************/"
#uri = "****************************************************************************************************"
