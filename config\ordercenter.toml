[application]
ip = "0.0.0.0"
port = 7405
machineid = 1
nodeid = 7405

[system]
cachelong = 60
oppaccount = 200844
loglevel = "info"

[notification]
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
vhost = "flower"
exchanger = "notification_center"
queue = "phoenix_notification_client_queue"
routingkey = "notification.order.status.*,notification.order.exec.*,notification.order.info.*"

[grpcserver]
riskcenter = "http://************:7407"
# orderrouterclient = "http://**********:8887"
orderrouter = "http://************:7406"
# orderrouter = "http://************:8406"
assetscenter = "http://************:7403"
akacenter = "http://************:7402"
logserver = "http://************:7420"
accountriskcenter = "http://************:7401"


[orderexec]
exec_type = 2                  #确认回报Confirm = 1,成交Filled = 2,撤单回报Canceled = 3,下单拒绝Rejected = 4,
order_id = 7261663015807672306 #订单号
exec_qty = 3000                #数量
exec_price = 7.35              #价格
exec_id = ""                   #成交编号
exec_time = ""                 #发生时间  2019-12-19 11:21:19
order_direction = 1            #订单方向  1=Buy, 2=Sell
brk_order_id = ""              #委托确认号
channel_id = 13                #通道id
channel_type = 1               #1 外盘, 2 内盘
memo = ""
