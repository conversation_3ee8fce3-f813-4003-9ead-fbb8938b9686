# 系统风控配置文件

# [database]
# uri = "mysql://companytest:<EMAIL>:13301/stock"

[application]
apphost = "0.0.0.0"
appport = 7407

[system]
# adminserver = "http://uat-stock-ha.chinaeast2.cloudapp.chinacloudapi.cn:89/"
# adminserver = "http://uat-stock.chinaeast2.cloudapp.chinacloudapi.cn:89/"
accountassetsserver = "http://**************:8401"
# cacheshort = 3600                                                         #单位秒
cachelong = 3600                               #单位秒
cacheflag = false                              #缓存开关
hqcenterserver = "http://**************:8416"
akacenterserver = "http://**************:8402"
logserver = "http://**************:8420"
loglevel = "info"

# max_shares = 300000 #单笔最大数量
# max_star_shares = 100000 #科创版单笔最大数量
# min_star_shares = 200

[notification]
vhost = "flower"
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "notification_center"
routing_keys = "notification.aka.#"


# [redis]
# #The URL format is redis://[<username>][:<password>@]<hostname>[:port][/<db>]
# #redis集群，通过逗号分隔
# uri = "**************************************************************************************************/,**************************************************************************************************/,**************************************************************************************************/" 
# # uri = "redis://:<EMAIL>:6379/3"
