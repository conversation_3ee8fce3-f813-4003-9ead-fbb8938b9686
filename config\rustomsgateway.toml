
[application]
apphost = "**********"
tcpport = 8888
wsport = 8889
machineid = 20
nodeid = 8888

[system]
orderserver = "http://**************:8405"
logserver = "http://**************:8420"
akaserver = "http://**************:8402"
cacheshort = 3600 #单位秒
cachelong = 3600 #单位秒
cacheflag = false

[notification]
amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "notification_center"
router_key = "notification.order.info.*,notification.accountrisk.client.marginrate,notification.assets.data.*,notification.rq_notify,notification.order.status.*"
queue_name = "phoenix_rustomsgateway"

[redis]
#The URL format is redis://[<username>][:<password>@]<hostname>[:port][/<db>]
#redis集群，通过逗号分隔
uri = "redis://:<EMAIL>:7000/,redis://:<EMAIL>:7001/,redis://:<EMAIL>:7002/,redis://:<EMAIL>:7003/,redis://:<EMAIL>:7004/,redis://:<EMAIL>:7005/"
# uri = "redis://:<EMAIL>:6379/3"