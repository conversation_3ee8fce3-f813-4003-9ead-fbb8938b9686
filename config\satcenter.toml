# 配置文件

[database]
finances_uri = "mysql://companytest:<EMAIL>:13301/finances_new"
# stock_uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock"


[application]
apphost = "127.0.0.1"
appport = 7603
machineid = 20
nodeid = 7603

[system]
logserver = "http://************:8420"
accountserver = "http://**************:8401"
cacheshort = 3600                            #单位秒
cachelong = 3600                             #单位秒
cacheflag = true
timedtask = 86400                            #单位秒
unitrisk_interval_start = "09:00:00"
unitrisk_interval_start_re_call = "09:05:00"
akaserver = "http://**************:8402"
loglevel = "info"

[notification]
vhost = "flower"
# amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "notification_center"
routing_keys = "notification.settlement.#"
