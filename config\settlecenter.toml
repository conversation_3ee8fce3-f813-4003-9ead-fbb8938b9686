# 系统风控配置文件

# [database]
# uri = "mysql://root:29481aaa61a8bc76@***********:13301/flower_stock"

[application]
apphost = "0.0.0.0"
appport = 7408
machineid = 20
nodeid = 7408

[system]
# assetsserver = "http://5************:7403"
logserver = "http://************:8420"
akaserver = "http://************:7402"
cacheshort = 3600                      #单位秒
cachelong = 3600                       #单位秒
cacheflag = false
loglevel = "info"

[notification]
vhost = "flower"
# amqpaddr = "amqp://pp:pp123@***********:5672"
exchanger = "notification_center"
routing_keys = "mc.exec.*"


# [redis]
# #The URL format is redis://[<username>][:<password>@]<hostname>[:port][/<db>]
# #redis集群，通过逗号分隔
# uri = "redis://:pl9rz1L0hbOoacesE3Jh@***********:8000/,redis://:pl9rz1L0hbOoacesE3Jh@***********:8001/,redis://:pl9rz1L0hbOoacesE3Jh@***********:8002/,redis://:pl9rz1L0hbOoacesE3Jh@***********:8003/,redis://:pl9rz1L0hbOoacesE3Jh@***********:8004/,redis://:pl9rz1L0hbOoacesE3Jh@***********:8005/"
# # uri = "redis://:<EMAIL>:6379/3"
