[application]
apphost = "0.0.0.0"
appport = 8412

[cassandra]
addr = ""
# addr = "dev-data.chinaeast2.cloudapp.chinacloudapi.cn:11092"# addr = "uat-stock-data.chinaeast2.cloudapp.chinacloudapi.cn:11092"username = "uatcass"
username = "uatcass"
password = "uatcass!2"
# addr = "***********:11092"
namespace = "stockhqklinedata_kyc"
# namespace = "stockhqklinedata_kyc"
# namespace = "stockhqklinedata_hk"
# namespace = "test"

[rabbitmq]
hqvhost = "%2f"
msgvhost = "flower"
amqpaddr = "amqp://pp:pp123@***********:5672/"
exchanger_delay = "Stock"
exchanger = "StockLive"
# exchanger = "stock_new_test"

# exchange_type = "topic"
[system]
exchangeno = "HS,XSHE,XSHG"
# quotationserver = "http://**************:50021"#A股
quotationserver = "http://************:8460"
akaserver = "http://************:7402"
logserver = "http://************:8420"
# exchangeno = "HK,XHKG"
# quotationserver = "http://**************:8460"
# quotationserver = "http://**************:50039"#港股

# quotationserver = "http://uat-stock.chinaeast2.cloudapp.chinacloudapi.cn:50050"#美股
cassinterval = 3
channelcap = 20480
filepath = "tickcenter"

[quotaiontime.exchange]
XSHG = ["09:30:00-11:30:00", "13:00:00-15:00:00"] #上交所
XSHE = ["09:30:00-11:30:00", "13:00:00-15:00:00"] #深交所
HS = ["09:30:00-11:30:00", "13:00:00-15:00:00"]   #A股板块
# XSHG = "09:30:00-11:30:00,13:00:00-15:00:00"        #A股指数
# XSHE = "09:30:00-11:30:00,13:00:00-15:00:00"        #A股指数
XHKG = ["09:30:00-12:00:00", "13:00:00-16:00:00"] #港交所
HK = ["09:30:00-12:00:00", "13:00:00-16:00:00"]   #港股板块
# SE_HKI = "09:30:00-12:00:00,13:00:00-16:00:00"      #港股指数
XASE = ["09:30:00-16:00:00"] #美交所
XNYS = ["09:30:00-16:00:00"] #纽交所
XNAS = ["09:30:00-16:00:00"] #纳斯达克
US = ["09:30:00-16:00:00"]   #美股板块
# SE_USI = "09:30:00-16:00:00"                        #美股指数
