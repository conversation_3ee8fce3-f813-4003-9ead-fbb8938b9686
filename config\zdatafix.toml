
[system]
# server = "http://174.10.0.4:7408"
# server = "http://174.10.0.4:7408" #重新结算
server = "http://47.98.144.24:7405" #补当天订单
fixtype = 3                       #修复类型: 1:账户风控  2:重新结算 3:补当天订单

[accountrisk]
[accountrisk.1]
unit_id = 0
cur_date = ********
before_date = ********
rate_buy = 1.073676
rate_sell = 1.069084

# [accountrisk.2]
# unit_id = 0
# cur_date = ********
# before_date = ********
# rate_buy = 1.073676
# rate_sell = 1.069084


[resettle]
# [resettle.1]
# market_type = 0            #市场类别: 1 沪深  2 港股  3 美股 0 全部
# unit_id = 0                #指定账户结算，不指定时默认为0
# sys_date = ********        #结算日期
# next_trade_date = ******** #下一个交易日
# currency = 1               #交易币种 1:hkd 2:cny 3:usd
# buy_rate = 1.076646        #买入汇率
# sell_rate = 1.072029       #卖出汇率

# [resettle.2]
# market_type = 0            #市场类别: 1 沪深  2 港股  3 美股 0 全部
# unit_id = 0                #指定账户结算，不指定时默认为0
# sys_date = ********        #结算日期
# next_trade_date = ******** #下一个交易日
# currency = 1               #交易币种 1:hkd 2:cny 3:usd
# buy_rate = 1.073676        #买入汇率
# sell_rate = 1.069084       #卖出汇率


[replenishorder.order]
msg_id = ********
unit_id = 41753 # 子账户id
stock_id= 10949 # 证券id
order_direction= 1 # 委托方向  1=买  2=卖
order_qty= 100 # 订单数量
price_type= 2  #价格类型(1:市价 2:限价)
order_price= 9.08 # 委托价格
operator_no= 41753  # 操作员
order_type= 1# 委托类型 1:app下单  2:跟单  3:风控止盈止损平仓单,4:风控总资产预警平仓单 5:pc客户端单 6:结算平仓单 7:管理端强平仓单,8:app清仓,9:pc清仓,10,管理员平仓,11,合约到期日强平,12,算法单
trade_mode= 1# 1:USER(用户直连) 2:AGENT(代理托管)
agent_account = 0 # 代理账户
algorithm_id= 0# 算法单id(普通0)
user_id= 41753 # 用户id

[replenishorder]
subinfo = [{ channel_id = 14, channel_type = 1, order_amount = 100 }]# channel_type 1内盘 2外盘
trade_time = "2025-02-12 14:30:00" # 交易时间
# 撮合必传,补单不用
to_unit_id = 0 # 转入子账户id
to_user_id = 0 # 转入用户id