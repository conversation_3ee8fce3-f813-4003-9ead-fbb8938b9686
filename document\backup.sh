#!/bin/bash

# MySQL credentials
MYSQL_USER="***"
MYSQL_PASSWORD="***"

# database list to be backedup
DATABASES="go_stock go_finances lion_stock lion_finances audit_customer audit_finance audit_stock"

# Remote server details (arrays as "172.26.2.32" "172.26.2.30")
REMOTE_SERVERS=("172.26.2.32")
REMOTE_PORTS=("5312")
REMOTE_USERS=("logserver")
REMOTE_DIRS=("/data/databack")
KEY_FILES=("/home/<USER>/.ssh/id_rsa_logserver")

# MySQL dump options
DUMPOPTIONS=" --quick --add-drop-table --extended-insert --set-gtid-purged=OFF"

# Backup directory
BACKUP_DIR="/data/backup"
DATE=$(date +%Y%m%d_%H%M)
BACKUP_DATE=$(date +%Y%m%d)


# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Create date-specific backup directory
LOCAL_BACKUP_DIR="$BACKUP_DIR/$BACKUP_DATE"
mkdir -p "$LOCAL_BACKUP_DIR"

# Clean up old backups (keep only 30 days)
find "$BACKUP_DIR" -maxdepth 1 -type d -name "????????" -mtime +30 -exec rm -rf {} \;

# Get databases to backup
if [ -z "$DATABASES" ]; then
    echo "DATABASES is empty, getting all databases from MySQL..."
    DATABASES=$(mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e 'SHOW DATABASES;' | grep -Ev "(Database|information_schema|performance_schema|mysql|sys)")
    echo "Found databases: $DATABASES"
else
    echo "Using predefined databases: $DATABASES"
fi

# Loop through each database and back it up
for DB in $DATABASES; 
do

    BACKUP_FILE="$BACKUP_DIR/${DB}_$DATE.sql"
    COMPRESSED_FILE="$LOCAL_BACKUP_DIR/${DB}_$DATE.sql.tar.gz"

    # Dump each database into a separate file
    mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD $DUMPOPTIONS $DB > $BACKUP_FILE

    # Compress the backup file directly to the date directory
    tar -czf $COMPRESSED_FILE -C $BACKUP_DIR "$(basename "$BACKUP_FILE")"

    # Upload to all remote servers
    for i in "${!REMOTE_SERVERS[@]}"; do
        REMOTE_SERVER="${REMOTE_SERVERS[$i]}"
        REMOTE_PORT="${REMOTE_PORTS[$i]}"
        REMOTE_USER="${REMOTE_USERS[$i]}"
        REMOTE_DIR="${REMOTE_DIRS[$i]}"
        KEY_FILE="${KEY_FILES[$i]}"
        
        # Create a batch file for sftp commands
        BATCH_FILE="$BACKUP_DIR/batchsync_${DB}_${i}.txt"
        echo "-mkdir $REMOTE_DIR/$BACKUP_DATE" > "$BATCH_FILE"
        echo "cd $REMOTE_DIR/$BACKUP_DATE" >> "$BATCH_FILE"
        echo "put $COMPRESSED_FILE" >> "$BATCH_FILE"
        echo "exit" >> "$BATCH_FILE"

        # Execute the sftp commands from the batch file
        sftp -o IdentitiesOnly=yes -oPort=$REMOTE_PORT -i $KEY_FILE -b $BATCH_FILE $REMOTE_USER@$REMOTE_SERVER
        
        # Clean up batch file
        rm $BATCH_FILE
    done

    # Clean up
    rm $BACKUP_FILE
done

echo "Backup and upload completed."

