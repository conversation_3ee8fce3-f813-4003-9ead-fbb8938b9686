#!/bin/bash

# 备份 MySQL 全部数据库的脚本
# 适用于 Ubuntu Linux，支持 MySQL/MariaDB

# 配置参数
MYSQL_USER="***"                # MySQL 用户名
MYSQL_PASSWORD="***"   # MySQL 密码（建议使用配置文件以提高安全性）
BACKUP_DIR="/data/backup"       # 备份文件存储目录
DATE=$(date +%Y%m%d_%H%M%S)     # 当前时间戳，格式如 20250730_2048
LOG_FILE="/data/backup/mysql_backup.log"  # 日志文件
COMPRESS="gzip"                  # 压缩工具（可选：bzip2 或其他）

# 确保备份目录存在
mkdir -p "$BACKUP_DIR"
if [ ! -d "$BACKUP_DIR" ]; then
    echo "错误：无法创建备份目录 $BACKUP_DIR" | tee -a "$LOG_FILE"
    exit 1
fi

# 检查 mysqldump 是否可用
if ! command -v mysqldump >/dev/null 2>&1; then
    echo "错误：mysqldump 未安装，请安装 mysql-client" | tee -a "$LOG_FILE"
    exit 1
fi

# 获取所有数据库名称（排除系统数据库）
DATABASES=$(mysql -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SHOW DATABASES;" | grep -Ev "(Database|information_schema|performance_schema|mysql|sys)")

# 检查是否成功获取数据库列表
if [ -z "$DATABASES" ]; then
    echo "错误：无法获取数据库列表，请检查 MySQL 用户名和密码" | tee -a "$LOG_FILE"
    exit 1
fi

# 记录备份开始时间
echo "备份开始时间: $(date)" | tee -a "$LOG_FILE"

# 循环备份每个数据库
for DB in $DATABASES; do
    BACKUP_FILE="$BACKUP_DIR/${DB}_${DATE}.sql"
    COMPRESSED_FILE="${BACKUP_FILE}.gz"

    echo "正在备份数据库: $DB 到 $BACKUP_FILE" | tee -a "$LOG_FILE"

    # 使用 mysqldump 备份数据库
    mysqldump -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" --databases "$DB" > "$BACKUP_FILE" 2>> "$LOG_FILE"
    
    # 检查备份是否成功
    if [ $? -eq 0 ]; then
        echo "数据库 $DB 备份成功" | tee -a "$LOG_FILE"
        
        # 压缩备份文件
        $COMPRESS "$BACKUP_FILE"
        if [ $? -eq 0 ]; then
            echo "压缩 $BACKUP_FILE 为 $COMPRESSED_FILE 成功" | tee -a "$LOG_FILE"
        else
            echo "警告：压缩 $BACKUP_FILE 失败" | tee -a "$LOG_FILE"
        fi
    else
        echo "错误：数据库 $DB 备份失败" | tee -a "$LOG_FILE"
    fi
done

# 清理旧备份（可选，保留最近 7 天的备份）
#find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete
#echo "已清理超过 7 天的备份文件" | tee -a "$LOG_FILE"

# 记录备份结束时间
echo "备份结束时间: $(date)" | tee -a "$LOG_FILE"
echo "----------------------------------------" | tee -a "$LOG_FILE"

exit 0