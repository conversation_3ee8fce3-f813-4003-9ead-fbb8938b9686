-- tfeeset需要先复制到新库
INSERT INTO phoenix_oms_feeset(
fee_type, exchange_id, capital_ratio, face_value_ratio, amount_ratio, maximum_fee, minimum_fee, currency_no, modify_date, order_direction, unit_id, serfee_type, close_type, stock_type, decimal_type, channel_id, plate_id, tid) 
SELECT c_fee_type, l_exchange_id, en_capital_ratio, en_face_value_ratio, en_amount_ratio, en_maximum_fee, en_minimum_fee, vc_currency_no, l_modify_date, CASE WHEN c_order_direction='!' THEN 0 ELSE c_order_direction end, l_unit_id, c_serfee_type, 
CASE WHEN c_close_type='' then 0 else c_close_type end as close_type, c_stock_type, c_decimal_type, l_channel_id, l_plate_id, l_tid 
FROM tfeeset;
