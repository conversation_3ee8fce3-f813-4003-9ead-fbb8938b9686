ALTER TABLE `finances_new`.`sys_commodity_channel` 
ADD COLUMN `max_hold` varchar(32) NOT NULL DEFAULT '' COMMENT '用户最大持仓量',
ADD COLUMN `min_hold` varchar(32) NOT NULL DEFAULT '' COMMENT '用户最小持仓量';


用保证金体系现有的表结构及数据
sys_menus 
sys_trade_channel


sys_trade_config_channel 需要初始化卖方向的初始数据
INSERT into sys_trade_config_channel (trade_config_id,trade_channel_id,`level`,user_id,state,update_time,direction) SELECT trade_config_id,trade_channel_id,`level`,user_id,state,update_time,2 from sys_trade_config_channel_copy1;

然后需要手动调整下顺序




UPDATE sys_commodity_ext set bond_wc=0.25;  初始化所有的品种的维持保证金25%

INSERT INTO `finances_new`.`sys_config`( `config_code`, `config_content`, `create_time`, `can_modify`, `visible_state`, `mark`, `config_group`, `business_type`, `ext1`, `ext2`, `modify_time`) VALUES ( 'change_user_status_notify_email', '<EMAIL>', 0, 0, 1, '预警通知的运营账号配置', '', 0, '', '', 0);
INSERT INTO `finances_new`.`sys_config`( `config_code`, `config_content`, `create_time`, `can_modify`, `visible_state`, `mark`, `config_group`, `business_type`, `ext1`, `ext2`, `modify_time`) VALUES ( 'channel_user_config2', '40584', 0, 0, 1, '', '', 0, '', '', 0);

INSERT INTO `finances_new`.`sys_sequence`(`id`, `sequence_id`, `sequence_type`) VALUES (8, 100000, '算法单编号');

ALTER TABLE `finances_new`.`sys_stock_suspension_record` 
ADD COLUMN `margin_rate_increment` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '保证金停牌增量';


CREATE TABLE `users_algorithm_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `algorithm_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1:twap,2:vwap,3:trigger',
  `stock_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '品种id',
  `start_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `price_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '价格类型，1：限价，2：市价',
  `price_num` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '价格/市价  1：对手一档，2：对手二挡',
  `order_num` int(11) NOT NULL DEFAULT '0' COMMENT '订单数量',
  `order_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1:买，2：卖',
  `algorithm_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1:待执行，2：执行中，3：已结束',
  `entrust_num` int(11) NOT NULL DEFAULT '0' COMMENT '已委托数量',
  `deal_num` int(11) NOT NULL DEFAULT '0' COMMENT '已成交数量',
  `mark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `trigger_condition` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT 'trigger类型时，条件的触发条件，>= 等',
  `trigger_price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT 'trigger类型时，触发价格',
  `uid` bigint(20) NOT NULL DEFAULT '0' COMMENT '唯一id',
  `interval_time` int(11) NOT NULL DEFAULT '0' COMMENT '间隔时间',
  `last_execute_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '上一次执行时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算法单配置';




ALTER TABLE `finances_new`.`users_rq` 
ADD COLUMN `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
ADD COLUMN `start_date` int(11) NOT NULL DEFAULT 0 COMMENT '有效期，开始日期，格式20230324',
ADD COLUMN `use_num` int(11) NOT NULL DEFAULT 0 COMMENT '已用额度',
ADD COLUMN `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '融券状态。0：未生效，1：生效，-1：失效',
ADD COLUMN `mark` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注' ;

ALTER TABLE `finances_new`.`users_wallet_cash_flow` 
MODIFY COLUMN `check_mark2` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '复核意见' AFTER `tnterest`;




ALTER TABLE `customer`.`users_trade_account` 
ADD COLUMN `rq_state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1:有融券权限，0：没有';
ALTER TABLE `customer`.`users_trade_account` 
ADD COLUMN `rq_level` int(11) NOT NULL DEFAULT 0 COMMENT '融券优先级，越小优先级越高';
ALTER TABLE `customer`.`users_trade_account` 
ADD COLUMN `algorithm_state` tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否开通算法单权限，1：开通，2：不开通';


CREATE TABLE `users_annual_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `real_name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_id` bigint(20) NOT NULL DEFAULT '0',
  `create_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `submit_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '提交申请时间',
  `check_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '审核时间',
  `status` tinyint(255) NOT NULL DEFAULT '0' COMMENT '状态，1：待更新，2：待审核，3：审核成功，4：审核拒绝',
  `check_mark` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `source_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '提交来源，pc,app',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=279 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户年审记录';


CREATE TABLE `users_annual_review_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `reviem_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '年审表id',
  `data` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'json数据',
  `data_type` int(11) NOT NULL COMMENT '数据类型,\r\n1、users表\r\n2、users_extend表\r\n3、users_file\r\n4、users_questionnaire\r\n5、users_questionnaire_answer\r\n6、users_bank',
  `create_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4324 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='年审相关数据表';


CREATE TABLE `sys_closure_topic_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic_type` int(11) DEFAULT NULL COMMENT '题目类别,对应销户原因',
  `topic_type_name` varchar(255) DEFAULT '' COMMENT '类别名称',
  `topic_type_name_en` varchar(255) DEFAULT NULL COMMENT '类别名称英文',
  `has_responsedays` tinyint(1) DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1:启用, 0:禁用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;


INSERT INTO `customer`.`sys_closure_topic_type`(`id`, `topic_type`, `topic_type_name`, `topic_type_name_en`, `has_responsedays`, `status`) VALUES (1, 1, '未结案的筛查命中', 'Unresolved Screening Hits', 1, 1);
INSERT INTO `customer`.`sys_closure_topic_type`(`id`, `topic_type`, `topic_type_name`, `topic_type_name_en`, `has_responsedays`, `status`) VALUES (2, 2, '不合规的账户', 'Non-compliant Accounts', 1, 1);
INSERT INTO `customer`.`sys_closure_topic_type`(`id`, `topic_type`, `topic_type_name`, `topic_type_name_en`, `has_responsedays`, `status`) VALUES (3, 3, '客户申请销户', 'Requested by Client', 2, 1);


https://stc2.lionky.com/FinanceAPI/inner/redis/initData  //初始化redis缓存数据


https://stc.lionky.com/FinanceAPI/util/updateEquiteToNewSystem?token=6a55c0ee37f9492ba0c4b9c307db46ed  //同步权益分派记录



