
第一步：导入finance_new库的所有表
第二步：删除以下无用表
DROP TABLE IF EXISTS commission;
DROP TABLE IF EXISTS commission_crmgroup;
DROP TABLE IF EXISTS commission_exchange;
DROP TABLE IF EXISTS commission_list;
DROP TABLE IF EXISTS commission_rule;
DROP TABLE IF EXISTS commission_rule_list;
DROP TABLE IF EXISTS commission_rule_temp;
DROP TABLE IF EXISTS commission_settle;
DROP TABLE IF EXISTS commission_settle_info;
DROP TABLE IF EXISTS commission_trades;
DROP TABLE IF EXISTS commission_unusual_log;
DROP TABLE IF EXISTS commission_users;
DROP TABLE IF EXISTS en_base_info;
DROP TABLE IF EXISTS en_dictionary_info;
DROP TABLE IF EXISTS en_user_info;
DROP TABLE IF EXISTS MT4_CONFIG;
DROP TABLE IF EXISTS MT4_PRICES;
DROP TABLE IF EXISTS MT4_TRADES;
DROP TABLE IF EXISTS MT4_TRADES_TEMP;
DROP TABLE IF EXISTS MT4_USERS;
DROP TABLE IF EXISTS stock_crush;
DROP TABLE IF EXISTS sys_account_type;
DROP TABLE IF EXISTS sys_channel_config;
DROP TABLE IF EXISTS sys_market_channel;
DROP TABLE IF EXISTS sys_questionnaire;
DROP TABLE IF EXISTS sys_symbols;
DROP TABLE IF EXISTS sys_topic;
DROP TABLE IF EXISTS sys_topic_answer;
DROP TABLE IF EXISTS tmt4tradedetail;
DROP TABLE IF EXISTS tmt4userposition;
DROP TABLE IF EXISTS trade_risk_list;
DROP TABLE IF EXISTS trade_risk_list_detail;
DROP TABLE IF EXISTS users_agent;
DROP TABLE IF EXISTS users_agent_trading;
DROP TABLE IF EXISTS users_bank;
DROP TABLE IF EXISTS users_consultation;
DROP TABLE IF EXISTS users_exam;
DROP TABLE IF EXISTS users_friend_authentication;
DROP TABLE IF EXISTS users_futures_config;
DROP TABLE IF EXISTS users_grada_fail;
DROP TABLE IF EXISTS users_grada_log;
DROP TABLE IF EXISTS users_level_config;
DROP TABLE IF EXISTS users_level_config_list;
DROP TABLE IF EXISTS users_level_goods;
DROP TABLE IF EXISTS users_questionnaire;
DROP TABLE IF EXISTS users_questionnaire_answer;
DROP TABLE IF EXISTS users_rate_blance;
DROP TABLE IF EXISTS users_revisit;
DROP TABLE IF EXISTS users_sale_team;
DROP TABLE IF EXISTS users_sale_team_member;
DROP TABLE IF EXISTS users_subscribe;
DROP TABLE IF EXISTS users_trade_account;
DROP TABLE IF EXISTS users_trade_account_config;
第三步：导入customer库所有表
第四不：改以下表的表名
ALTER TABLE sys_area RENAME cus_sys_area;
ALTER TABLE sys_bank RENAME cus_sys_bank;
ALTER TABLE sys_config RENAME cus_sys_config;
ALTER TABLE sys_dictionary RENAME cus_sys_dictionary;
ALTER TABLE sys_sequence RENAME cus_sys_sequence;
ALTER TABLE sys_tips_language RENAME cus_sys_tips_language;
ALTER TABLE users RENAME cus_users;
ALTER TABLE users_apply_record RENAME cus_users_apply_record;
ALTER TABLE users_extend_info RENAME cus_users_extend_info;
ALTER TABLE users_file RENAME cus_users_file;
ALTER TABLE users_login_log RENAME cus_users_login_log;
ALTER TABLE users_wallet RENAME cus_users_wallet;
第五步从finances_new 重新导入以上几个被覆盖的表，也就是改表名的表
第六步从notify库导入所有表
第七步从setcenter库导入表。
这样就所有表合到一起了
