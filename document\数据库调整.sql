-- 2025-01-15 

-- 增加通道属性
--finace-_new
ALTER TABLE `sys_trade_channel` ADD COLUMN `channel_attr` int(4) NOT NULL DEFAULT 1 COMMENT '内盘通道属性 1:柜台 2：兜底' AFTER `unit_id`;
INSERT INTO `sys_menus`(`id`, `menu_url`, `menu_sort`, `menu_mark`, `menu_status`, `parent_id`, `menu_icon`, `menu_name`, `menu_type`, `menu_code`, `system_name`, `menu_path`, `type`, `menu_type_new`, `component`) VALUES (1078, '/tradeManagement/stock/stockPool', 1, '融券券池', 1, 809, '', '融券券池', 1, '', 'admin,agent', 'tradeManagement/stockPool', 'menu', 'tab', '/src/views/backend/tradeManagement/stockPool/index.vue');
INSERT INTO `sys_menus`(`id`, `menu_url`, `menu_sort`, `menu_mark`, `menu_status`, `parent_id`, `menu_icon`, `menu_name`, `menu_type`, `menu_code`, `system_name`, `menu_path`, `type`, `menu_type_new`, `component`) VALUES (1079, '', 1, '查询', 1, 1078, '', '查询', 0, '', 'admin', NULL, 'button', NULL, NULL);
INSERT INTO `sys_menus`(`id`, `menu_url`, `menu_sort`, `menu_mark`, `menu_status`, `parent_id`, `menu_icon`, `menu_name`, `menu_type`, `menu_code`, `system_name`, `menu_path`, `type`, `menu_type_new`, `component`) VALUES (1080, '', 2, '重置', 1, 1078, '', '重置', 0, '', 'admin', NULL, 'button', NULL, NULL);
INSERT INTO `sys_menus`(`id`, `menu_url`, `menu_sort`, `menu_mark`, `menu_status`, `parent_id`, `menu_icon`, `menu_name`, `menu_type`, `menu_code`, `system_name`, `menu_path`, `type`, `menu_type_new`, `component`) VALUES (1081, '', 3, '导入', 1, 1078, '', '导入', 0, '', 'admin', '', 'button', '', '');
INSERT INTO `sys_menus`(`id`, `menu_url`, `menu_sort`, `menu_mark`, `menu_status`, `parent_id`, `menu_icon`, `menu_name`, `menu_type`, `menu_code`, `system_name`, `menu_path`, `type`, `menu_type_new`, `component`) VALUES (1082, '', 4, '外部券池模板', 1, 1078, '', '外部券池模板', 0, '', 'admin', '', 'button', '', '');
INSERT INTO `sys_menus`(`id`, `menu_url`, `menu_sort`, `menu_mark`, `menu_status`, `parent_id`, `menu_icon`, `menu_name`, `menu_type`, `menu_code`, `system_name`, `menu_path`, `type`, `menu_type_new`, `component`) VALUES (1083, '', 5, '导出', 1, 1078, '', '导出', 0, '', 'admin', '', 'button', '', '');



CREATE TABLE `users_agent` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL DEFAULT '0' COMMENT '机构id',
  `agent_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '机构交易名称',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '机构交易验证密码',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态：0可交易 1禁止交易',
  `create_date` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `agent_contact` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '机构联系人',
  `agent_email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '机构邮箱',
  `agent_mail` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '机构联系电话',
  `comp_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '对象id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





CREATE TABLE `users_agent_trading` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `unit_id` int(11) NOT NULL COMMENT '交易用户id',
  `agent_id` int(11) NOT NULL COMMENT '交易用户关联的机构账户id',
  `status` int(11) NOT NULL COMMENT '交易账户交易状态 0新建1正常交易2禁止交易',
  `created_at` bigint(20) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



--customer
ALTER TABLE `users_trade_account` 
ADD COLUMN `rq_account_type` int(11) NOT NULL COMMENT '0：默认（多空），1：做空账户，2：做多账户';

--stock
ALTER TABLE `phoenix_stockposition_channel` 
ADD COLUMN `p_rq_type` int(11) NOT NULL COMMENT '券池类型，1：外部券池，0：默认内部券池';

ALTER TABLE `phoenix_stockposition_channel_his` 
ADD COLUMN `p_rq_type` int(11) NOT NULL COMMENT '券池类型，1：外部券池，0：默认内部券池';





-- 狮子通道发表，狮子新增表
CREATE TABLE `phoenix_ord_stockorder_ext` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '本系统订单编号',
  `ext_order_id` varchar(32) NOT NULL DEFAULT '' COMMENT '外部订单编号',
  `unit_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '所属客户id',
  `create_date` bigint(20) NOT NULL COMMENT '创建时间',
  `clientid` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=327 DEFAULT CHARSET=utf8mb4;






--共赢期权表变更

ALTER TABLE `users_option` 
ADD COLUMN `otype` int(11) NOT NULL DEFAULT 0 COMMENT '期权类型：0：默认期权，1：共赢期权',
ADD COLUMN `proft_rate` decimal(16, 4) NOT NULL DEFAULT 0.0000 COMMENT '共赢期权，收益比例';


ALTER TABLE `users_option_his` 
ADD COLUMN `otype` int(11) NOT NULL DEFAULT 0 COMMENT '期权类型：0：默认期权，1：共赢期权',
ADD COLUMN `proft_rate` decimal(16, 4) NOT NULL DEFAULT 0.0000 COMMENT '共赢期权，收益比例';