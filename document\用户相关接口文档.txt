1、客户列表查询
/FinanceAPI/customerList/showList

入参：
businessType
name：姓名
idNumber ：证件号
tradeAccount ：交易账号
tradeAccountStatus ：账号状态
updateState  客户资料更新状态
examineTimeBegin 审核通过开始时间
examineTimeEnd 审核通过结束
pep
aml
gradaRiskLevel
gradaComplate
emidState
emid
sellerName
sellerAccount
agentName
agentAccount
pageNo
pageSize
token


返回：

{
  "code": "0",
  "count": 446,
  "data": [
    {
      "id": "42543",
      "realName": "孙赛赛",
      "male": "男",
      "cartType": "身份证",
      "idNumber": "*****",
      "examineTime": "2025-05-16 14:25:27",
      "riskMatchingStatus": "否",
      "ifUpdate": "未更新",
      "lastCertResult": "",
      "currentCertResult": "",
      "futuresAccount": null,
      "derivativesAccount": null,
      "securityAccount": [
        {
          "businesstype": "3",
          "businesstypename": "证券",
          "tradeaccount": "********",
          "accounttype": "5",
          "accounttypename": "证券账户",
          "tradestatus": "1",
          "tradestatusname": "正常交易",
          "customergroup": "",
          "agent": "(********)",
          "crmgroup": "默认佣金组",
          "algorithmOpenState": 0
        }
      ],
      "isShowInfo": "3",
      "statusName": "正常",
      "status": 1,
      "mobileType": "86",
      "cartTypeId": 1,
      "sellerID": "",
      "businessType": "3",
      "accountNo": "",
      "algorithmOpenState": "",
      "aMLMatch": "",
      "pepMatch": "",
      "gradaRiskLevel": "",
      "gradaComplate": "未完成",
      "lastTradeTime": "",
      "lastUploadTime": "",
      "pushState": 0,
      "pushStateStr": "",
      "account": ""
    }
  ],
  "msg": "成功"
}








2、客户审核列表

/FinanceAPI/customerExam/showList

参数：
businessType
name
idNumber
mobile
mail
examState
compCert
applyTimeBegin
applyTimeEnd
pageNo
pageSize
usercate
token

返回：


{
  "code": "0",
  "count": 73,
  "data": [
    {
      "businessType": "证券",
      "id": "41806",
      "examID": 17284,
      "realName": "周凯慧",
      "male": "男",
      "cartType": "身份证",
      "idNumber": "*****",
      "examState": "审核成功",
      "checkstate": "",
      "applyTime": "2025-05-14 09:39:23",
      "certState": "未认证",
      "isShowInfo": "3",
      "canCheck": "0",
      "usercate": 3
    }
  ],
  "msg": "成功"
}







3、详情页  客户基本信息

FinanceAPI/customerManageDetail/baseInfoQuery

入参：
userID：41806
queryMode：detail
queryTab：baseinfo
isAddPasswd：1
token：195d2af01e2d477bb9e85d2499343fc6



返回：

{
  "code": "0",
  "data": {
    "userName": "周凯慧",
    "zhname": "周凯慧",
    "userSex": "男",
    "userBirthday": "1991-02-05",
    "userMobile": "(+86)***********",
    "userMail": "<EMAIL>",
    "userCardType": "身份证",
    "userIDCard": "330726199102050017",
    "userCardBeginDate": "2017-11-28",
    "userCardEndDate": "2037-11-28",
    "userCardAddress": "上海市浦东新区花木镇梅花路289号",
    "userPostalAddress": "中国大陆-上海-浦东新-康安路 567 弄 7 单元 201",
    "businessType": "",
    "remark": "",
    "examStatus": "",
    "businessTypeInfo": null,
    "isShowCompcert": false,
    "sarStatus": "",
    "gradaAML": "",
    "gradaPEP": "",
    "gradaResult": "",
    "gradaRiskLevel": 0,
    "gradaStatus": "",
    "idStatus": "",
    "sign_org": "上海市公安局浦东分局",
    "nation": "1",
    "nationName": "",
    "isAddPasswd": "",
    "birthplace": "浙江省金华市浦江县",
    "similarityPercent": 0,
    "lastUploadTime": "",
    "livecate": 0,
    "openSource": "PC端",
    "pushState": 0,
    "account_status": 1
  },
  "msg": "成功"
}


4、详情页 银行卡信息

customerManageDetail/baseInfoQuery

入参：

userID：41806
queryMode：detail
queryTab：bankcard
isAddPasswd：1
token：195d2af01e2d477bb9e85d2499343fc6


返回：


{
  "code": "0",
  "data": [
    {
      "id": 1001700,
      "userId": 41806,
      "cardNo": "6222620110035515395",
      "bankCode": "BOCOM",
      "bankName": "交通银行",
      "bankProvince": "上海",
      "bankCity": "浦东新",
      "bankBranchName": "期货大厦支行",
      "createTime": 0,
      "bankSort": 0,
      "bankType": "",
      "bankCountry": "1",
      "status": 1
    }
  ],
  "msg": "成功"
}



5、详情页-解绑银行卡

/FinanceAPI/users/queryUserUnbindCard


入参

userid：41806
lang
isAddPasswd：1
token


返回：

{
  "code": "0",
  "data": [],
  "msg": "成功"
}




6、详情页-账户详情

/FinanceAPI/customerManageDetail/baseInfoQuery

入参：

userID：41806
queryMode：detail
queryTab:account
token

返回：
{
  "code": "0",
  "data": {
    "futuresAccount": {
      "tradeAccountInfo": {
        "tradeAccount": "",
        "accountType": "",
        "orderCode": "",
        "tradeState": "",
        "tradeStatusName": "",
        "agentName": "",
        "agentAccount": ""
      },
      "totalIn": "",
      "totalOut": "",
      "serviceName": "",
      "serviceAccount": "",
      "agentName": "",
      "agentAccount": "",
      "memberName": "",
      "memberAccount": "",
      "sellerName": "",
      "sellerAccount": "",
      "loginControl": ""
    },
    "derivativesAccount": {
      "visible": "",
      "sellerName": "",
      "sellerID": "",
      "userRole": "",
      "isCreateAgentAccount": false,
      "userAccountControl": {
        "accountStatus": "",
        "outPutAdversionMoney": ""
      },
      "accountList": null
    },
    "securityAccont": {
      "securityList": {
        "id": ********,
        "userId": 41806,
        "tradeType": 3,
        "tradeAccount": "********",
        "accountType": "证券账户",
        "orderCode": "",
        "tradeState": "1",
        "tradeStatusName": "正常交易",
        "agentName": "",
        "agentAccount": "",
        "counterparty": 0,
        "rqAccountType": 0
      },
      "totalIn": "0.00",
      "totalOut": "0.00",
      "serviceName": "",
      "serviceAccount": "",
      "agentId": "",
      "agentName": "",
      "agentAccount": "",
      "memberName": "",
      "memberAccount": "",
      "sellerName": "",
      "sellerAccount": "",
      "sellerName2": "",
      "sellerAccount2": "",
      "loginControl": "1,2",
      "returnCommissionType": "不返佣",
      "rankingList": "1",
      "orderRankingLists": "1",
      "commissionGroupID": "0",
      "bino": "",
      "saleId": "",
      "gemLimit": "25",
      "algorithmState": 0,
      "applySource": "web",
      "applyTime": "2025-05-14 09:39:23",
      "lastUploadTime": "",
      "account_status": 1
    }
  },
  "msg": "成功"
}





7、详情页-影像材料


FinanceAPI/customerManageDetail/baseInfoQuery

入参：
userID：41806
queryMode：detail
queryTab:openmat
isAddPasswd:1
token

返回：
{
    "code": "0",
    "data": {
        "userID": 0,
        "idCardFront": "https://lioncdn.blob.core.windows.net/flower/sas_flower_4d4934a56f1a4f29b0dbef5f5215ae86.jpg?se=2025-05-19T02%3A45%3A11Z\u0026sig=BH0Fk7b6jVn4pjfTYlBN7AJnlgBmyuKP2heah9GiLC4%3D\u0026sp=r\u0026spr=https\u0026sr=b\u0026st=2025-05-19T02%3A35%3A11Z\u0026sv=2020-02-10",
        "idCardBack": "https://lioncdn.blob.core.windows.net/flower/sas_flower_897f222c3bcc4da296cb2d66405273b3.jpg?se=2025-05-19T02%3A45%3A11Z\u0026sig=zpK1O7UVkSdQjHd%2BhgQBA7j%2BcaeeJfIRyirLI0fZ%2Bdo%3D\u0026sp=r\u0026spr=https\u0026sr=b\u0026st=2025-05-19T02%3A35%3A11Z\u0026sv=2020-02-10",
        "autograph": "https://lioncdn.blob.core.windows.net/flower/sas_flower_cfdca26753834501a6368205caaa278f.jpg?se=2025-05-19T02%3A45%3A11Z\u0026sig=a7nUoaMJQBfkRdvUjxARm24CW9YetlrTuVJ3LBmQwsw%3D\u0026sp=r\u0026spr=https\u0026sr=b\u0026st=2025-05-19T02%3A35%3A11Z\u0026sv=2020-02-10",
        "riskDisclosure": "https://lioncdn.blob.core.windows.net/flower/sas_flower_cbd3a06dee8b4732b5c0d984ae46e64b.jpg?se=2025-05-19T02%3A45%3A11Z\u0026sig=svRKHr7UCx4zuiIvuEGdHXc6IzvF2nvh4k7UHZCLeRw%3D\u0026sp=r\u0026spr=https\u0026sr=b\u0026st=2025-05-19T02%3A35%3A11Z\u0026sv=2020-02-10",
        "isAddPasswd": "1",
        "faceImg": "",
        "regularFaceImg": "",
        "comparisonDate": ""
    },
    "msg": "成功"
}



8-详情页-回访信息


FinanceAPI/customerManageDetail/baseInfoQuery

入参：

userID：41806
queryMode：detail
queryTab：revisit
token

返回：
{
  "code": "0",
  "data": {
    "revisitFlag": "1",
    "revisitResultInfo": [
      {
        "revisitResult": "成功",
        "revisitUser": "Qingli",
        "selfOpenAccount": "是",
        "selfOperate": "是",
        "readRisk": "是",
        "remark": "",
        "revisitDate": "2025-05-14 09:42:25",
        "successFlag": "1",
        "revisitType": "开户审核",
        "revisitFile": []
      }
    ]
  },
  "msg": "成功"
}


9、详情页-财富来源


FinanceAPI/customerManageDetail/baseInfoQuery

入参：


userID：41806
queryMode：detail
queryTab：other
token


返回：
{
  "code": "0",
  "data": {
    "userID": 41806,
    "assetsSource": "工资,存款,投资回报",
    "assetsSourceID": "1,2,3",
    "assetsType": "存款,股票,基金,车,外汇",
    "assetsTypeID": "3,5,6,2,7",
    "netAssets": "100-500万",
    "netAssetsID": "3",
    "userIncome": "100-500万",
    "userIncomeID": "4",
    "bankruptcyDesc": "",
    "careerType": "职员",
    "careerTypeID": "1",
    "companyName": "东证融汇证券资产管理有限公司",
    "companyType": "国有企业",
    "companyTypeID": "4",
    "companyAddress": "上海市浦东新区杨高南路729号陆家嘴世纪金融广场1号楼37层",
    "userPosition": "产品",
    "userPositionID": "2",
    "workingSife": "8",
    "positionDesc": "",
    "industryType": "金融",
    "industryTypeID": "2",
    "industryDesc": "",
    "openSource": "PC端",
    "submitDate": "2025-05-14 09:39:23",
    "checkDate": "2025-05-14 09:42:27",
    "businessType": "证券",
    "checkUser": "Qingli",
    "familyName": "",
    "familySurname": "",
    "showFamily": 0,
    "hasTaxInfo": "0",
    "taxCountry": "",
    "taxNumber": "",
    "checkmark": "",
    "emid": ""
  },
  "msg": "成功"
}


10、详情页-投资调研


FinanceAPI/customerManageDetail/baseInfoQuery

入参：


userID：41806
queryMode：detail
queryTab：questionnaire
token


返回：


{
  "code": "0",
  "data": {
    "questionnaireScore": "70",
    "riskLevel": "进取",
    "submitTime": "2025-05-14 09:39:23",
    "lastModifyTime": "2025-05-14 09:39:23",
    "details": [
      {
        "questionnaireType": "投资目的",
        "questionnaireContent": [
          {
            "id": 1,
            "topicAnswer": "收入,抵制通胀,资本增值,投机",
            "topicTitle": "投资目的（可多选）"
          },
          {
            "id": 25,
            "topicAnswer": "资本增值",
            "topicTitle": "主要投资目的"
          },
          {
            "id": 2,
            "topicAnswer": "100~500万",
            "topicTitle": "预投金额（RMB）"
          },
          {
            "id": 3,
            "topicAnswer": "分散投资（股票、基金、债券等）",
            "topicTitle": "首选投资类型"
          },
          {
            "id": 4,
            "topicAnswer": "中期（6~10年）",
            "topicTitle": "投资期限"
          },
          {
            "id": 5,
            "topicAnswer": "1~5年",
            "topicTitle": "您认为需要多长时间才能达到您预期的投资回报?"
          }
        ]
      },
      {
        "questionnaireType": "投资经验",
        "questionnaireContent": [
          {
            "id": 6,
            "topicAnswer": "5~10年",
            "topicTitle": "货币市场基金、存款、债券、债券基金或简单的保本产品"
          },
          {
            "id": 7,
            "topicAnswer": "5~10年",
            "topicTitle": "股票、货币或利率挂钩的简单结构性产品"
          },
          {
            "id": 8,
            "topicAnswer": "5~10年",
            "topicTitle": "港股、国外股票"
          },
          {
            "id": 9,
            "topicAnswer": "5~10年",
            "topicTitle": "期货、期权、对冲基金、证券保证金交易、商品保证金交易、外币保证金交易、认股证"
          },
          {
            "id": 10,
            "topicAnswer": "11~50次",
            "topicTitle": "投资频率（过去一年）"
          }
        ]
      },
      {
        "questionnaireType": "投资现状",
        "questionnaireContent": [
          {
            "id": 11,
            "topicAnswer": "是",
            "topicTitle": "是否持有港股、海外股票？"
          },
          {
            "id": 12,
            "topicAnswer": "是",
            "topicTitle": "是否持有期货、期权、杠杆式外汇交易合约？"
          },
          {
            "id": 13,
            "topicAnswer": "是",
            "topicTitle": "是否持有存款、债券、债券基金或简单的保本产品？"
          },
          {
            "id": 14,
            "topicAnswer": "否",
            "topicTitle": "是否持有单位信托基金、互惠基金？"
          },
          {
            "id": 15,
            "topicAnswer": "否",
            "topicTitle": "是否持有股票挂钩存款/票据、货币挂钩存款、其他结构性存款/产品？"
          },
          {
            "id": 16,
            "topicAnswer": "否",
            "topicTitle": "是否持有对冲基金、非上市（场外）衍生品？"
          },
          {
            "id": 17,
            "topicAnswer": "是（比特币）",
            "topicTitle": "持有的其他投资产品"
          },
          {
            "id": 18,
            "topicAnswer": "有经验，有一些投资经验，可在略微指点下自行做出投资决定",
            "topicTitle": "以下哪一句话最能准确描述您在固定收益证券、结构性/衍生产品、期货、期权、认股权证或对冲基金的投资经验？"
          }
        ]
      },
      {
        "questionnaireType": "风险承受度",
        "questionnaireContent": [
          {
            "id": 19,
            "topicAnswer": "25~45岁",
            "topicTitle": "您的年龄是？"
          },
          {
            "id": 20,
            "topicAnswer": "否",
            "topicTitle": "您在未来三个月是否急用到现金的情况？"
          },
          {
            "id": 21,
            "topicAnswer": "\u003c10%",
            "topicTitle": "您在投资中配置的流动资产（高流通）占比是？"
          },
          {
            "id": 22,
            "topicAnswer": "26~50%",
            "topicTitle": "您能够承受多大程度的亏损?"
          },
          {
            "id": 23,
            "topicAnswer": "我仍然工作且我需要依赖我的投资来增加收入",
            "topicTitle": "以下哪一句最能准确描述您的工作/收入情况?"
          },
          {
            "id": 24,
            "topicAnswer": "我主要目标是实现长期资产增值（25%以上），虽然我会担心价值剧烈波动、担心损失风险提高，但是我接受相当程度的投资波动",
            "topicTitle": "以下哪一句最能准确描述您对投资价值波动的感受和态度？"
          }
        ]
      }
    ]
  },
  "msg": "成功"
}


11、详情页-审核记录

/FinanceAPI/user/queryCheckRecord


入参：
userid：41806
type：1
token


返回：

{
  "applyinfo": {
    "date": "2025-05-14 09:33:40",
    "source": "web"
  },
  "code": "0",
  "data": [
    {
      "date": "2025-05-14 09:42:27",
      "checkName": "Qingli",
      "state": 2,
      "userid": 40411,
      "mark": ""
    }
  ],
  "msg": "成功"
}



12、预开户列表查询


/FinanceAPI/user/advanceOpenList

入参：

businessTypeIDS

idCard
realName
mobile
email
actionType：query
pageNo
pageSize
beginTime
endTime
usercate
token

返回：


{
  "code": "0",
  "count": 11,
  "data": [
    {
      "businessType": "证券",
      "userId": "41805",
      "realName": "杰森",
      "sex": "男",
      "idCardType": "身份证",
      "idCard": "*****",
      "applyTime": "2025-05-13 20:27:06",
      "isShowInfo": "3",
      "usercate": "3"
    }
  ],
  "msg": "成功",
  "pageNo": 1
}


13、客户交易状态查询


/FinanceAPI/user/pageQueryUserTradeState

入参：

pageNo
pageSize
name
account
tradeState
algorithmState
token

返回：

{
  "code": "0",
  "data": [
    {
      "account": "********",
      "name": "分帐户-中金",
      "tradeState": "正常交易",
      "tradeStateNum": 1,
      "algorithmState": "不开通",
      "algorithmStateNum": 2,
      "modifydate": "2024-06-14 11:47:26",
      "id": 200844
    },
    {
      "account": "********",
      "name": "分帐户-华泰",
      "tradeState": "正常交易",
      "tradeStateNum": 1,
      "algorithmState": "不开通",
      "algorithmStateNum": 2,
      "modifydate": "2024-06-14 11:47:29",
      "id": 41687
    }
  ],
  "msg": "成功",
  "total": 54
}



14、代理列表

FinanceAPI/agentList/showList


入参：

businessType
idNumber
name
mobile
mail
pageNo
pageSize
riskMatchStatus
tradeAccount
updateState
sellerName
sellerAccount
agentName
agentAccount
token


返回：

{
  "code": "0",
  "count": 2,
  "data": [
    {
      "accountid": ********,
      "id": 41774,
      "mail": "*****",
      "mobile": "*****",
      "status": "正常",
      "realName": "姚立军",
      "createDate": "2025-04-01 11:13:57",
      "accountNo": "300151",
      "idCard": "*****",
      "idNumber": "*****",
      "cartType": "身份证",
      "mobileType": "86",
      "phone": "",
      "customerAmount": 0,
      "businessType": "证券",
      "cardType": 1,
      "businessTypeID": "3",
      "statusID": 1,
      "ifUpdate": "未更新",
      "riskMatchingStatus": "是",
      "saleName": "姚立军",
      "saleId": "81010"
    },
    {
      "accountid": ********,
      "id": 41707,
      "mail": "*****",
      "mobile": "*****",
      "status": "正常",
      "realName": "李淼",
      "createDate": "2025-04-01 10:38:57",
      "accountNo": "300150",
      "idCard": "*****",
      "idNumber": "*****",
      "cartType": "身份证",
      "mobileType": "86",
      "phone": "",
      "customerAmount": 0,
      "businessType": "证券",
      "cardType": 1,
      "businessTypeID": "3",
      "statusID": 1,
      "ifUpdate": "未更新",
      "riskMatchingStatus": "是",
      "saleName": "李淼",
      "saleId": "81009"
    }
  ],
  "msg": "ok"
}


15、销售列表

FinanceAPI/sale/listControl

入参：
businessType
accountNo
realName
mobile
status
actionType
pageNo
pageSize
token


{
  "code": "0",
  "count": 9,
  "data": [
    {
      "accountid": 0,
      "id": 41963,
      "mail": "*****",
      "mobile": "",
      "status": "正常",
      "realName": "吴星翰",
      "createDate": "2025-01-07 12:36:58",
      "accountNo": "89280",
      "idCard": "*****",
      "idNumber": "",
      "cartType": "",
      "mobileType": "86",
      "phone": "*****",
      "customerAmount": 31,
      "businessType": "证券",
      "cardType": 1,
      "businessTypeID": "3",
      "statusID": 1,
      "ifUpdate": "",
      "riskMatchingStatus": "",
      "saleName": "",
      "saleId": ""
    }
  ],
  "msg": "ok"
}



16、开户验证码

Customer/auth/v1/mailcaptcha

入参：

{
  "mail": "<EMAIL>"
}

返回：

{"code":0,"message":"OK","data":null}



17、验证验证码

Customer/auth/v1/mailauth

入参：
{
    "mail": "<EMAIL>",
    "captcha": "6635"
}



{"code":0,"message":"OK","data":{"session":"e23d449454972bb5e01e0ca6554553d1"}}


18、开户查询暂存信息

/Customer/openaccount/v1/queryUserInfo


入参：
{
    "session": "12f0a380ed5eda08fb549b147504a88f",
    "opensource": 2
}

返回：
{
    "code": 0,
    "message": "OK",
    "data": {
        "userinfo": {
            "male": 0,
            "usersurname": "",
            "username": "",
            "idcard": "",
            "cardtype": 0,
            "birthday": "",
            "mobiletype": "",
            "mobile": "",
            "mail": "<EMAIL>",
            "postalcountry": "",
            "postalprovince": "",
            "postalcity": "",
            "postaladdress": "",
            "emid": "",
            "birthplace": "",
            "similarityPercent": 0,
            "cardaddress": "",
            "cardStartDate": "",
            "cardEnddate": "",
            "cardOrgan": "",
            "nation": 0
        },
        "bankcardinfo": {
            "bankcard": "",
            "bankcountry": "",
            "bankprovince": "",
            "bankcity": "",
            "bankbranchname": "",
            "bankcode": ""
        },
        "assetinfo": {
            "assetsource": "",
            "assetstype": "",
            "familyname": "",
            "familysurname": "",
            "netassets": 0,
            "userincome": 0,
            "bankruptcydesc": "",
            "bankruptcy": -1
        },
        "careerinfo": {
            "careertype": 0,
            "companytype": 0,
            "companyname": "",
            "companyaddress": "",
            "userposition": 0,
            "userpositionother": "",
            "industry": 0,
            "industryother": "",
            "workinglife": -1
        },
        "userfiles": {
            "idcardback": "",
            "idcardfront": "",
            "riskdisclosure": "",
            "autograph": "",
            "faceImg": ""
        },
        "taxinfo": {
            "option1": "",
            "option2": "",
            "option3": "",
            "option4": "",
            "taxcountry": "",
            "taxnumber": ""
        },
        "isopenaccount": 0,
        "isagent": 0,
        "userid": 42526,
        "examid": 17997,
        "extendid": 0,
        "cardinfoid": 0,
        "valcode": "",
        "valkey": "",
        "session": "12f0a380ed5eda08fb549b147504a88f",
        "openbusintype": "",
        "invitecode": "",
        "isquestanswer": "0",
        "opensource": 0,
        "usertype": 0,
        "accounttype": 0
    }
}


19、开户查询图形验证码

Customer/auth/v1/captcha

返回：

{
    "code": 0,
    "message": "OK",
    "data": {
        "valImgBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAABQBAMAAAAJnpF4AAAAIVBMVEUAAAA+MR9CNSM2KRcjFgQhFAK5rJowIxFZTDrBtKKypZNfEjczAAAAAXRSTlMAQObYZgAAAnFJREFUeJzslz9uHD0MxfU+4zO2JIEUKYPcIMgJAuQASZF+K5/DpY8dkJREkfN3YxUBMiyMNVbz9CNFPs2WK6644orHgoE5QiBiZp6ipUrMNEEKTApGM8QkP1VT5XcVDqyPW/khfHuAskD23via2iL7u5euVvWbbMx7/NB1XGMNTncDf/iuXOQHj7y2cgHgzjYqwrYauLyNFmL6nKg0NU2YajDV3Zq+cFU1UEz4SVbehUvprN+sWZqwaXpXywf7zBHs6xcuzy9oC4nYDgIaDO6l9MxADYiC2JOsuZdWIKtOL954LMOwiVrLchBDODg0LmpUBasHjP7AIEZxuPW/2r6EVjxatgq5WNfnzNUSFSnRxIGToDdcWkm12ays5iFHsw8foIDVKVmOu03qgZZlmTKU/lKSQp7dsYFolgDFPZlrx8Inad0sX2OWK8YAkNWfqnVscd3eIsPy4oCNDGC0p7msn+My1n4GuXJ1EBwVjTkfkc6h61sBlhO0DL02oufKAx9Dxs61sKqUIUYM/Yf57dWxAte2FuqImdNpyfVeKreEVbn2kmxfmscUc0+Ezcd+wd4t5a01ciHuNgpvg8VaKpd2xKYWtrnMhJN+drKQ8S7XQi5zpb0OuPTvwJWajXao17jkiefGVXaw9iwRzvVyL+5dErdluY6teuQaOkJ6/xGuLuefEleytkdeRjnXqzzK5UvzLZK9Tbj+OyWVhzd7rvX950/nuOI10rn6m5J8fY4rR+NCt5M/ftvuh4r69vSOXwH9UPnYVs9z4dQ9dKDmiLN+6FSyn1OkNP7/NU+r/JiodcUV/2ZMnKJruv+u+B0AAP//uC4yUJCy4vEAAAAASUVORK5CYII=",
        "valKey": "9UHz91A7ty1fBfuESzYT"
    }
}


20、上传图片

Customer/userfile/v1/uploadFile


返回：

{
    "code": 0,
    "message": "OK",
    "data": {
        "object_id": "sas_winner_8d40854ad41a4b6b8e8715e1440de65f.jpg",
        "url": "https://lioncdn.blob.core.windows.net/winner/sas_winner_8d40854ad41a4b6b8e8715e1440de65f.jpg?se=2025-05-19T06%3A49%3A51Z\u0026sig=BfmaDp0bZf6BNrtVbbXPFagtI%2FukW4S3xUz2OR1jlYo%3D\u0026sp=r\u0026spr=https\u0026sr=b\u0026st=2025-05-19T06%3A39%3A51Z\u0026sv=2020-02-10",
        "fileName": "1212.jpg"
    }
}


21、开户上传图片

/Customer/userfile/v1/saveUserFile

入参：
{
    "fileurl": "sas_winner_8d40854ad41a4b6b8e8715e1440de65f.jpg",
    "filesuffix": "jpg",
    "uploadsource": 3,
    "filetype": 3,
    "userid": 42526
}

返回：
{"code":0,"message":"OK","data":null}



22、开户图片ocr识别

Customer/grada/ocrCardImg

入参：
{
    "imgurl": "https://lioncdn.blob.core.windows.net/winner/sas_winner_8d40854ad41a4b6b8e8715e1440de65f.jpg?se=2025-05-19T06%3A49%3A51Z&sig=BfmaDp0bZf6BNrtVbbXPFagtI%2FukW4S3xUz2OR1jlYo%3D&sp=r&spr=https&sr=b&st=2025-05-19T06%3A39%3A51Z&sv=2020-02-10",
    "token": "12f0a380ed5eda08fb549b147504a88f"
}

返回：

{"code":"0","data":{"personalNumber":"522529199905040053","dateofBirth":"1999-05-04","name":"黄杰","nationality":"布依","cardAddress":"贵州省镇宁布依族苗族育治县城关镇东街19号附9","sex":"男","countryName":"CHN","cardStartDate":"","cardEndDate":"","signorg":""},"message":"成功"}




23、保存开户信息第一步

Customer/openaccount/v1/saveUserExam

入参：

{
    "userinfo": {
        "usersurname": "黄",
        "username": "杰",
        "idcard": "340811198808226318",
        "birthday": "1999-05-04",
        "mobiletype": "86",
        "mobile": "***********",
        "mail": "<EMAIL>",
        "postalcountry": "1",
        "postalprovince": "251",
        "postalcity": "502",
        "postaladdress": "贵州省镇宁布依族苗族育治县城关镇东街19号附9",
        "birthplace": "贵州省镇宁布依族苗族育治县",
        "cardaddress": "贵州省镇宁布依族苗族育治县",
        "cardStartDate": "2025-05-01",
        "cardEnddate": "2099-12-31",
        "cardOrgan": "测试机关",
        "similarityPercent": 0,
        "emid": "",
        "male": 1,
        "cardtype": 1,
        "nation": 1
    },
    "assetinfo": {
        "assetsource": "1",
        "assetstype": "1",
        "familyname": "",
        "familysurname": "",
        "bankruptcydesc": "",
        "netassets": 1,
        "userincome": 1,
        "bankruptcy": 0
    },
    "bankcardinfo": {
        "bankcard": "6212261212022782541",
        "bankprovince": "251",
        "bankcity": "502",
        "bankcode": "ICBC",
        "bankbranchname": "测试支行",
        "bankcountry": "1"
    },
    "careerinfo": {
        "companyname": "测试公司",
        "companyaddress": "浙江杭州",
        "userpositionother": "",
        "industryother": "",
        "careertype": 1,
        "companytype": 1,
        "userposition": 2,
        "industry": 1,
        "workinglife": 10
    },
    "taxinfo": {
        "option1": "1",
        "option2": "1",
        "option3": "1",
        "option4": "2",
        "taxcountry": "",
        "taxnumber": ""
    },
    "valcode": "1254",
    "valkey": "gzaXn8yoUKfDg18xuSkS",
    "invitecode": "300152",
    "userid": 42526,
    "examid": 17997,
    "extendid": 0,
    "cardinfoid": 0,
    "session": "12f0a380ed5eda08fb549b147504a88f",
    "isopenaccount": 1,
    "opensource": 2,
    "openbusintype": "3"
}


24、开户提示查询

/Customer/user/queryTips

入参：
{
    "id": "35"
}

{
    "code": "0",
    "data": "<p><span style=\"color: #000000;\">证券、期货、差价合约、衍生品等业务是一项高风险的投资，可能获得较高的收益，也可能发生较大的损失。投资者在进入市场前，应充分了解交易市场的相关风险和交易产品的规则和特点。投资者应充分评估自身的风险承受能力，合理配置资产、不应借钱来做投资，理性地进行投资。投资者不应听信市场上任何具有操作建议和培训服务，客服人员不负责开户工作，开户和交易行为由投资者自行决定并自主到官网和交易软件进行操作，交易导致的后果和责任均由投资者自行承担，我司不因此承担任何法律责任。因各种不可抗力因素导致的服务器中断，可能导致您无法在交易软件下单或平仓等操作，在此过程中产生的价格风险，由投资者承担。</span></p><p style=\"text-align: right;\"><span style=\"color: #000000;\">特此提示！</span></p>",
    "message": "成功"
}


25、查询开户问卷题目


Customer/questionnaire/queryQuestionnaireList


返回：

{
    "code": "0",
    "data": [
        {
            "categoryName": "投资目的",
            "sort": 1,
            "topicList": [
                {
                    "topicId": 1,
                    "topicTitle": "投资目的（可多选）",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 1,
                            "anwserDesc": "退休",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 2,
                            "anwserDesc": "保本",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 3,
                            "anwserDesc": "收入",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 4,
                            "anwserDesc": "对冲",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 5,
                            "anwserDesc": "抵制通胀",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 6,
                            "anwserDesc": "资本增值",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 7,
                            "anwserDesc": "套利",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 8,
                            "anwserDesc": "投机",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 25,
                    "topicTitle": "主要投资目的",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 87,
                            "anwserDesc": "保本",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 88,
                            "anwserDesc": "稳定收入",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 89,
                            "anwserDesc": "资本增值",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 90,
                            "anwserDesc": "稳定收入和资本增值均衡",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 2,
                    "topicTitle": "预投金额（RMB）",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 9,
                            "anwserDesc": "<50万",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 10,
                            "anwserDesc": "50~100万",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 11,
                            "anwserDesc": "100~500万",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 12,
                            "anwserDesc": "500~1000万",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 13,
                            "anwserDesc": ">1000万",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 3,
                    "topicTitle": "首选投资类型",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 14,
                            "anwserDesc": "定期存款或储蓄账户",
                            "selected": false,
                            "relationTextState": "1"
                        },
                        {
                            "anwserId": 15,
                            "anwserDesc": "分散投资（股票、基金、债券等）",
                            "selected": false,
                            "relationTextState": "2"
                        },
                        {
                            "anwserId": 16,
                            "anwserDesc": "投机类",
                            "selected": false,
                            "relationTextState": "5"
                        },
                        {
                            "anwserId": 17,
                            "anwserDesc": "其他",
                            "selected": false,
                            "relationTextState": "4"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 4,
                    "topicTitle": "投资期限",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 18,
                            "anwserDesc": "短期（少于一年）",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 19,
                            "anwserDesc": "中短期（1~5年）",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 20,
                            "anwserDesc": "中期（6~10年）",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 21,
                            "anwserDesc": "长期（10年以上）",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 5,
                    "topicTitle": "您认为需要多长时间才能达到您预期的投资回报?",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 22,
                            "anwserDesc": ">10年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 23,
                            "anwserDesc": "6~10年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 24,
                            "anwserDesc": "1~5年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 25,
                            "anwserDesc": "<1年",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                }
            ]
        },
        {
            "categoryName": "投资经验",
            "sort": 2,
            "topicList": [
                {
                    "topicId": 6,
                    "topicTitle": "货币市场基金、存款、债券、债券基金或简单的保本产品",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 26,
                            "anwserDesc": "没有经验",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 27,
                            "anwserDesc": "0~4年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 28,
                            "anwserDesc": "5~10年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 29,
                            "anwserDesc": "超过10年",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 7,
                    "topicTitle": "股票、货币或利率挂钩的简单结构性产品",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 30,
                            "anwserDesc": "没有经验",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 31,
                            "anwserDesc": "0~4年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 32,
                            "anwserDesc": "5~10年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 33,
                            "anwserDesc": "超过10年",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 8,
                    "topicTitle": "港股、国外股票",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 34,
                            "anwserDesc": "没有经验",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 35,
                            "anwserDesc": "0~4年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 36,
                            "anwserDesc": "5~10年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 37,
                            "anwserDesc": "超过10年",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 9,
                    "topicTitle": "期货、期权、对冲基金、证券保证金交易、商品保证金交易、外币保证金交易、认股证",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 38,
                            "anwserDesc": "没有经验",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 39,
                            "anwserDesc": "0~4年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 40,
                            "anwserDesc": "5~10年",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 41,
                            "anwserDesc": "超过10年",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 10,
                    "topicTitle": "投资频率（过去一年）",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 42,
                            "anwserDesc": "0~10次",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 43,
                            "anwserDesc": "11~50次",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 44,
                            "anwserDesc": "51~100次",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 45,
                            "anwserDesc": "101~500次",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 46,
                            "anwserDesc": "500次以上",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                }
            ]
        },
        {
            "categoryName": "投资现状",
            "sort": 3,
            "topicList": [
                {
                    "topicId": 11,
                    "topicTitle": "是否持有港股、海外股票？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 47,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 48,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 12,
                    "topicTitle": "是否持有期货、期权、杠杆式外汇交易合约？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 49,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 50,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 13,
                    "topicTitle": "是否持有存款、债券、债券基金或简单的保本产品？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 51,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 52,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 14,
                    "topicTitle": "是否持有单位信托基金、互惠基金？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 53,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 54,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 15,
                    "topicTitle": "是否持有股票挂钩存款/票据、货币挂钩存款、其他结构性存款/产品？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 55,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 56,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 16,
                    "topicTitle": "是否持有对冲基金、非上市（场外）衍生品？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 57,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 58,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 17,
                    "topicTitle": "持有的其他投资产品",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 59,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "1"
                        },
                        {
                            "anwserId": 60,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 18,
                    "topicTitle": "以下哪一句话最能准确描述您在固定收益证券、结构性/衍生产品、期货、期权、认股权证或对冲基金的投资经验？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 61,
                            "anwserDesc": "毫无经验",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 62,
                            "anwserDesc": "经验有限，有一些投资经验，但需要详细指点",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 63,
                            "anwserDesc": "有经验，有一些投资经验，可在略微指点下自行做出投资决定",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 64,
                            "anwserDesc": "经验丰富，有丰富经验的积极投者，更愿意资金做出投资决定",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                }
            ]
        },
        {
            "categoryName": "风险承受度",
            "sort": 4,
            "topicList": [
                {
                    "topicId": 19,
                    "topicTitle": "您的年龄是？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 65,
                            "anwserDesc": ">65岁",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 66,
                            "anwserDesc": "46~64岁",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 67,
                            "anwserDesc": "25~45岁",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 86,
                            "anwserDesc": "<25岁",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 20,
                    "topicTitle": "您在未来三个月是否急用到现金的情况？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 68,
                            "anwserDesc": "是",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 69,
                            "anwserDesc": "否",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 21,
                    "topicTitle": "您在投资中配置的流动资产（高流通）占比是？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 70,
                            "anwserDesc": "<10%",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 71,
                            "anwserDesc": "10~25%",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 72,
                            "anwserDesc": "26~50%",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 73,
                            "anwserDesc": ">50%",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 22,
                    "topicTitle": "您能够承受多大程度的亏损?",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 74,
                            "anwserDesc": "<15%",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 75,
                            "anwserDesc": "15~25%",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 76,
                            "anwserDesc": "26~50%",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 77,
                            "anwserDesc": ">50%",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 23,
                    "topicTitle": "以下哪一句最能准确描述您的工作/收入情况?",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 78,
                            "anwserDesc": "我已退休/没有工作且我需要依赖我的投资来支付生活费用",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 79,
                            "anwserDesc": "我已退休/没有工作且我不需要依赖我的投资来支付生活费用",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 80,
                            "anwserDesc": "我仍然工作且我需要依赖我的投资来增加收入",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 81,
                            "anwserDesc": "我仍然工作且我不需要依赖我的投资来增加收入",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                },
                {
                    "topicId": 24,
                    "topicTitle": "以下哪一句最能准确描述您对投资价值波动的感受和态度？",
                    "topicType": "0",
                    "anwserList": [
                        {
                            "anwserId": 82,
                            "anwserDesc": "我不希望看到投资价值出现波动，我无法承受任何资金损失",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 83,
                            "anwserDesc": "我比较谨慎，到那时能接受我的投资价值出现轻微波动以及可能损失部分本金，比起最大限度的实现资本增值（10%以下），我更倾向保本和赚取收益",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 84,
                            "anwserDesc": "我对投资的态度比较温和，并接受高回报（在10-25%之间）意味着必须接受我的投资价值出现波动以及可能损失本金",
                            "selected": false,
                            "relationTextState": "0"
                        },
                        {
                            "anwserId": 85,
                            "anwserDesc": "我主要目标是实现长期资产增值（25%以上），虽然我会担心价值剧烈波动、担心损失风险提高，但是我接受相当程度的投资波动",
                            "selected": false,
                            "relationTextState": "0"
                        }
                    ],
                    "anwserId": "",
                    "anwserTxt": ""
                }
            ]
        }
    ],
    "message": "成功"
}


26、开户提交

Customer/questionnaire/saveQuestionnaire


{
    "list": [
        {
            "topicId": 1,
            "topicAnswerId": 1,
            "topicTitle": "投资目的（可多选）",
            "topicAnswerDesc": "退休",
            "supplementText": ""
        },
        {
            "topicId": 25,
            "topicAnswerId": 87,
            "topicTitle": "主要投资目的",
            "topicAnswerDesc": "保本",
            "supplementText": ""
        },
        {
            "topicId": 2,
            "topicAnswerId": 9,
            "topicTitle": "预投金额（RMB）",
            "topicAnswerDesc": "<50万",
            "supplementText": ""
        },
        {
            "topicId": 3,
            "topicAnswerId": 14,
            "topicTitle": "首选投资类型",
            "topicAnswerDesc": "定期存款或储蓄账户",
            "supplementText": ""
        },
        {
            "topicId": 4,
            "topicAnswerId": 18,
            "topicTitle": "投资期限",
            "topicAnswerDesc": "短期（少于一年）",
            "supplementText": ""
        },
        {
            "topicId": 5,
            "topicAnswerId": 22,
            "topicTitle": "您认为需要多长时间才能达到您预期的投资回报?",
            "topicAnswerDesc": ">10年",
            "supplementText": ""
        },
        {
            "topicId": 6,
            "topicAnswerId": 26,
            "topicTitle": "货币市场基金、存款、债券、债券基金或简单的保本产品",
            "topicAnswerDesc": "没有经验",
            "supplementText": ""
        },
        {
            "topicId": 7,
            "topicAnswerId": 31,
            "topicTitle": "股票、货币或利率挂钩的简单结构性产品",
            "topicAnswerDesc": "0~4年",
            "supplementText": ""
        },
        {
            "topicId": 8,
            "topicAnswerId": 35,
            "topicTitle": "港股、国外股票",
            "topicAnswerDesc": "0~4年",
            "supplementText": ""
        },
        {
            "topicId": 9,
            "topicAnswerId": 39,
            "topicTitle": "期货、期权、对冲基金、证券保证金交易、商品保证金交易、外币保证金交易、认股证",
            "topicAnswerDesc": "0~4年",
            "supplementText": ""
        },
        {
            "topicId": 10,
            "topicAnswerId": 43,
            "topicTitle": "投资频率（过去一年）",
            "topicAnswerDesc": "11~50次",
            "supplementText": ""
        },
        {
            "topicId": 11,
            "topicAnswerId": 48,
            "topicTitle": "是否持有港股、海外股票？",
            "topicAnswerDesc": "否",
            "supplementText": ""
        },
        {
            "topicId": 12,
            "topicAnswerId": 50,
            "topicTitle": "是否持有期货、期权、杠杆式外汇交易合约？",
            "topicAnswerDesc": "否",
            "supplementText": ""
        },
        {
            "topicId": 13,
            "topicAnswerId": 52,
            "topicTitle": "是否持有存款、债券、债券基金或简单的保本产品？",
            "topicAnswerDesc": "否",
            "supplementText": ""
        },
        {
            "topicId": 14,
            "topicAnswerId": 54,
            "topicTitle": "是否持有单位信托基金、互惠基金？",
            "topicAnswerDesc": "否",
            "supplementText": ""
        },
        {
            "topicId": 15,
            "topicAnswerId": 56,
            "topicTitle": "是否持有股票挂钩存款/票据、货币挂钩存款、其他结构性存款/产品？",
            "topicAnswerDesc": "否",
            "supplementText": ""
        },
        {
            "topicId": 16,
            "topicAnswerId": 58,
            "topicTitle": "是否持有对冲基金、非上市（场外）衍生品？",
            "topicAnswerDesc": "否",
            "supplementText": ""
        },
        {
            "topicId": 17,
            "topicAnswerId": 60,
            "topicTitle": "持有的其他投资产品",
            "topicAnswerDesc": "否",
            "supplementText": ""
        },
        {
            "topicId": 18,
            "topicAnswerId": 62,
            "topicTitle": "以下哪一句话最能准确描述您在固定收益证券、结构性/衍生产品、期货、期权、认股权证或对冲基金的投资经验？",
            "topicAnswerDesc": "经验有限，有一些投资经验，但需要详细指点",
            "supplementText": ""
        },
        {
            "topicId": 19,
            "topicAnswerId": 65,
            "topicTitle": "您的年龄是？",
            "topicAnswerDesc": ">65岁",
            "supplementText": ""
        },
        {
            "topicId": 20,
            "topicAnswerId": 68,
            "topicTitle": "您在未来三个月是否急用到现金的情况？",
            "topicAnswerDesc": "是",
            "supplementText": ""
        },
        {
            "topicId": 21,
            "topicAnswerId": 70,
            "topicTitle": "您在投资中配置的流动资产（高流通）占比是？",
            "topicAnswerDesc": "<10%",
            "supplementText": ""
        },
        {
            "topicId": 22,
            "topicAnswerId": 74,
            "topicTitle": "您能够承受多大程度的亏损?",
            "topicAnswerDesc": "<15%",
            "supplementText": ""
        },
        {
            "topicId": 23,
            "topicAnswerId": 81,
            "topicTitle": "以下哪一句最能准确描述您的工作/收入情况?",
            "topicAnswerDesc": "我仍然工作且我不需要依赖我的投资来增加收入",
            "supplementText": ""
        },
        {
            "topicId": 24,
            "topicAnswerId": 83,
            "topicTitle": "以下哪一句最能准确描述您对投资价值波动的感受和态度？",
            "topicAnswerDesc": "我比较谨慎，到那时能接受我的投资价值出现轻微波动以及可能损失部分本金，比起最大限度的实现资本增值（10%以下），我更倾向保本和赚取收益",
            "supplementText": ""
        }
    ],
    "userId": 42526
}

返回：

{
    "code": "0",
    "data": null,
    "message": "成功"
}



27、开户审核

FinanceAPI/customerManageDetail/baseInfoQuery


入参：

queryTab：approvedOrNot
remark：风险揭示书不符合规则
userID：42526
sarStatus：2
userExamState：3
businessTypeStr：3
token：cb45cafaf10743768e064bfc40aca168

返回：
{
    "code": "0",
    "data": null,
    "msg": "成功"
}




28、app狮子号登录

/FinanceAppAPI/user/userLogin

入参：
email=41697&login_type=2&captcha=4cfe000ecb84550c55de0ddeea720a88&entry_point&marketNo=APP_Flutter_Winner_Android&versionNo=10105&token=&uid=0&userid=0&lang=zh


返回：

{
	"list": [{
			"accountNo": "",
			"accountType": "1",
			"statusStr": "未开户",
			"status": "0",
			"tradeStatus": "",
			"tradeStatusStr": ""
		},
		{
			"accountNo": "********",
			"accountType": "3",
			"statusStr": "未登录",
			"status": "2",
			"tradeStatus": "1",
			"tradeStatusStr": "正常交易"
		}
	],
	"token": "5223690f182c437ab1953578f173242b",
	"realName": "测试",
	"userId": 41697,
	"futuresUserID": 0,
	"mobile": "***********",
	"mobileType": "86",
	"mail": "<EMAIL>",
	"mailNo": "c****<EMAIL>",
	"examineStatus": "2",
	"male": "1",
	"riskAuthState": "1",
	"mobileNo": "138****1286",
	"informationComplate": 1,
	"grantLoginState": 1,
	"organUserType": 0,
	"rqstate": 0,
	"annualState": 0,
	"annualMark": "",
	"userType": 0,
	"open": 0,
	"algorithmState": 0,
	"state": 1
}


29：查询交易账号

/FinanceAppAPI/user/grantLoginAccount

入参：
marketNo=APP_Flutter_Winner_Android&versionNo=10105&token=5223690f182c437ab1953578f173242b&uid=41697&userid=41697&lang=zh

返回：

{
    "code": "0",
    "data": {
        "token": "",
        "type": 3,
        "accountNo": "********",
        "futuresUserID": 0,
        "accountCate": 2,
        "followNo": 0,
        "expireRemind": 0,
        "applyId": 2385,
        "expireDate": ""
    },
    "msg": "成功"
}