

1、  新增融券券池 ，新增字段   内，外券池     
     	按钮：导入外部券池，外部券池模板下载，导出
	展示字段：所属市场，品种代码，名称，可用额度，已用额度，券池类型 


ALTER TABLE `phoenix_stockposition_channel` 
ADD COLUMN `p_rq_type` int(11) NOT NULL COMMENT '券池类型，1：外部券池，0：默认内部券池';


ALTER TABLE `phoenix_stockposition_channel_his` 
ADD COLUMN `p_rq_type` int(11) NOT NULL COMMENT '券池类型，1：外部券池，0：默认内部券池';


2、融券管理，新增时  证券代码选择时，列表新增券池类型（内，外券池     ）


3、users_trade_account  rq_state扩展   3：有融券权限，做空账户   ，4：有融券权限，做多账户


4、账户详情，新增展示字段,融券类型，新增编辑按钮


5、phoenix_risk_check 校验空头账户。


6、融券列表新增召回功能，，输入价格，数量，类似订单补录功能实现。
     未用额度超过召回数量，直接减少可用额度，
     未用额度少于召回数量，减少额度，然后剩余部分通过补录订单实现




7、FinanceAPI 总账户持仓  /accountpositions 接口改造过滤掉数据、

8、FinanceAPI 总账户风控  /risk/queryRisk 接口改造


9、账户风控结算，处理（待定）

