#!/bin/bash

# Fix script for buildall.sh to ensure it works properly on Linux
# This script addresses common issues with shell scripts created on Windows

echo "Fixing buildall.sh for Linux execution..."

# Check if buildall.sh exists
if [ ! -f "buildall.sh" ]; then
    echo "ERROR: buildall.sh not found in current directory"
    exit 1
fi

# Convert line endings from Windows (CRLF) to Unix (LF)
echo "Converting line endings..."
if command -v dos2unix &> /dev/null; then
    dos2unix buildall.sh
    echo "✓ Line endings converted using dos2unix"
elif command -v sed &> /dev/null; then
    sed -i 's/\r$//' buildall.sh
    echo "✓ Line endings converted using sed"
else
    echo "⚠ Warning: dos2unix or sed not available. You may need to manually convert line endings."
fi

# Make the script executable
echo "Making buildall.sh executable..."
chmod +x buildall.sh
echo "✓ Script is now executable"

# Verify the shebang line
SHEBANG=$(head -n1 buildall.sh)
if [[ "$SHEBANG" == "#!/bin/bash"* ]]; then
    echo "✓ Shebang line is correct: $SHEBANG"
else
    echo "⚠ Warning: Unusual shebang line: $SHEBANG"
fi

# Check if bash is available
if command -v bash &> /dev/null; then
    echo "✓ Bash is available at: $(which bash)"
else
    echo "⚠ Warning: Bash not found in PATH"
fi

# Check if cargo is available
if command -v cargo &> /dev/null; then
    echo "✓ Cargo is available at: $(which cargo)"
else
    echo "⚠ Warning: Cargo not found in PATH. Make sure Rust is installed."
fi

# Test syntax
echo "Testing script syntax..."
if bash -n buildall.sh; then
    echo "✓ Script syntax is valid"
else
    echo "✗ Script has syntax errors"
    exit 1
fi

echo ""
echo "================================"
echo "buildall.sh has been fixed!"
echo "You can now run it with:"
echo "  ./buildall.sh"
echo "  ./buildall.sh musl"
echo "  ./buildall.sh gnu"
echo "================================"
