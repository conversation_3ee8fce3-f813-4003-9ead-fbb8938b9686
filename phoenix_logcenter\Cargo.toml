[package]
name = "phoenix_logcenter"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
common = { workspace = true }

config = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
lapin = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
# notify = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }

anyhow = { workspace = true }
thiserror = { workspace = true }

# log = { workspace = true }
tracing = { workspace = true }
axum = { workspace = true }
tower = { workspace = true }
tower-http = { workspace = true }
http-body-util = { workspace = true }
chrono = { workspace = true }
teloxide = { workspace = true }
# teloxide-macros = { workspace = true }
# polodb_core = "5.1.3"
fjall = { workspace = true }
reqwest = { workspace = true }
# colored = { workspace = true }

strum = { workspace = true }
strum_macros = { workspace = true }

[build-dependencies]
# tonic-build = { workspace = true, features = ["prost"] }
tonic-build.workspace = true
