use strum_macros::Display;

pub const EMPTY_STR: &str = "";
// pub const MESSAGE_OK: &str = "";

// pub const IGNORE_ROUTES: [&str; 3] = ["/api/ping", "/api/v1/login", "/api/pinyin"];
// pub(crate) const TOKEN: &str = "icryrainix";

#[derive(PartialEq, Clone, Debug)]
pub enum HttpCode {
    OK = 200,
    Error = 201,
}

#[derive(Display, PartialEq, Clone, Debug)]
pub enum LogLevel {
    #[strum(to_string = "DEBUG")]
    Debug = 0,
    #[strum(to_string = "INFO")]
    Info = 1,
    #[strum(to_string = "WARN")]
    Warn = 2,
    #[strum(to_string = "ERROR")]
    Error = 3,
}

impl LogLevel {
    pub fn from_i32(value: i32) -> Self {
        match value {
            0 => LogLevel::Debug,
            1 => LogLevel::Info,
            2 => LogLevel::Warn,
            _ => LogLevel::Error,
        }
    }

    // pub fn to_i32(&self) -> i64 {
    //     match self {
    //         LogLevel::Debug => 0,
    //         LogLevel::Info => 1,
    //         LogLevel::Warn => 2,
    //         LogLevel::Error => 3,
    //     }
    // }

    // pub fn as_str(&self) -> &'static str {
    //     match self {
    //         // LogLevel::Debug => "🟦 <b>DEBUG</b>",
    //         // LogLevel::Info => "🟩 <b>INFO</b>",
    //         // LogLevel::Warn => "🟧 <b>WARN</b>",
    //         // LogLevel::Error => "🟥 <b>ERROR</b>",
    //         LogLevel::Debug => "DEBUG",
    //         LogLevel::Info => "INFO",
    //         LogLevel::Warn => "WARN",
    //         LogLevel::Error => "ERROR",
    //     }
    // }
}
