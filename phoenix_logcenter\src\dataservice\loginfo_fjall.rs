use crate::webserver::dto::QueryDto;
use anyhow::{anyhow, Result};
use fjall::{Config, Keyspace, PartitionCreateOptions, PersistMode};
use serde::{Deserialize, Serialize};
use std::fs::File;
use std::io::prelude::*;
use tracing::*;
// const LOG_COLLECTION: &str = "loginfo";

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct LogInfo {
    sid: String,
    srvname: String,
    logtype: i32,  //日志类型：1-系统监控日志、2-业务日志
    loglevel: i32, //DBG(debug):0; INF(info):1; WRN(warning):2; ERR(error):3-9;
    logtime: String,
    logcontent: String,
}

impl LogInfo {
    pub fn new(sid: &String, srvname: &String, logtype: i32, loglevel: i32, logtime: &String, logcontent: &String) -> Self {
        LogInfo {
            sid: sid.to_owned(),
            srvname: srvname.to_owned(),
            logtype,
            loglevel,
            logtime: logtime.to_owned(),
            logcontent: logcontent.to_owned(),
        }
    }
    pub fn insert_loginfo(info: &LogInfo, keyspace: &Keyspace) -> Result<i64> {
        // Each partition is its own physical LSM-tree
        let partion = keyspace.open_partition(&info.srvname, PartitionCreateOptions::default())?;
        partion.insert(format!("{}-{}", info.loglevel, info.logtime), serde_json::to_string(info).unwrap_or_default())?;
        // let collection = db.collection::<LogInfo>(&info.srvname);
        // let ret = collection.insert_one(info);
        // if ret.is_err() {
        //     error!("{}", ret.as_ref().unwrap_err().to_string());
        //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        // }
        // Ok(ret.unwrap().inserted_id)
        Ok(1)
    }
    pub fn query_serviceinfos(db: &Keyspace) -> Result<Vec<String>> {
        let pations = db.list_partitions();
        Ok(pations.into_iter().map(|v| v.to_string()).collect())
        // Ok(vec![])
        // let ret = db.list_collection_names();

        // // let collection = db.collection::<LogInfo>(LOG_COLLECTION);
        // // let ret = collection.insert_many(infos);
        // if ret.is_err() {
        //     error!("{}", ret.as_ref().unwrap_err().to_string());
        //     return Err(anyhow!("{}", ret.as_ref().unwrap_err().to_string()));
        // }
        // Ok(ret.unwrap())
    }

    pub fn export_loginfos(dto: &QueryDto, db: &Keyspace) -> Result<String> {
        if dto.sysname.is_empty() {
            return Err(anyhow!("service name is empty, not allowed"));
        }
        let filename = format!("./logdb/{}.log.{}", &dto.sysname, &dto.logdate);
        let mut file = std::fs::File::create(&filename)?;

        let partion = db.open_partition(&dto.sysname, PartitionCreateOptions::default())?;
        for kv in partion.prefix(&dto.loglevel.to_string()) {
            if let Ok(value) = kv {
                info!("value:{:?}", String::from_utf8(value.1.to_vec()));
                if let Ok(logstr) = String::from_utf8(value.1.to_vec()) {
                    if let Ok(loginfo) = serde_json::from_str::<LogInfo>(&logstr) {
                        let log_content = format!("{} {} {} {}", loginfo.srvname, loginfo.loglevel, loginfo.logtime, loginfo.logcontent);
                        //save to file
                        file.write(&log_content.as_bytes())?;
                    }
                }
            }
        }
        Ok(filename)
    }

    pub fn query_loginfos(dto: &QueryDto, db: &Keyspace) -> Result<Vec<LogInfo>> {
        if dto.sysname.is_empty() {
            return Err(anyhow!("service name is empty, not allowed"));
        }

        let mut results: Vec<LogInfo> = vec![];
        let partion = db.open_partition(&dto.sysname, PartitionCreateOptions::default())?;
        for kv in partion.prefix(&dto.loglevel.to_string()) {
            if let Ok(value) = kv {
                info!("key:{:?}", String::from_utf8(value.0.to_vec()));
                info!("value:{:?}", String::from_utf8(value.1.to_vec()));
                if let Ok(logstr) = String::from_utf8(value.1.to_vec()) {
                    if let Ok(loginfo) = serde_json::from_str::<LogInfo>(&logstr) {
                        results.push(loginfo);
                    }
                }
            }
            // info!("kv value: {:?}", &kv);
        }

        Ok(results)
    }

    pub fn clean(db: &Keyspace) -> Result<()> {
        // let collections = db.list_collection_names()?;
        // for col in collections.into_iter() {
        //     let collection = db.collection::<LogInfo>(&col);
        //     let _ = collection.drop();
        // }

        Ok(())
    }

    // pub fn query_loginfos(db: &Database) -> Result<Vec<LogInfo>> {
    //     //https://www.polodb.org/docs/curd/query
    //     let collection = db.collection::<LogInfo>(LOG_COLLECTION);
    //     let books = collection
    //         .find(doc! {
    //             "author": "George Orwell",
    //         })
    //         .run()?;
    //     // Specify AND Conditions
    //     let ret = collection
    //         .find(doc! {
    //             "author": "George Orwell",
    //             "title": "1984",
    //         })
    //         .run()?;
    //     //Pagination
    //     let books = collection.find(doc! {}).skip(1).limit(1).run()?;
    //     //Sort the results
    //     let books = collection
    //         .find(doc! {})
    //         .sort(doc! {
    //             "title": 1,
    //         })
    //         .run()?;
    //     // Specify OR Conditions
    //     // let result = collection
    //     //     .find(doc! {
    //     //         "$or": [
    //     //             {
    //     //                 "title": "The Grapes of Wrath",
    //     //             },
    //     //             {
    //     //                 "title": "To Kill a Mockingbird",
    //     //             }
    //     //         ]
    //     //     })
    //     //     .run()?
    //     //     .collect::<Result<Vec<Document>>>()?;
    //     //Specify EQ and NE Condition
    //     collection
    //         .find(doc! {
    //             "age": { "$eq": 18 },
    //         })
    //         .run()?;
    //     //Specify GT and GTE Conditions
    //     collection
    //         .find(doc! {
    //             "age": { "$gt": 18 },
    //         })
    //         .run()?;
    //     //Specify IN and NIN Conditions
    //     collection
    //         .find(doc! {
    //             "age": { "$in": [18, 19, 20] },
    //         })
    //         .run()?;
    //     //Specify REGEX Condition
    //     // collection.find(doc! {
    //     //     "title": { "regex": polodb_core::bson::Regex {
    //     //         "pattern": "\w*Ring\w*",
    //     //         "options": "i",
    //     //     }}
    //     // }).run()?;
    //     Ok(vec![])
    // }
}
