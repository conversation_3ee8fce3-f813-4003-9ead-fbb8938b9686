mod common;
mod config;
// mod controller;
mod dataservice;
mod protofiles;
mod service;
// mod server;
mod mw;
mod webserver;

use std::{net::SocketAddr, sync::Arc};

use crate::config::settings::Settings;
use ::common::init_tracing;
// use anyhow::Result;
use fjall::{Config, Keyspace, PersistMode};
// use polodb_core::Database;
use service::server::ServerHandler;
// use utility::loggings;
use tracing::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/logcenter.yaml";
    // loggings::log_init(cfg);

    let prefix = "phoenix_logcenter";
    let dir = "./log";
    let level = "INFO";
    let _guard = init_tracing(prefix, dir, level);

    let settings = Settings::new().expect("init configurtion error");
    info!("初始化配置信息:{:#?}", &settings);

    std::fs::create_dir_all(&settings.application.logdir).expect("create uploaddir directory error");
    let dbfile: String = format!("{}/{}", &settings.application.logdir, &settings.application.dbname);
    let db = Config::new(&dbfile).open().expect("open dbfile error");

    // let db = Database::open_path(&dbfile).expect("open logdatas db error:");

    let dbconn = Arc::new(db);
    let dbconn_clone = dbconn.clone();
    let server = prepare(&settings, dbconn_clone).await.expect("Init server error......");
    server_run(server, dbconn, &settings).await
}

async fn prepare(settings: &Settings, dbconn: Arc<Keyspace>) -> anyhow::Result<ServerHandler> {
    let grpc = ServerHandler::new(settings, dbconn).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, dbconn: Arc<Keyspace>, settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    // let addr = "0.0.0.0:60000".parse().unwrap();
    let addr = format!("{}:{}", settings.application.apphost, settings.application.appport).parse().unwrap();
    info!("Starting logcenter service on {}", addr);

    //receive ctrl-c exit signal
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    let keyspace = dbconn.clone();
    //增加http接口
    let http_url = format!("{}:{}", settings.application.apphost, settings.application.httpport);
    let httpaddr: SocketAddr = http_url.as_str().parse().expect("parse httpaddr error");
    // let dbconn = Arc::new(db.clone());
    let arc_setting = Arc::new(settings.clone());
    let app = webserver::routers::create_route(arc_setting, dbconn);

    let listener = tokio::net::TcpListener::bind(&http_url).await.unwrap();

    info!("Starting logcenter http server on {:?}", &httpaddr);
    tokio::spawn(async move {
        axum::serve(listener, app.into_make_service()).await.unwrap();
    });

    tonic::transport::Server::builder()
        .add_service(crate::protofiles::logcenter::log_center_service_server::LogCenterServiceServer::new(svr))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await
        .expect("build server error");

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    keyspace.persist(PersistMode::SyncAll)?;
    info!("Shutted down");
    Ok(())
}
