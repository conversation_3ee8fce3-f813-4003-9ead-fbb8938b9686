use crate::config::settings::Settings;
use crate::dataservice::loginfo::LogInfo;
use crate::protofiles::logcenter::*;
use anyhow::Result;
// use polodb_core::Database;
use fjall::Keyspace;
use std::sync::Arc;
use tracing::*;

// #[derive(Clone)]
pub struct ServerController {
    settings: Arc<Settings>,
    db: Arc<Keyspace>,
    tx_msg: tokio::sync::broadcast::Sender<LogInfo>,
}

//处理业务逻辑
impl ServerController {
    pub fn new(settings: Arc<Settings>, db: Arc<Keyspace>, tx_msg: tokio::sync::broadcast::Sender<LogInfo>) -> Self {
        ServerController { settings, db, tx_msg }
    }
    // pub async fn update_configurations(&self, settings: &Settings) -> Result<()> {
    //     let mut wr = self.settings.write().await;
    //     *wr = settings.to_owned();
    //     Ok(())
    // }
    pub async fn backup_and_clean(&self) -> Result<()> {
        // info!("new configurations:{:#?}", &self.settings.read().await);
        let current_date = utility::timeutil::current_date();
        let current_log_file = format!("{}/{}", self.settings.application.logdir, self.settings.application.dbname);
        let new_log_file = format!("{}/{}.{}", self.settings.application.logdir, self.settings.application.dbname, current_date);

        let ret = crate::common::common::copy_dir_all(current_log_file, new_log_file);
        if ret.as_ref().is_err() {
            error!("back up error......{}", ret.as_ref().unwrap_err().to_string());
        }

        let _ret = crate::dataservice::loginfo::LogInfo::clean(&self.db);

        Ok(())
    }

    pub async fn push_log(&self, req: &OmsLogMessage) -> OmsLogResp {
        info!("请求信息：{:?}", &req);
        let mut result = OmsLogResp { err_code: 0, err_msg: String::from("") };
        let info: LogInfo = LogInfo::new(&req.log_sid, &req.srv_name, req.log_type, req.log_level, &req.log_time, &req.log_content);

        if let Err(err) = self.tx_msg.send(info.clone()) {
            error!("send log error:{}", err.to_string());
        }

        let ret = crate::dataservice::loginfo::LogInfo::insert_loginfo(&info, &self.db);
        if ret.as_ref().is_err() {
            result.err_code = -1;
            result.err_msg = ret.as_ref().unwrap_err().to_string();
        }
        // std::thread::sleep(std::time::Duration::from_secs(2));
        result
    }
}
