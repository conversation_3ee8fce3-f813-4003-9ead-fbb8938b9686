pub use super::controller::*;
use super::TelegramBots;
use crate::{
    config::Settings,
    protofiles::logcenter::{log_center_service_server::*, *},
};

use chrono::Timelike;
use fjall::Keyspace;
use tonic::{self, Request, Response, Status};

use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot};
use tracing::*;

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
    // telebots: TelegramBots,
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings, db: Arc<Keyspace>) -> Self {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(10 as u64));

        let (tx_log, _rx_log) = tokio::sync::broadcast::channel(16);

        let telebots = TelegramBots::new(&settings.telegramchat, tx_log.clone()).await;
        telebots.listen_msg().await;

        // let stub = ServerController::new(db);
        let settings = Arc::new(settings.to_owned());
        let stub = Arc::new(ServerController::new(settings, db, tx_log));

        let (tx, mut rx) = mpsc::channel(16);
        let (tx_close, mut rx_close) = oneshot::channel();

        let stub_clone = stub.clone();
        let stub_for_dispatch = stub.clone();

        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
            // telebots,
        };

        tokio::spawn(async move {
            persist_interval.tick().await; //skip first tick
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        if let Some(task) = may_task{
                            task(stub_for_dispatch.clone()).await;
                        }
                    }
                    _ = persist_interval.tick() => {
                        //如果是晚上11：00-12：00，则进行备份
                        let local: chrono::DateTime<chrono::Local>  = chrono::Local::now();
                        if local.hour() > 23 && local.hour() <= 24 {
                            info!("Start to backup database and clean:{:?}",std::thread::current());
                            let _ = stub_clone.backup_and_clean().await;
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }
            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
            }

            warn!("Server scheduler has exited");
        });

        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl LogCenterService for ServerHandler {
    async fn push_log(&self, request: Request<OmsLogMessage>) -> Result<Response<OmsLogResp>, Status> {
        //注意：以下代码仅作示例
        let ret = self.stub.push_log(&request.into_inner()).await;
        Ok(Response::new(ret))
    }
}
