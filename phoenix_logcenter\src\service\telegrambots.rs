use super::telegramlogger::TelegramLogger;
use crate::{common::constant::LogLevel, config::TelegramChat, dataservice::loginfo::LogInfo};
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct TelegramBots {
    pub recv_msg: tokio::sync::broadcast::Sender<LogInfo>,
    pub teleloggers: HashMap<String, TelegramLogger>,
}

impl TelegramBots {
    pub async fn new(telegrams: &HashMap<String, TelegramChat>, recv_msg: tokio::sync::broadcast::Sender<LogInfo>) -> Self {
        let mut teleloggers = HashMap::new();

        for (key, val) in telegrams.iter() {
            let telegramlogger = TelegramLogger::new(val.token.as_str(), val.chatid).await;
            teleloggers.insert(key.to_uppercase(), telegramlogger);
        }

        Self { recv_msg, teleloggers }
    }

    pub async fn listen_msg(&self) {
        let mut rx = self.recv_msg.subscribe();
        let teleloggers = self.teleloggers.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    task = rx.recv() => {
                        if let Ok(msg) = task {

                            // let logger = teleloggers.get(&LogLevel::from_i32(msg.loglevel).to_string()).map_or(teleloggers.get(&LogLevel::Debug.to_string()).unwrap(), |logger| logger);
                            if let Some(logger) = teleloggers.get(&LogLevel::from_i32(msg.loglevel).to_string()).or_else(|| teleloggers.get(&LogLevel::Debug.to_string())) {
                                logger.send_msg(&msg).await;
                            }
                            // if let Some(logger) = teleloggers.get(&LogLevel::from_i32(msg.loglevel).to_string()) {
                            //     logger.send_msg(&msg).await;
                            // } else if let Some(logger) = teleloggers.get(&LogLevel::Debug.to_string()) {
                            //     logger.send_msg(&msg).await;
                            // }
                        }
                    }
                }
            }
        });
    }
}
