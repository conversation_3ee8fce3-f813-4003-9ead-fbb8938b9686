// use teloxide::dispatching::{dialogue, dialogue::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Update<PERSON><PERSON>erEx<PERSON>, UpdateHand<PERSON>};
// use teloxide::prelude::*;
// use teloxide::requests::RequesterExt;
// use teloxide::types::Message;
// use teloxide::types::Update;
// use teloxide::utils::command::{self, BotCommands};
// use teloxide::Bot;
// use teloxide::{prelude::*, types::User};

// pub type Error = Box<dyn std::error::Error + Send + Sync>;

// async fn run() {
//     // 7261914946:AAFwM6WEBMEgKEQ8LpQB1FC2siYSuEc-_m4
//     info!("Starting teloxide-bot...");

//     // 将 YOUR_BOT_TOKEN 替换为你的实际的机器人 token
//     let bot = Bot::new("7773697898:AAHqkp81O9CpMsAsIPIC3NI8qQT0KEzAnEs");
//     info!("bot: {:?}", bot);
//     info!("bot up: {:#?}", bot.get_updates().await.unwrap());

//     info!("{:#?}", bot.get_me().await.unwrap());
//     let me = bot.get_me().await.unwrap();
//     let user = me.user;
//     let c = bot.get_chat(user.id).await.unwrap();
//     info!("{:#?}", c);
//     let cat = ChatId(-4664310419);
//     for i in 0..10 {
//         tokio::time::sleep(std::time::Duration::from_secs(2)).await;
//         let _ = bot.send_message(cat, format!("hello world: {i}")).await;
//     }

//     let schema = Update::filter_message()
//         .filter_map(|update: Update| update.from().cloned())
//         .branch(Message::filter_text().endpoint(process_text_message));
//     Dispatcher::builder(bot, schema).build().dispatch().await;

//     // teloxide::repl(bot, |bot: Bot, msg: Message| async move {
//     //     info!("{:?}", msg);

//     //     bot.send_dice(msg.chat.id).await?;

//     //     Ok(())
//     // })
//     // .await;
// }

// async fn process_text_message(bot: Bot, user: User, message_text: String) -> Result<(), Error> {
//     /*
//         The id of a chat with a user is the same as his telegram_id
//         from the bot's perspective.

//         Injected dependencies:
//         - Bot is provided by the Dispatcher::dispatch
//         - User is provided by the (1)
//         - String is provided by the (2)
//     */
//     tokio::spawn(async move {
//         loop {
//             let s = "hello world";
//         }
//     });
//     info!("User {:?} sent: {message_text}", user);
//     let _ = bot.send_message(user.id, format!("Hi! You sent: {message_text}"));
//     Ok(())
// }

// use std::collections::HashMap;

use crate::common::constant::LogLevel;
use crate::dataservice::loginfo::LogInfo;
use teloxide::prelude::*;
use teloxide::types::ParseMode;
// use teloxide::utils::html::escape;
use teloxide::Bot;
use tracing::*;

#[derive(Debug, Clone)]
pub struct TelegramLogger {
    pub bot: Bot,
    // pub chatid: HashMap<String, i64>,
    pub _token: String,
    pub chatid: i64,
}

impl TelegramLogger {
    pub async fn new(token: &str, chatid: i64) -> Self {
        // let bot = Bot::new(token);
        // teloxide::repl(bot, |bot: Bot, msg: Message| async move {
        //     info!("{:#?}", msg);

        //     bot.send_dice(msg.chat.id).await?;

        //     Ok(())
        // })
        // .await;
        let bot = Bot::new(token);
        // let chatid = ChatId(chatid);
        Self { bot, chatid, _token: token.to_owned() }
    }

    pub async fn send_msg(&self, req: &LogInfo) {
        let chatid = ChatId(self.chatid);

        let log_level = LogLevel::from_i32(req.loglevel);
        let log_level_str = match log_level {
            LogLevel::Debug => format!("🟦 <b>{}</b>", log_level.to_string()),
            LogLevel::Info => format!("🟩 <b>{}</b>", log_level.to_string()),
            LogLevel::Warn => format!("🟧 <b>{}</b>", log_level.to_string()),
            LogLevel::Error => format!("🟥 <b>{}</b>", log_level.to_string()),
        };

        // 使用 HTML 格式化消息
        let log = format!(
            "<b>IP:</b> {}\n\
            <b>服务:</b> {}\n\
            <b>时间:</b> {}\n\
            <b>类型:</b> {}\n\
            <b>级别:</b> {}\n\
            <b>内容:</b> {}",
            teloxide::utils::html::escape(&req.sid),
            teloxide::utils::html::escape(&req.srvname),
            teloxide::utils::html::escape(&req.logtime),
            if req.logtype == 1 { "系统监控日志" } else { "业务日志" },
            log_level_str,
            teloxide::utils::html::escape(&req.logcontent)
        );

        if let Err(err) = self.bot.send_message(chatid, log).parse_mode(ParseMode::Html).await {
            error!("Error sending message : {:?}", err);
        };
    }
}
