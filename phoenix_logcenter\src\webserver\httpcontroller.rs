use crate::config::Settings;
use crate::dataservice::loginfo::LogInfo;

use super::dto::*;
use super::httpresponse::*;
use axum::{Extension, Json};
use fjall::Config;
use fjall::Keyspace;
// use polodb_core::Database;
use serde_json::Value;
use std::sync::Arc;
use tracing::*;

pub async fn test_controller() -> Json<Value> {
    response_json_value_ok(1, "")
}

pub async fn query_loginfos(Extension(db): Extension<Arc<Keyspace>>, Extension(settings): Extension<Arc<Settings>>, Json(dto): Json<QueryDto>) -> Json<Value> {
    info!("Dto is:{:?}", &dto);
    let empty: Vec<LogInfo> = vec![];

    if dto.sysname.is_empty() {
        return response_json_value_error("please choose system name", empty);
    }

    let mut db = db.to_owned();
    let current_date = utility::timeutil::current_date();
    if dto.logdate != current_date {
        info!("query old logs for:{}", dto.logdate);
        let new_log_file = format!("{}/{}.{}", settings.application.logdir, settings.application.dbname, current_date);

        if let Ok(keyspace) = Config::new(&new_log_file).open() {
            db = Arc::new(keyspace);
        }
    }
    let ret = LogInfo::query_loginfos(&dto, &db);
    if ret.as_ref().is_err() {
        return response_json_value_error(&ret.unwrap_err().to_string(), empty);
    }

    if ret.as_ref().is_err() {
        return response_json_value_error(&ret.unwrap_err().to_string(), empty);
    }
    let ret = ret.unwrap();
    response_json_value_ok(ret.len() as u64, ret)
    // response_json_value_ok(0, 0)
}
pub async fn query_serviceinfos(Extension(db): Extension<Arc<Keyspace>>, Extension(_settings): Extension<Arc<Settings>>) -> Json<Value> {
    let ret = crate::dataservice::loginfo::LogInfo::query_serviceinfos(&db);
    if ret.as_ref().is_err() {
        let empty: Vec<String> = vec![];
        return response_json_value_error(&ret.unwrap_err().to_string(), empty);
    }
    let ret = ret.unwrap();
    response_json_value_ok(ret.len() as u64, ret)
}
