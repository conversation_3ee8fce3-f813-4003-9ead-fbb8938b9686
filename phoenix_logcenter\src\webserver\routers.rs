use axum::{extract::Extension, http::Method, middleware, routing::post, Router};
use fjall::Keyspace;
// use polodb_core::Database;
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::cors::{Any, CorsLayer};

use crate::{config::Settings, mw::printreq::print_request_response};
pub fn create_route(setting: Arc<Settings>, db: Arc<Keyspace>) -> Router {
    let cors = CorsLayer::new()
        // allow `GET` and `POST` when accessing the resource
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::OPTIONS, Method::DELETE])
        // allow requests from any origin
        .allow_origin(Any)
        .allow_headers(Any);

    let v1_route = create_v1_route();

    // let dbconn = Arc::new(db.clone());
    let app = Router::new()
        .nest("/api", v1_route)
        .layer(middleware::from_fn(print_request_response))
        .layer(ServiceBuilder::new().layer(Extension(db)))
        .layer(ServiceBuilder::new().layer(Extension(setting)))
        .route_layer(cors);

    app
}

fn create_v1_route() -> Router {
    let route = Router::new()
        .route("/log", post(super::httpcontroller::query_loginfos))
        .route("/service", post(super::httpcontroller::query_serviceinfos))
        .route("/login", post(super::httpcontroller::test_controller));

    let app_route = Router::new().merge(route);

    let v1_route = Router::new().nest("/v1", app_route);
    v1_route
}
