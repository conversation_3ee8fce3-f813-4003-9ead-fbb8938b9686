[package]
name = "phoenix_zfix"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# utility = { path = "../utility" }
common = { workspace = true }

config = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
# notify = "5"
# rust_decimal = "^1"
# rust_decimal_macros = "^1"

anyhow = { workspace = true }
thiserror = { workspace = true }

# log = { workspace = true }
# log4rs = { workspace = true }
tracing = { workspace = true }

[build-dependencies]
# tonic-build = { version = "0.12.3", features = ["prost"] }
tonic-build.workspace = true
