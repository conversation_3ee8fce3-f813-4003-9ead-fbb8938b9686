fn build_grpc() {
    tonic_build::configure()
        .out_dir("src/protofiles")
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(
            &[
                "../shared/protoes/protofiles/accountriskcenter.proto",
                "../shared/protoes/protofiles/settlecenter.proto",
                "../shared/protoes/protofiles/ordercenter.proto",
            ],
            &["../shared/protoes/protofiles"],
        )
        .unwrap();
}

fn main() {
    build_grpc();
}
