use std::collections::BTreeMap;

use config::{Config, ConfigError, File};
use serde::Deserialize;

#[derive(Debug, Clone, Deserialize)]
pub struct Accountrisk {
    pub unit_id: i64,
    pub cur_date: i64,
    pub before_date: i64,
    pub rate_buy: f64,
    pub rate_sell: f64,
}

//resettle
#[derive(Debu<PERSON>, <PERSON>lone, Deserialize)]
pub struct Resettle {
    pub market_type: i32,
    pub unit_id: i32,
    pub sys_date: i32,
    pub next_trade_date: i32,
    pub currency: i32, //1:hkd 2:cny 3:usd
    pub buy_rate: f64,
    pub sell_rate: f64,
}

//resettle
#[derive(Debug, Clone, Deserialize)]
pub struct Order {
    pub msg_id: i64,
    /// 子账户id
    pub unit_id: i64,
    /// 证券id
    pub stock_id: i64,
    /// 委托方向  1=买  2=卖
    pub order_direction: i32,
    /// 订单数量
    pub order_qty: i32,
    /// 价格类型(市价限价) 1=市价 2=限价
    pub price_type: i32,
    /// 委托价格
    pub order_price: f64,
    /// 操作员
    pub operator_no: i64,
    /// 委托类型 1:app下单  2:跟单  3:风控止盈止损平仓单,4:风控总资产预警平仓单 5:pc客户端单 6:结算平仓单 7:管理端强平仓单,8:app清仓,9:pc清仓,10,管理员平仓,11,合约到期日强平,12,算法单
    pub order_type: i32,
    /// 1:USER(用户直连) 2:AGENT(代理托管)
    pub trade_mode: i32,
    /// 代理账户
    pub agent_account: i64,
    /// 算法单id(普通0)
    pub algorithm_id: i64,
    /// 用户id
    pub user_id: i64,
}
#[derive(Debug, Clone, Deserialize)]
pub struct Subinfo {
    pub channel_id: i64,
    pub channel_type: i32,
    pub order_amount: i32,
}

#[derive(Debug, Clone, Deserialize)]
pub struct ReplenishOrder {
    pub order: Order,
    pub subinfo: Vec<Subinfo>,
    pub to_unit_id: i64,
    pub to_user_id: i64,
    pub trade_time: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct System {
    pub server: String,
    pub fixtype: i32,
}

#[derive(Debug, Clone, Deserialize)]
pub struct Settings {
    pub accountrisk: BTreeMap<i32, Accountrisk>,
    pub resettle: BTreeMap<i32, Resettle>,
    pub system: System,
    pub replenishorder: ReplenishOrder,
}

impl Settings {
    pub fn new() -> Result<Self, ConfigError> {
        // let config_file = "config/riskcenter";
        let s = Config::builder()
            // Start off by merging in the "default" configuration file
            .add_source(File::with_name("config/zdatafix.toml"))
            // Add in the current environment file
            // Default to 'development' env
            // Note that this file is _optional_
            //.add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
            // Add in a local configuration file
            // This file shouldn't be checked in to git
            //.add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
            // Add in settings from the environment (with a prefix of APP)
            // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
            //.add_source(Environment::with_prefix("app"))
            // You may also programmatically change settings
            //.set_override("database.url", "postgres://")?
            .build()
            .expect("build configuration error");

        // You can deserialize (and thus freeze) the entire configuration as
        s.try_deserialize()
    }

    // pub fn watch(&mut self) {
    //     // Create a channel to receive the events.
    //     let (tx, rx) = channel();
    //     // Automatically select the best implementation for your platform.
    //     // You can also access each implementation directly e.g. INotifyWatcher.
    //     let mut watcher: RecommendedWatcher = Watcher::new(tx, Duration::from_secs(3)).expect("create configuration watch error");

    //     // Add a path to be watched. All files and directories at that path and
    //     // below will be monitored for changes.
    //     watcher
    //         .watch("config/riskcenter", RecursiveMode::NonRecursive)
    //         .expect("watch error");

    //     // This is a simple loop, but you may want to use more complex logic here,
    //     // for example to handle I/O.
    //     loop {
    //         match rx.recv() {
    //             Ok(DebouncedEvent::Write(_)) => {
    //                 println!(" * configuration written; refreshing configuration ...");
    //                 self.refresh().unwrap();
    //                 // show();
    //             }

    //             Err(e) => println!("watch error: {:?}", e),

    //             _ => {
    //                 // Ignore event
    //             }
    //         }
    //     }
    // }
}
