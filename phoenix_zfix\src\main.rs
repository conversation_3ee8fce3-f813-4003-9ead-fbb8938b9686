mod config;
mod protofiles;
mod service;
// mod service;

use crate::config::settings::Settings;
use anyhow::Result;
use service::controller::ServerController;
use tracing::*;
// use server::ServerHandler;
// use utility::loggings;
// use protofiles::phoenixaccountriskcenter::phoenix_riskcenter_server::PhoenixRiskcenterServer;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // loggings::log_init();
    // log4rs::init_file("config/zdatafix.yaml", Default::default()).unwrap();

    let prefix = "phoenix_assetscenter";
    let dir = "./log";
    let level = "INFO";
    let _guard = common::init_tracing(prefix, dir, level);

    let settings = Settings::new().expect("init configurtion error");
    info!("初始化配置信息:{:#?}", &settings);

    let controller = ServerController::new(&settings).await?;
    // info!("ServerController result:{:?}", &controller);

    let result = controller.fix_data().await?;
    info!("computer result:{:?}", &result);

    // let server = prepare(&settings).await.expect("Init server error......");

    // info!("开始启动系统...");
    // server_run(server, &settings).await
    Ok(())
}

// async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
//     // let grpc_stub = create_controller(settings).await;

//     let grpc = ServerHandler::new(&settings).await;

//     Ok(grpc)
// }

// async fn server_run(mut svr: ServerHandler, settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {

//     Ok(())
// }
