use crate::config::settings::Settings;
use crate::protofiles::phoenixordercenter::{OrderReq, ReplenishOrderReq, Riskinfo};
use crate::protofiles::phoenixsettlecenter::settlecenter_service_client::SettlecenterServiceClient;
use crate::protofiles::phoenixsettlecenter::{ExchangeRate, ReSettleDetail, ReSettleReq};
use crate::protofiles::{phoenixaccountriskcenter::*, phoenixordercenter::order_center_service_client::OrderCenterServiceClient};
use account_risk_center_client::AccountRiskCenterClient;
use anyhow::Result;
use tokio::sync::RwLock;
use tracing::*;

#[derive(Debug)]
pub struct ServerController {
    settings: Settings,
    accountclient: Option<RwLock<AccountRiskCenterClient<tonic::transport::Channel>>>,
    settleclient: Option<RwLock<SettlecenterServiceClient<tonic::transport::Channel>>>,
    orderclient: Option<RwLock<OrderCenterServiceClient<tonic::transport::Channel>>>,
}

//处理业务逻辑
impl ServerController {
    pub async fn new(settings: &Settings) -> Result<Self> {
        if settings.system.server == "" {
            return Err(anyhow::anyhow!("server url is empty"));
        }

        // if settings.request.is_empty() {
        //     return Err(anyhow::anyhow!("request is empty"));
        // }
        let server_uri = settings.system.server.clone();

        match settings.system.fixtype {
            val if val == super::constant::FixType::Accountrisk as i32 => {
                if settings.accountrisk.is_empty() {
                    return Err(anyhow::anyhow!("account risk parameters are empty"));
                }
                let client = AccountRiskCenterClient::connect(server_uri).await.unwrap();
                Ok(ServerController {
                    settings: settings.clone(),
                    accountclient: Some(RwLock::new(client)),
                    settleclient: None,
                    orderclient: None,
                })
            }
            val if val == super::constant::FixType::Resettle as i32 => {
                if settings.resettle.is_empty() {
                    return Err(anyhow::anyhow!("resettle parameters are empty"));
                }
                let client = SettlecenterServiceClient::connect(server_uri).await.unwrap();
                Ok(ServerController {
                    settings: settings.clone(),
                    settleclient: Some(RwLock::new(client)),
                    accountclient: None,
                    orderclient: None,
                })
            }
            val if val == super::constant::FixType::Currentorder as i32 => {
                // if settings.order.is_empty() {
                //     return Err(anyhow::anyhow!("order parameters are empty"));
                // }
                let client = OrderCenterServiceClient::connect(server_uri).await.unwrap();
                Ok(ServerController {
                    settings: settings.clone(),
                    settleclient: None,
                    accountclient: None,
                    orderclient: Some(RwLock::new(client)),
                })
            }
            _ => return Err(anyhow::anyhow!("fixtype is not support")),
        }
    }

    pub async fn fix_data(&self) -> Result<String> {
        match self.settings.system.fixtype {
            val if val == super::constant::FixType::Accountrisk as i32 => self.phoenix_computer_assets_from_deal().await,
            val if val == super::constant::FixType::Resettle as i32 => self.resettle().await,
            val if val == super::constant::FixType::Currentorder as i32 => self.fix_current_order().await,
            _ => Err(anyhow::anyhow!("fixtype is not support")),
        }
    }

    async fn phoenix_computer_assets_from_deal(&self) -> Result<String> {
        if self.settings.system.server == "" {
            return Err(anyhow::anyhow!("server url is empty"));
        }

        if self.settings.accountrisk.is_empty() {
            return Err(anyhow::anyhow!("request is empty"));
        }
        if self.accountclient.is_none() {
            return Err(anyhow::anyhow!("accountclient is none"));
        }
        let accountclient = self.accountclient.as_ref().unwrap();
        for (k, v) in &self.settings.accountrisk {
            info!("{}: {:?}", k, v);
            let request = PhoenixAssetscomputeRequest {
                unit_id: v.unit_id,
                cur_date: v.cur_date,
                before_date: v.before_date,
                rate_buy: v.rate_buy,
                rate_sell: v.rate_sell,
            };
            let mut client = accountclient.try_write().unwrap();
            let result = client.compute_assets_from_deal(request).await?;
            info!("Result: {:?}", result);
        }

        Ok("Done".to_string())
    }

    async fn fix_current_order(&self) -> Result<String> {
        if self.orderclient.is_none() {
            return Err(anyhow::anyhow!("order client is none"));
        }
        let orderclient = self.orderclient.as_ref().unwrap();
        let mut client = orderclient.try_write().unwrap();
        let order = OrderReq {
            msg_id: self.settings.replenishorder.order.msg_id,
            unit_id: self.settings.replenishorder.order.unit_id,
            stock_id: self.settings.replenishorder.order.stock_id,
            order_direction: self.settings.replenishorder.order.order_direction,
            order_qty: self.settings.replenishorder.order.order_qty,
            price_type: self.settings.replenishorder.order.price_type,
            order_price: self.settings.replenishorder.order.order_price,
            operator_no: self.settings.replenishorder.order.operator_no,
            order_type: self.settings.replenishorder.order.order_type,
            trade_mode: self.settings.replenishorder.order.trade_mode,
            agent_account: self.settings.replenishorder.order.agent_account,
            algorithm_id: self.settings.replenishorder.order.algorithm_id,
            user_id: self.settings.replenishorder.order.user_id,
        };
        // let risk = Riskinfo {
        //     channel_id: self.settings.replenishorder.subinfo.
        // }
        let mut risk = Vec::new();
        for subinfo in self.settings.replenishorder.subinfo.iter() {
            let risk_info = Riskinfo {
                channel_id: subinfo.channel_id,
                channel_type: subinfo.channel_type,
                order_amount: subinfo.order_amount,
            };
            risk.push(risk_info);
        }
        let request = ReplenishOrderReq {
            order: Some(order),
            riskinfo: risk,
            to_unit_id: self.settings.replenishorder.to_unit_id,
            to_user_id: self.settings.replenishorder.to_user_id,
            trade_time: self.settings.replenishorder.trade_time.clone(),
        };
        info!("Request: {:#?}", request);
        let result = client.replenishment_order(request).await?;
        info!("Result: {:?}", result);
        Ok("".to_string())
    }

    async fn resettle(&self) -> Result<String> {
        if self.settings.system.server == "" {
            return Err(anyhow::anyhow!("server url is empty"));
        }

        if self.settings.resettle.is_empty() {
            return Err(anyhow::anyhow!("resettle is empty"));
        }

        if self.settleclient.is_none() {
            return Err(anyhow::anyhow!("settle client is none"));
        }

        let settleclient = self.settleclient.as_ref().unwrap();
        for (k, v) in &self.settings.resettle {
            info!("{}: {:?}", k, v);
            let date_data = ExchangeRate {
                currency: v.currency,
                buy_rate: v.buy_rate,
                sell_rate: v.sell_rate,
            };
            let detail = ReSettleDetail {
                market_type: v.market_type,
                unit_id: v.unit_id,
                sys_date: v.sys_date,
                next_trade_date: v.next_trade_date,
                rate_data: Some(date_data),
            };
            let mut client = settleclient.try_write().unwrap();
            let request = ReSettleReq { re_settles: vec![detail] };
            let result = client.re_settle(request).await?;
            info!("Result: {:?}", result);
        }
        Ok("".to_string())
    }
}
