pub use super::controller::*;

use crate::config::settings::Settings;
use tonic::{self, Request, Response, Status};
// use utility::constant::MessageType;
// use crate::protofiles::phoenixriskcenter::phoenix_riskcenter_server::PhoenixRiskcenter;
use crate::protofiles::phoenixriskcenter::{phoenix_riskcenter_server::PhoenixRiskcenter, PhoenixRiskCheckRequest, PhoenixRiskCheckResponse, PhoenixRiskRequest, PhoenixRiskResponse};
use futures::{
    channel::mpsc::{channel, Receiver},
    SinkExt, StreamExt,
};
use std::path::Path;
// use std::fmt::Debug;
use std::sync::Arc;

use tokio::sync::{mpsc, oneshot, RwLock};

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    set_close: Option<oneshot::Sender<()>>,
    // mqclient: QuotationClient,
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

// impl ServerHandler {
//     pub async fn new(settings: &Settings) -> Self {

// }
// }
//这里实现grpc的接口
#[tonic::async_trait]
impl PhoenixRiskcenter for ServerHandler {
    async fn phoenix_risk_check(&self, request: Request<PhoenixRiskCheckRequest>) -> Result<Response<PhoenixRiskCheckResponse>, Status> {
        //注意：以下代码仅作示例
        let ret = self.stub.phoenix_risk_check(&request.into_inner()).await;
        Ok(Response::new(ret.unwrap()))
    }

    async fn phoenix_risk_test(&self, request: Request<PhoenixRiskRequest>) -> Result<Response<PhoenixRiskResponse>, Status> {
        //注意：以下代码仅作示例
        let ret = self.stub.phoenix_risk_test(&request.into_inner()).await;
        Ok(Response::new(ret.unwrap()))
    }
}
