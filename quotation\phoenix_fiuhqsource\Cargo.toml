[package]
name = "phoenix_fiuhqsource"
version = "0.2.0"
edition = "2021"
description = "融聚汇数据源 build time: 2025-07-03"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
common = { workspace = true }
protoes = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
# async-stream = { workspace = true }
tonic = { workspace = true }
tokio-util = { workspace = true }
futures-util = { workspace = true }
futures = { workspace = true }
futures-lite = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
tokio-tungstenite = { workspace = true }
config = { workspace = true }
anyhow = { workspace = true }
byteorder = { workspace = true }
lazy_static = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
# serde_yaml = { workspace = true }
chrono = { workspace = true }
reqwest-websocket = { workspace = true }
reqwest = { workspace = true }
sea-orm = { workspace = true }
rust_decimal = { workspace = true }
base64 = { workspace = true }
tracing = { workspace = true }
[build-dependencies]
tonic-build = { workspace = true }

[[bin]]
name = "realtime"
path = "src/test.rs"
