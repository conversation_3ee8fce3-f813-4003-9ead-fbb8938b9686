fn build_grpc() {
    tonic_build::configure()
        .out_dir("src/protofiles")
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        // .compile_protos(&["proto/hs.proto", "proto/hk.proto", "proto/hqmsg.proto", "proto/marketdata.proto", "proto/fiuweb.proto"], &["proto"])
        .compile_protos(&["proto/hs.proto", "proto/hk.proto", "proto/fiuweb.proto"], &["proto"])
        .unwrap();
}

fn main() {
    build_grpc();
}
