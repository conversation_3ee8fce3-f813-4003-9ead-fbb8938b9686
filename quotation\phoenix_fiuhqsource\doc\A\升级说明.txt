V2.0升级说明：
1，码表协议codeteble增加字段：证券子类型、交易货币、产品状态、昨收价以及日期；
2，增加科创板盘后交易协议kcclose；
3，修复个别指数成交量成交额无数据的问题;
4，指数成交总量统一单位为：手。

V2.1升级说明：
1，修复股票小数位问题。


V2.2升级说明：
1，修复上海B股小数位问题。
2，修改科创板盘后协议为：科创板、创业板盘后协议（kcclose）
3，kcclose协议增加市场字段：market


V2.3升级说明：
1，股指期货价格精度由3位小数改为4位小数。


V2.4升级说明：
1，修复重启接收程序后一笔成交额异常的问题


V2.5升级说明：
1,protobuf格式增加一个消息id=111，收到该消息标识码表发送完毕。

V2.6升级说明-20201016：
1，码表增加字段hkflag：中华通标记，N否Y是S只可卖

V2.7升级说明-20201021：
1，修复中途重连后的type问题。

V2.8升级说明-20201105：
1，优化pb版本多个客户端连接。

V2.9升级说明-20210302：
1，增加沪深南向额度剩余数据协议。

V2.10升级说明-20210305：
1，调整沪深南向额度剩余数据小数位精度。

V2.11升级说明-20210623：
1，snapshot快照/kcclose盘后协议分别增加现量字段lastqty。

V2.12升级说明-20210921：
1，修复码表协议中子类型返回值问题
2，码表协议增加涨停价和跌停价字段

V2.13升级说明-20220311：
1，允许推送多次码表
2，优化双路码表字段的合并机制。


V2.14升级说明-20220323：
1，csv码表日志打印昨收价

V2.15升级说明-20220816：
1，pb格式支持上海可转债价格3位小数
2，pb格式从proto2升级为proto3

V2.20升级说明-20230627
1，pb和json格式的order协议增加证券类型type字段