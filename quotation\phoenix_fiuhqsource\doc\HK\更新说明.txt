更新说明：

版本V2.1更新说明-20201228
1）优化股票码表name字段的展示。


版本V2.2更新说明-20210121
1）去除码表协议中的市场代号字段返回值中的空格。
2）修复指数0105000的开盘价。
3）股票快照协议增加pflag字段。

版本V2.3更新说明-20210226
1）增加缓存securitystatus协议功能：每次重连可获取全量停复牌信息。

版本V2.4更新说明-20210304
1）调整码表部分字段的数值单位为1：涉及字段有accured，couponrate，counversionratio， strikeprice1， strikeprice2， posupper， poslower

版本V2.5更新说明-20210331
1）优化防止出现非结尾符\0
2）优化snapshot协议的现价机制
3）增加支持protobuf协议功能(公测中)

版本V2.6更新说明-20210407
1）修复config文件的protocol参数设置问题引起无法启动。


版本V2.7更新说明-20210412
1）修复protobuf中的trade协议发送


版本V2.8更新说明-20210427
1）恒生指数9点30分之前的last取当天的open

版本V2.9更新说明-20210605
1) 过滤恒生指数上午时段的close
2）完善多个连接的重连推送机制
3）完善客户端连接断开后的报错机制

版本V2.10更新说明-20210615
1) 修改指数收盘时刻last的取值为close

版本V2.11更新说明-20210923
1) 缓存trading_session_status交易时段状态协议
2) 修复参考价refprice协议的最高最低价格的返回值


版本V2.12更新说明-20211019
1)优化0000100恒生指数的行情时间取值机制


版本V2.13更新说明-20211102
1)优化trading_session_status协议缓存机制


版本V2.14更新说明-20211122
1)修正pb版本的恒生指数之外指数的成交额数据

版本V2.15更新说明-20220224
1）重启后5分钟未收到码表则切到备份站点。



版本V2.16更新说明-20220511
1）增加碎股挂单协议(json格式和pb格式）
2）码表协议增加：繁体名称和标的物代码（json格式和pb格式）
3）挂单协议FIU_ORDER新增repeated askvec,bidvec（仅pb格式）
4）心跳机制调整
5）从v1.6版本开始proto2改为proto3

版本V2.17更新说明-20220602
1）json格式指数价格由支持3位小数改为4位小数
2）pb格式数据类型更改：float改为double

版本V2.18更新说明-20220629
1）优化重启机制

版本V2.19更新说明-20220720
1）修改委托挂单量超过21亿股为负数的问题。

版本V2.20更新说明-20220810
1）pb格式和json格式都增加板块成交总额协议

版本V2.21更新说明-20220915
1）解决重连时板块成交总额协议的发送数量大的问题

版本v2.22更新说明-20221130
1）重连可获取broker快照

版本v2.23更新说明-20231114
1) PBOrderBroker调整结构，其中字段: string detail = 2;增加配置参数newpb控制
2) PBOrder字段:string asklist = 3; string bidlist = 4; 增加配置参数newpb控制
3) PB版本价格由小数改成整数
4) config配置增加可配是否打印全量日志
5) config配置增加参数newpb控制PBOrderBroker和PBOrder内容展示



