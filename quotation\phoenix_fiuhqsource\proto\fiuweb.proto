syntax = "proto3";
package fiuwebsocket;

//订阅
message SubscribeMsg {
    int32 command = 1;                  // 协议类型 订阅快照: 1 取消订阅快照: 2 订阅order: 5
                                        // 取消订阅order: 6 订阅扩展行情: 18 取消订阅扩展行情: 19
                                        // 订阅指数行情: 20 取消订阅指数行情: 21
    string version = 2;                 // 协议版本号 1.0.0
    string token = 3;                   // 权限控制 szfiu
    repeated string symbols = 4;        // 证券代码 [“600519.sh”]
    int32 timeMode = 5;                 // 快照时间模式：0：实时，1：延时
}

message Connect {
    string protocol = 1;            //协议类型 1017
    string Identify = 2;            //连接标识
}

message RespMsg {
    string protocol = 1;            //协议类型 1006
    string code = 2;                 //返回代码 200：成功
    string msg = 3;                 //返回消息
}

//A股快照
message HsSnapshot
{
    string protocol = 1;        //协议类型 1001
    string symbol = 2;          //证券代码
    string time = 3;            //行情时间 yyyy-MM-dd HH:mm:ss
    double preClose = 4;        //昨收价
    double open = 5;            //开盘价
    double high = 6;            //最高价
    double low = 7;             //最低价
    double close = 8;           //收盘价
    double last = 9;            //最新价
    int64 volume = 10;          //成交量
    double amount = 11;         //成交额
    double change = 12;         //涨跌额
    double changeRate = 13;     //涨跌幅
    double amplitude = 14;      //振幅
    double avgPrice = 15;       //平均价
}

message HsOrderItem {
    double price = 1;           //挂单价格
    int64 volume = 2;           //买入成交量
}

//A股挂单
message HsOrder {
    string protocol = 1;                    //协议类型 1002
    string time = 2;                        //时间 yyyy-MM-dd HH:mm:ss
    string symbol = 3;                      //证券代码
    repeated HsOrderItem bidList = 4;       //买单序列
    repeated HsOrderItem askList = 5;       //卖单序列
}

//A股扩展行情
message HsExtendSnap
{
    string protocol = 1;                    //协议类型 1010
    string symbol = 2;                      //证券代码
    string name = 3;                        //证券名称
    string time = 4;                        //行情时间 yyyy-MM-dd HH:mm:ss
    int64 totalShares = 5;                  //总股本
    double totalMarketValue = 6;            //总市值
    int64 circulationShares = 7;            //流通股本
    double circulationMarketValue = 8;      //流通市值
    double epsLyr = 9;                      //每股盈利(静态)
    double epsTtm = 10;                     //每股盈利(ttm)
    double nav = 11;                        //每股净资产
    double totalAsset = 12;                 //资产总额
    double totalLiabilities = 13;           //总负债
    double dpsLyr = 14;                     //股息(静态)
    double dpsTtm = 15;                     //股息(ttm)
    double dividendRateLyr = 16;            //股息率(静态)
    double dividendRateTtm = 17;            //股息率(ttm)
    double perLyr = 18;                     //市盈率(静态)
    double perTtm = 19;                     //市盈率(ttm)
    double perForecast = 20;                //市盈率(预测)
    double turnoverRate = 21;               //换手率
    double bidAskRate = 22;                 //委比
    double bidAskDiff = 23;                 //委差
    double volumeRate = 24;                 //量比
    double week52High = 25;                 //52周最高
    double week52Low = 26;                  //52周最低
    double historyHigh = 27;                //历史最高
    double historyLow = 28;                 //历史最低
    double netProfit = 29;                  //净利润(元)
    double pbr = 30;                        //市净率
    double peg = 31;                        //市盈率相对盈利增长比率
    double roe = 32;                        //净资产收益率
    double grossProfitRate = 33;            //毛利率
    double profitMargin = 34;               //净利润率
    double premium = 35;                    //溢价
    double ffEpsReported = 36;              //每股净值
    string underlying = 37;                 //资产类别
    string tradingCurrency = 38;            //交易货币
    double limitUp = 39;                    //涨停价
    double limitDown = 40;                  //跌停价
    double ask = 41;                        //内盘
    double bid = 42;                        //外盘
}

//A股指数
message HsIndexQuote {
    string protocol = 1;                    //协议类型 1011
    string symbol = 2;                      //证券代码
    string name = 3;                        //证券名称
    string time = 4;                        //行情时间 yyyy-MM-dd HH:mm:ss
    double open = 5;                        //开盘价
    double high = 6;                        //最高价
    double low = 7;                         //最低价
    double close = 8;                       //收盘价
    double preClose = 9;                    //昨收价
    int64 volume = 10;                      //成交量
    double amount = 11;                     //成交额
    double change = 12;                     //涨跌额
    double changeRate = 13;                 //涨跌幅
    double amplitude = 14;                  //振幅
    double week52High = 15;                 //52周最高
    double week52Low = 16;                  //52周最低
    int64 rise = 17;                        //涨家数
    int64 flat = 18;                        //平家数
    int64 fall = 19;                        //跌家数
    double totalMarketValue = 20;           //总市值
    double perTtm = 21;                     //市盈率TTM
    double avgPrice = 22;                   //平均价
    double circulationMarketValue = 23;     //流通总市值
    double perLyr = 24;                     //静态市盈率
    double turnoverRate = 25;               //换手率
    double last = 26;                       //最新价
}


//港股快照
message HkSnapshot
{
    string protocol = 1;        //协议类型 1001
    string symbol = 2;          //证券代码
    string time = 3;            //行情时间 yyyy-MM-dd HH:mm:ss
    double preClose = 4;        //昨收价
    double open = 5;            //开盘价
    double high = 6;            //最高价
    double low = 7;             //最低价
    double close = 8;           //收盘价
    double last = 9;            //最新价
    int64 volume = 10;          //成交量
    double amount = 11;         //成交额
    double shortShares = 12;    //沽空量
    double shortTurnover = 13;  //沽空额
    double change = 14;         //涨跌额
    double changeRate = 15;     //涨跌幅
    double amplitude = 16;      //振幅
    double avgPrice = 17;       //平均价
    int32 timeMode = 100;       //快照时间模式：0：实时，1：延时
}

message HkOrderItem {
    double price = 1;           //挂单价格
    int64 volume = 2;           //成交量
    int64 number = 3;           //数量
    int32 timeMode = 100;       //快照时间模式：0：实时，1：延时
}

//港股挂单
message HkOrder {
    string protocol = 1;                    //协议类型 1002
    string time = 2;                        //时间 yyyy-MM-dd HH:mm:ss
    string symbol = 3;                      //证券代码
    repeated HkOrderItem bidList = 4;       //买单序列
    repeated HkOrderItem askList = 5;       //卖单序列
}

//港股扩展行情
message HkExtendSnap {
    string protocol = 1;                    //协议类型 1010
    string symbol = 2;                      //证券代码
    string name = 3;                        //证券名称
    string time = 4;                        //行情时间 yyyy-MM-dd HH:mm:ss
    int64 totalShares = 5;                  //总股本
    double totalMarketValue = 6;            //总市值
    int64 circulationShares = 7;            //流通股本
    double circulationMarketValue = 8;      //流通市值
    double epsLyr = 9;                      //每股盈利(静态)
    double epsTtm = 10;                     //每股盈利(ttm)
    double nav = 11;                        //每股净资产
    double totalAsset = 12;                 //资产总额
    double totalLiabilities = 13;           //总负债
    double dpsLyr = 14;                     //股息(静态)
    double dpsTtm = 15;                     //股息(ttm)
    double dividendRateLyr = 16;            //股息率(静态)
    double dividendRateTtm = 17;            //股息率(ttm)
    double perLyr = 18;                     //市盈率(静态)
    double perTtm = 19;                     //市盈率(ttm)
    double perForecast = 20;                //市盈率(预测)
    double turnoverRate = 21;               //换手率
    double bidAskRate = 22;                 //委比
    double bidAskDiff = 23;                 //委差
    double volumeRate = 24;                 //量比
    double week52High = 25;                 //52周最高
    double week52Low = 26;                  //52周最低
    double historyHigh = 27;                //历史最高
    double historyLow = 28;                 //历史最低
    double netProfit = 29;                  //净利润(元)
    double pbr = 30;                        //市净率
    double peg = 31;                        //市盈率相对盈利增长比率
    double roe = 32;                        //净资产收益率
    double grossProfitRate = 33;            //毛利率
    double profitMargin = 34;               //净利润率
    double premium = 35;                    //溢价
    double ffEpsReported = 36;              //每股净值
    string underlying = 37;                 //资产类别
    string tradingCurrency = 38;            //交易货币
    int32 timeMode = 100;                   //快照时间模式：0：实时，1：延时
}

//港股指数
message HkIndexQuote {
    string protocol = 1;                    //协议类型 1011
    string symbol = 2;                      //证券代码
    string name = 3;                        //证券名称
    string time = 4;                        //行情时间 yyyy-MM-dd HH:mm:ss
    double open = 5;                        //开盘价
    double high = 6;                        //最高价
    double low = 7;                         //最低价
    double close = 8;                       //收盘价
    double preClose = 9;                    //昨收价
    int64 volume = 10;                      //成交量
    double amount = 11;                     //成交额
    double change = 12;                     //涨跌额
    double changeRate = 13;                 //涨跌幅
    double amplitude = 14;                  //振幅
    double week52High = 15;                 //52周最高
    double week52Low = 16;                  //52周最低
    int64 rise = 17;                        //涨家数
    int64 flat = 18;                        //平家数
    int64 fall = 19;                        //跌家数
    double totalMarketValue = 20;           //总市值 板块特有
    double perTtm = 21;                     //市盈率TTM 板块特有
    double avgPrice = 22;                   //平均价
    double circulationMarketValue = 23;     //流通总市值
    double perLyr = 24;                     //静态市盈率
    double turnoverRate = 25;               //换手率
    double last = 26;                       //最新价
    int32 timeMode = 100;                   //快照时间模式：0：实时，1：延时
}
