
syntax = "proto3";
package hk;

message PBStockDefine
{
	int64 code = 1;  // inside id 
	string name = 2;
	string shortname = 3;
	string currency = 4;
	string isincode = 5;
	string spread = 6;

	int32 producttype = 7;
	int32 lotsize = 8;

	int32 preclose = 9;
	int32 vcmflag = 10;
	int32 shortsell = 11;
	int32 casflag = 12;
	int32 dummyflag = 13;
	int32 testflag = 14;
	int32 stampdutyflag = 15;

			
	int32 listingdate = 16;
	int32 delistingdate = 17;
	int32 enfflag = 18;
	int32 accured = 19;
	int32 couponrate = 20;
	int32 counversionratio = 21;

	string freetext = 22;
	int32 strikeprice1 = 23;
	int32 strikeprice2 = 24;



	int32 maturitydate = 25;
	int32 callput = 26;
	int32 style = 27;
	int32 posflag = 28;
	int32 warrenttype = 29;
	int32 callprice = 30;
	int32 decimalprice = 31;
	int32 entitlement = 32;
	int32 decimalentitlement = 33;
	int32 nowarrants = 34;
	int32 nounderlying = 35;

	int32 posupper = 36;
	int32 poslower = 37;

	string market = 38;
	string instrument = 39;
	int32  ccassflag = 40;
	bytes  securitynamegccs = 41;
	int32  underlyingsecuritycode = 42;
}

message PBIndexDefine
{
	string code = 1;
	string currency = 2;
	int32 indexsource = 3;
}

message PBTrade
{
	int64 code = 1;
	sint64 time = 2;
	string name = 3;
	int64 tickerid = 4;
	string direction = 5;
	int32 price = 6;
	int32 qty = 7;
	int32 type = 8;
	int32 cancelflag = 9;
}

// 股票快照: 消息id FIU_SNAPSHOT，消息体 PBSnapshot
message PBSnapshot
{
	int64 code = 1;
	string name = 2;
	sint64 time = 3;
	int32 preclose = 4;
	int32 lastprice = 5;
	int32 open = 6;
	int32 high = 7;
	int32 low = 8;
	int32 close = 9;  
	int32 vwap = 10;  // market trade volume
	int64 sharestraded = 11;
	int64 turnover = 12;  
	int64 shortshares = 13;
	int64 shortturnover = 14;
	int32 pflag = 15;
}

message PBIndex
{
	string code = 1;
	sint64 time = 2;
	int64  indexstatus = 3;

	int32  preclose = 4;
	int32  open = 5;
	int32  high = 6;
	int32  low = 7;
	int32  close = 8;
	int32  last = 9;

	int64  totalvol = 10;

	int64  turnover = 11;
	int32  netchgpreday = 12;
	int32  netchgpredaypct = 13;
	int32  easvalue = 14;
}

message PBVcmTrigger
{
	int64 code = 1;
	sint64 coolingoffstarttime = 2;
	sint64 coolingoffendtime = 3;
	int32  vcmprice = 4;
	int32  vcmlowerprice = 5;
	int32  vcmupperprice = 6;
}

message PBOrderBroker
{
	int64 code = 1;
	string detail = 2;
	int64 time =3; 
	int32 direction = 4;//方向 买 卖
	int32 moreflag = 5;//是否还有未展示完的席位排位
	repeated PBOrderBrokerItem orderbrokervec = 6;
}

message PBOrderBrokerItem
{
	int32 index = 1;//档位
	repeated int32 broker = 2;//4位数席位号
}

message PBConnectBalance
{
	string market = 1;
	sint64 time = 2;
	string direction = 3;
	int64 balance = 4;
}

message PBConnectTurnover
{
	string market = 1;
	sint64 time = 2;
	string direction = 3;
	int64 buyturnover = 4;
	int64 sellturnover = 5;
	int64 buysell = 6;
}

message PBOrder
{
	// tick by tick order
	int32 code = 1;
	sint64 time = 2;

	string asklist = 3;
	string bidlist = 4;

	// 3、4 and 5、6 data is same
	repeated PBOrderItem askvec = 5;
	repeated PBOrderItem bidvec = 6;
}

message PBOrderItem
{
	int32 price = 1;
	int64 qty = 2;
	int32 num = 3;
}

message PBSecurityStatus
{
	int32 code = 1;
	int32 suspension = 2;
}

message PBEquipPrice
{
	int32 code = 1;
	int32 price = 2;
	int32 vol = 3;
}

message PBOrderImbalance
{
	int32 code = 1;
	string direction = 2;
	int32 quantity = 3;
}

message PBTradeSessionStatus
{
	string code = 1;
	int32 status = 2;
	int32 subid = 3;
	int32 controlflag = 4;
	int64 starttime = 5;
	int64 endtime = 6;
}

message PBRefPrice
{
	int32 code = 1;
	int32 refprice = 2;
	int32 upperprice = 3;
	int32 lowerprice = 4;
}

message PBOddLot
{
	int64  code = 1;
	int64  time = 2;
	int32  bidprice = 3;
	int32  bidquantity = 4;
	int32  bidnum = 5;
	string bidbroker = 6;
	int32  askprice = 7;
	int32  askquantity = 8;
	int32  asknum = 9;
	string askbroker = 10;
}

message PBMarketTurnOver
{
	string 	market = 1;
	string  currency = 2;
	int64  turnover = 3;
	sint64 time = 4;
}