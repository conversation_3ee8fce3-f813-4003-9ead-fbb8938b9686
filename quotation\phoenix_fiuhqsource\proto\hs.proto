
syntax = "proto3";
package hs;

message CNCode
{
	string symbol = 1;
	int64 market = 2;
	string name = 3;
	int64 type = 4;
	int64 subtype_sz = 5;
	string subtype_sh = 6;
	string currency = 7;
	float preclose = 8;
	string status = 9;
	int64 date = 10;
	string hkflag = 11;
	float uplimit = 12;
	float downlimit = 13;
}

message CNOrder
{
	// 逐笔委托 
	string symbol = 1;
	sint64 time = 2;

	repeated float bidpx = 3;
	repeated int64 bidsize = 4;
	repeated float askpx = 5;
	repeated int64 asksize = 6;
	int32 market = 7;
	int32 type = 8;
}


//快照协议: 消息id ZL_COMMON_STOCK，消息体 CNStock
message CNStock
{
	string symbol = 1;
	string name = 2;
	sint64 time = 3;
	float preclose = 4;
	float lastprice = 5;
	float open = 6;
	float high = 7;
	float low = 8;
	float close = 9;  

	int64 totalvol = 10;
	float totalamount = 11;
	int32 market = 12;
	string state = 13;
	int32 type = 14;
	string direction = 15;

	double turnover = 16;
	int32 lastqty = 17;
}

message KCClose
{
	string symbol = 1;
	string name = 2;
	sint64 time = 3;
	float close = 4;
	int64 totalvol = 5;
	float totalamount = 6;
	int64 bidsize = 7;
	int64 asksize = 8;
	string state = 9;
	int64 market = 10;
	int32 lastqty = 11;
}

message South
{
	string market = 1;
	sint64 time = 2;
	int64 threshold = 3;
	int64 posamount = 4;
	int64 used = 5;
	int32 status = 6;
}

