// use sea_orm::{ConnectOptions, Database, DatabaseConnection};
use sea_orm::{ConnectOptions, DatabaseConnection, SqlxMySqlConnector};
use std::time::Duration;
// use crate::server::service::push_log;

#[derive(Debug, Clone)]
#[allow(dead_code)]

pub struct DbClient {
    pub dbconn: DatabaseConnection,
    // backend: DatabaseBackend,
}

impl DbClient {
    #[allow(dead_code)]
    pub async fn new(uri: &String) -> Self {
        let mut opt = ConnectOptions::new(uri.to_owned());
        opt.max_connections(64)
            .min_connections(16)
            .connect_timeout(Duration::from_secs(10))
            .max_lifetime(Duration::from_secs(10))
            .idle_timeout(Duration::from_secs(8))
            .sqlx_logging(false);
        // .sqlx_logging(true)
        // .sqlx_logging_level(LevelFilter::Error);

        // let dbconn = Database::connect(opt).await.expect("can't connect to database");

        // let dbconn = SqlxMySqlConnector::connect(opt).await.expect("can't connect to database");
        let dbconn = match SqlxMySqlConnector::connect(opt).await {
            Ok(conn) => conn,
            Err(e) => {
                // push_log(format!("connect db err: {:?}", e).as_str()).await;
                panic!("connect db err: {:?}", e)
            }
        };

        DbClient {
            dbconn,
            // backend: DatabaseBackend::MySql,
        }
    }

    #[allow(dead_code)]
    pub fn get_conn(&self) -> &DatabaseConnection {
        &self.dbconn
    }
}
