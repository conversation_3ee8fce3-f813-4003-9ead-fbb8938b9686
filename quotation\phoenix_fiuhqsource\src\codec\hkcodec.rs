#![allow(dead_code)]

use anyhow::Result;
use prost::bytes::{/*Buf, */ BufMut, BytesMut};
use prost::Message;
use tracing::{error, info};
// use byteorder::{BigEndian, ByteOrder, <PERSON><PERSON>ndi<PERSON>, ReadBytesExt};
use std::io;
use tokio_util::codec::{Decoder, Encoder};

use crate::protofiles::hk::*;

pub enum FiuType {
    FiuHeartbeat = 0,
    FiuCode = 1,
    FiuSnapshot = 2,
    FiuOrder = 3,
    FiuIndexDefine = 4,
    FiuTrade = 5,
    FiuIndex = 6,
    FiuVcmTrigger = 7,
    FiuOrderBroker = 8,
    FiuConnectBalance = 9,
    FiuConnectTurnover = 10,
    FiuSecurityStatus = 11,
    FiuEquipPrice = 12,
    FiuOrderImbalance = 13,
    FiuTradeSessionStatus = 14,
    FiuRefPrice = 15,
    FiuOddLot = 16,
    FiuMarketTurnOver = 17,
}

// #[derive(serde::Serialize, serde::Deserialize)]
// #[derive(Clone, PartialEq, ::prost::Message)]
#[derive(Debug, Clone)]
pub struct FiuHeader {
    // #[prost(int32, tag = "1")]
    pub us_len: u16,
    // #[prost(int32, tag = "2")]
    pub c_type: u8,
}

impl FiuHeader {
    pub fn analysis_head(buf: &[u8]) -> FiuHeader {
        let len = u16::from_le_bytes(buf[0..2].try_into().unwrap());
        let bt = u8::from_le_bytes(buf[2..3].try_into().unwrap());
        // let len = u16::from_be_bytes(buf[0..2].try_into().unwrap());
        // let bt = u8::from_be_bytes(buf[2..3].try_into().unwrap());
        FiuHeader { us_len: len, c_type: bt }
    }
}

pub struct HkHqCodec;

//解码
impl Decoder for HkHqCodec {
    type Item = PbSnapshot;
    type Error = io::Error;

    fn decode(&mut self, src: &mut BytesMut) -> Result<Option<Self::Item>, Self::Error> {
        if src.len() < 3 {
            //不够包头
            error!("Recve Message head error!!");
            return Ok(None);
        } else {
            let mut head = vec![0u8; 3];
            head.copy_from_slice(&src[..3]); //先不截取,判断消息够一个消息后截取
            let msg_head = FiuHeader::analysis_head(&head);
            if msg_head.us_len < 3 || src.len() < msg_head.us_len as usize {
                return Ok(None);
            }
            let msg_len = (msg_head.us_len - 3) as usize;
            let _ = src.split_to(3); //足够解析一个消息,截掉消息头
            let msg = src.split_to(msg_len);
            let _ = match_msg(msg_head.c_type, msg);
            Ok(None)
        }
    }
}

//编码
impl Encoder<FiuHeader> for HkHqCodec {
    type Error = io::Error;
    //包头
    fn encode(&mut self, msg: FiuHeader, dst: &mut BytesMut) -> Result<(), Self::Error> {
        // let mut payload = prost::bytes::BytesMut::new();
        // let _ = msg.encode(&mut payload).unwrap();

        // dst.reserve(payload.len() + 8);//重置len
        // dst.put_u64(payload.len() as u64);//按大端字节顺序将一个无符号的64位整数写入self。
        // dst.put(payload);//追加消息

        dst.put_u16_le(msg.us_len); //消息头
        dst.put_u8(msg.c_type);

        // dst.put(payload);//消息体

        Ok(())
    }
}

pub fn match_msg(c_type: u8, msg: BytesMut) -> Result<()> {
    match c_type {
        v if v == FiuType::FiuHeartbeat as u8 => {
            info!("Heartbeat");
        }
        v if v == FiuType::FiuCode as u8 => { // PBStockDefine
             // info!("{:?}", PbStockDefine::decode(msg)?);
        }
        v if v == FiuType::FiuSnapshot as u8 => {
            // PBSnapshot
            info!("{:?}", PbSnapshot::decode(msg)?);
        }
        v if v == FiuType::FiuOrder as u8 => {
            // PBOrderItem PBOrder
            info!("{:?}", PbOrder::decode(msg)?);
        }
        v if v == FiuType::FiuIndexDefine as u8 => {
            // PBIndexDefine
            info!("{:?}", PbIndexDefine::decode(msg)?);
        }
        v if v == FiuType::FiuTrade as u8 => {
            // PBTrade
            info!("{:?}", PbTrade::decode(msg)?);
        }
        v if v == FiuType::FiuIndex as u8 => {
            // PBIndex
            info!("{:?}", PbIndex::decode(msg)?);
        }
        v if v == FiuType::FiuVcmTrigger as u8 => {
            // PBVcmTrigger
            info!("{:?}", PbVcmTrigger::decode(msg)?);
        }
        v if v == FiuType::FiuOrderBroker as u8 => {
            // PBOrderBroker
            info!("{:?}", PbOrderBroker::decode(msg)?);
        }
        v if v == FiuType::FiuConnectBalance as u8 => {
            // PBConnectBalance
            info!("{:?}", PbConnectBalance::decode(msg)?);
        }
        v if v == FiuType::FiuConnectTurnover as u8 => {
            // PBConnectTurnover
            info!("{:?}", PbConnectTurnover::decode(msg)?);
        }
        v if v == FiuType::FiuSecurityStatus as u8 => {
            //PBSecurityStatus
            info!("{:?}", PbSecurityStatus::decode(msg)?);
        }
        v if v == FiuType::FiuEquipPrice as u8 => {
            // PBEquipPrice
            info!("{:?}", PbEquipPrice::decode(msg)?);
        }
        v if v == FiuType::FiuOrderImbalance as u8 => {
            // PBOrderImbalance
            info!("{:?}", PbOrderImbalance::decode(msg)?);
        }
        v if v == FiuType::FiuTradeSessionStatus as u8 => {
            // PBTradeSessionStatus
            info!("{:?}", PbTradeSessionStatus::decode(msg)?);
        }
        v if v == FiuType::FiuRefPrice as u8 => {
            // PBRefPrice
            info!("{:?}", PbRefPrice::decode(msg)?);
        }
        v if v == FiuType::FiuOddLot as u8 => {
            // PBOddLot
            info!("{:?}", PbOddLot::decode(msg)?);
        }
        v if v == FiuType::FiuMarketTurnOver as u8 => {
            // PBMarketTurnOver
            info!("{:?}", PbMarketTurnOver::decode(msg)?);
        }
        _ => {}
    }
    Ok(())
}
