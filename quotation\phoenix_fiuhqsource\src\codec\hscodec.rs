#![allow(dead_code)]

use prost::bytes::{BufMut, BytesMut};
use prost::Message;
// use byteorder::{<PERSON><PERSON>ndi<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ndian, ReadBytesExt};
use std::io;
use tokio_util::codec::{Decoder, Encoder};
use tracing::{error, info};

use crate::protofiles::hs::*;

pub enum BtType {
    ZlCommonCode = 99,
    ZlCommonStock = 100,
    ZlCommonOrder = 101,
    ZlClientHeartbeat = 102,
    ZlClientKcclose = 110,
    ZlCodeEnd = 111,
    FiuSouth = 112,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct MsgHeader {
    pub us_length: u16,
    pub bt_type: u8,
}

impl MsgHeader {
    pub fn analysis_head(buf: &[u8]) -> MsgHeader {
        let len = u16::from_le_bytes(buf[0..2].try_into().unwrap());
        let bt = u8::from_le_bytes(buf[2..3].try_into().unwrap());
        // let len = u16::from_be_bytes(buf[0..2].try_into().unwrap());
        // let bt = u8::from_be_bytes(buf[2..3].try_into().unwrap());
        MsgHeader { us_length: len, bt_type: bt }
    }
}

pub struct HsHqCodec;

pub enum FiuDateSource {
    Stock(Box<CnStock>),
    Order(Box<CnOrder>),
}

//解码
impl Decoder for HsHqCodec {
    type Item = FiuDateSource;
    type Error = io::Error;

    fn decode(&mut self, src: &mut BytesMut) -> Result<Option<Self::Item>, Self::Error> {
        let now = std::time::Instant::now();
        if src.len() < 3 {
            //不够包头
            error!("Recve Message head error!!");
            return Ok(None);
        }
        let mut head = vec![0u8; 3];
        head.copy_from_slice(&src[..3]); //先不截取,判断消息够一个消息后截取
        let msg_head = MsgHeader::analysis_head(&head);
        if msg_head.us_length < 3 || src.len() < msg_head.us_length as usize {
            //一个完整的包[包头+包体]
            error!("消息不完整: {:?}", msg_head);
            return Ok(None);
        }

        let msg_len = (msg_head.us_length - 3) as usize;
        let _ = src.split_to(3); //足够解析一个消息,截掉消息头
        let msg = src.split_to(msg_len); //截取消息

        if msg_head.bt_type == BtType::ZlCommonCode as u8 {
            // info!("ZL_COMMON_CODE: {:?}", CnCode::decode(_msg)?);
            // info!("ZL_COMMON_CODE");
            info!("1耗时: {:?}", now.elapsed());
            return Ok(None);
        } else if msg_head.bt_type == BtType::ZlCommonStock as u8 {
            info!("ZL_COMMON_STOCK: {:?}", CnStock::decode(msg)?);
            // return Ok(Some(CnStock::decode(msg)?));
            // return Ok(Some(FiuDateSource::Stock(Box::new(CnStock::decode(msg)?))))
            info!("2耗时: {:?}", now.elapsed());
            return Ok(None);
        } else if msg_head.bt_type == BtType::ZlCommonOrder as u8 {
            info!("ZL_COMMON_ORDER: {:?}", CnOrder::decode(msg)?);
            info!("3耗时: {:?}", now.elapsed());
            // info!("ZL_COMMON_ORDER");
            // return Ok(Some(FiuDateSource::Order(Box::new(CnOrder::decode(msg)?))))
            return Ok(None);
        } else if msg_head.bt_type == BtType::ZlClientHeartbeat as u8 {
            info!("心跳: {:?}", msg_head);
            info!("4耗时: {:?}", now.elapsed());
            return Ok(None);
        } else if msg_head.bt_type == BtType::ZlClientKcclose as u8 {
            // info!("ZL_CLIENT_KCCLOSE: {:?}", KcClose::decode(msg)?);
            // info!("ZL_CLIENT_KCCLOSE");
            info!("5耗时: {:?}", now.elapsed());
            return Ok(None);
        } else if msg_head.bt_type == BtType::ZlCodeEnd as u8 {
            info!("ZL_CODE_END: 标识码表接收完毕: {:?}", msg_head);
            info!("6耗时: {:?}", now.elapsed());
            return Ok(None);
        } else if msg_head.bt_type == BtType::FiuSouth as u8 {
            // info!("FIU_SOUTH: {:?}", South::decode(msg)?);
            // info!("FIU_SOUTH");
            info!("7耗时: {:?}", now.elapsed());
            return Ok(None);
        } else {
            return Ok(None);
        }
    }
}

//编码
impl Encoder<MsgHeader> for HsHqCodec {
    type Error = io::Error;
    //包头
    fn encode(&mut self, msg: MsgHeader, dst: &mut BytesMut) -> Result<(), Self::Error> {
        // let mut payload = prost::bytes::BytesMut::new();
        // let _ = msg.encode(&mut payload).unwrap();

        // dst.reserve(payload.len() + 8);//重置len
        // dst.put_u64(payload.len() as u64);//按大端字节顺序将一个无符号的64位整数写入self。
        // dst.put(payload);//追加消息

        dst.put_u16_le(msg.us_length); //消息头
        dst.put_u8(msg.bt_type);

        // dst.put(payload);//消息体

        Ok(())
    }
}
