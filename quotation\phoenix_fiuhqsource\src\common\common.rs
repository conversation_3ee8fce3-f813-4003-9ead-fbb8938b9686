use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use tokio::sync::RwLock;
use tracing::info;

use protoes::hqmsg::YsHqInfo;

use crate::fiuhq::{
    hk::HkStockBasicInfo,
    hs::{HsStockBasicInfo, HsStockQuote},
};
use crate::protofiles::{CnOrder, CnStock, HkExtendSnap, HsExtendSnap, PbOrder, PbSnapshot};

use lazy_static::lazy_static;

lazy_static! {
    static ref HS_ORDER: RwLock<HashMap<String, CnOrder>> = RwLock::new(HashMap::new());
    static ref HK_ORDER: RwLock<HashMap<String, PbOrder>> = RwLock::new(HashMap::new());
    static ref HS_TICK: RwLock<HashMap<String, YsHqInfo>> = RwLock::new(HashMap::new());
    static ref HK_TICK: RwLock<HashMap<String, YsHqInfo>> = RwLock::new(HashMap::new());
    static ref PRICE: RwLock<HashMap<String, (f64, i64)>> = RwLock::new(HashMap::new());
    static ref HS_EXTEND_SNAP: RwLock<HashMap<String, HsExtendSnap>> = RwLock::new(HashMap::new());
    static ref HK_EXTEND_SNAP: RwLock<HashMap<String, HkExtendSnap>> = RwLock::new(HashMap::new());
}

#[allow(dead_code)]
pub enum SecurityType {
    Stock = 0,             //股票
    Fund = 1,              //基金
    Bond = 2,              //债券
    Index = 3,             //指数
    Forward = 4,           //期货
    BuyBack = 5,           //回购
    StockIndexFutures = 6, //股指期货
    ForeignExchange = 7,   //外汇
    Warrant = 8,           //权证
    BShare = 9,            //B股
}

#[allow(dead_code)]
pub enum Market {
    SH = 0, //上海 XSHG
    SZ = 1, //深圳 XSHE
    // HS
    HK = 2, //香港 XHKG
            // SG
            // US
}

#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct StockInfo {
    pub name: String,
    pub inner_code: String,
    pub outer_code: String,
    pub market_id: i64,
    pub lot_size: i32, //每手股数
    pub currency: String,
    pub security_status: i32,     //股票状态 0	正常 1	未上市 2	熔断 3	停牌 4	退市
    pub stock_type: i32,          //股票类型1	正股 2	ETF 3	涡轮 4	指数 5	板块 6	其他 7	基金 8	债券 9	ETN 10	B股
    pub plate_codes: Vec<String>, //所属板块代码(预留字段)
    pub inner_stock_type: i32,
}

#[allow(dead_code)]
pub fn get_exchange_id(market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => "XSHG".to_string(),
        v if v == Market::SZ as i64 => "XSHE".to_string(),
        v if v == Market::HK as i64 => "XHKG".to_string(),
        _ => "".to_string(),
    }
}

#[allow(dead_code)]
pub fn get_inner_code(symbol: String, market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => format!("{}_XSHG", symbol),
        v if v == Market::SZ as i64 => format!("{}_XSHE", symbol),
        v if v == Market::HK as i64 => format!("{}_XHKG", symbol),
        _ => "".to_string(),
    }
}

#[allow(dead_code)]
pub fn get_outer_code(symbol: String, market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => format!("{}.sh", symbol),
        v if v == Market::SZ as i64 => format!("{}.sz", symbol),
        v if v == Market::HK as i64 => format!("{}.hk", symbol),
        _ => "".to_string(),
    }
}

#[allow(dead_code)]
pub async fn insert_cn_order(order: &CnOrder) {
    let mut hs_order = HS_ORDER.write().await;
    let sysmbol = get_outer_code(order.symbol.to_owned(), order.market as i64);
    hs_order.insert(sysmbol, order.clone());
}

#[allow(dead_code)]
pub async fn insert_pb_order(order: &PbOrder) {
    let mut hk_order = HK_ORDER.write().await;
    let symbol = format!("{:05}.hk", order.code);
    hk_order.insert(symbol, order.to_owned());
}

#[allow(dead_code)]
pub async fn insert_hs_extend_snap(snap: &HsExtendSnap) {
    let mut hs = HS_EXTEND_SNAP.write().await;
    hs.insert(snap.symbol.to_owned(), snap.clone());
}

#[allow(dead_code)]
pub async fn insert_hk_extend_snap(snap: &HkExtendSnap) {
    let mut hk = HK_EXTEND_SNAP.write().await;
    hk.insert(snap.symbol.to_owned(), snap.clone());
}

///A股tick
#[allow(dead_code)]
pub async fn convert_cnstocks_to_tick(cnstock: &CnStock) -> Option<YsHqInfo> {
    // let curr = chrono::Local::now().naive_local().format("%Y%m%d").to_string().parse::<i64>().unwrap();
    // if cnstock.time / 1000000000 != curr {
    //     info!("time not match: {} {}", cnstock.time, curr);
    //     return None;
    // }
    let symbol = get_outer_code(cnstock.symbol.to_owned(), cnstock.market as i64);
    let hs_order = HS_ORDER.read().await;
    let order = match hs_order.get(&symbol) {
        Some(v) => v.to_owned(),
        None => CnOrder::default(),
    };
    drop(hs_order);

    let extend = HS_EXTEND_SNAP.read().await;
    let extend_snap = match extend.get(&symbol) {
        Some(v) => v.to_owned(),
        None => return None,
    };
    drop(extend);

    let mut tick = YsHqInfo::default();
    tick.exchange_id = get_exchange_id(cnstock.market as i64);
    tick.contract_no1 = format!("{}_{}", cnstock.symbol, tick.exchange_id);

    // CnStock { symbol: "301169", name: "零点有数", time: 20240524144712000, preclose: 29.95, lastprice: 29.24,
    // open: 29.8, high: 30.13, low: 29.23, close: 0.0, totalvol: 921853, totalamount: 27229548.0, market: 1, state: "T0",
    // r#type: 0, direction: "", turnover: 0.0, lastqty: 0 }
    let year = cnstock.time / 10000000000000; //2023
    let month = cnstock.time / 100000000000; //202312
    let day = cnstock.time / 1000000000; //20231215
    let hour = cnstock.time / 10000000; //2023121516
    let minute = cnstock.time / 100000; //202312151600
    let second = cnstock.time / 1000; //20231215160000
    tick.tapidtstamp = format!(
        "{:04}-{:02}-{:02} {:02}:{:02}:{:02}.{:03}",
        year,
        month % 100,
        day % 100,
        hour % 100,
        minute % 100,
        second % 100,
        cnstock.time % 1000
    );

    let point = if cnstock.r#type == SecurityType::Stock as i32 { 100.0 } else { 1000.0 };

    tick.q_pre_closing_price = (f64::from(cnstock.preclose) * point).round() / point;
    tick.q_last_price = (f64::from(cnstock.lastprice) * point).round() / point;
    tick.q_opening_price = (f64::from(cnstock.open) * point).round() / point;
    tick.q_high_price = (f64::from(cnstock.high) * point).round() / point;
    tick.q_low_price = (f64::from(cnstock.low) * point).round() / point;
    tick.q_closing_price = (f64::from(cnstock.close) * point).round() / point;
    let bid_px: Vec<f64> = order.bidpx.iter().map(|x| (*x as f64 * point).round() / point).collect();
    tick.q_bid_price.extend_from_slice(&bid_px);
    tick.q_bid_qty.extend_from_slice(&order.bidsize); //单位 手, 股票：手（1 手=100 股）, 债券：手（1 手=10 张）,基金：手（1 手=100 份）

    let ask_px: Vec<f64> = order.askpx.iter().map(|x| (*x as f64 * point).round() / point).collect();
    tick.q_ask_price.extend_from_slice(&ask_px);
    tick.q_ask_qty.extend_from_slice(&order.asksize);

    if cnstock.preclose != 0.000 && cnstock.lastprice != 0.000 {
        tick.q_change_value = ((cnstock.lastprice - cnstock.preclose) as f64 * point).round() / point; // 最新价 - 昨收价
        tick.q_change_rate = (tick.q_change_value / cnstock.preclose as f64 * 100.0 * 100.0).round() / 100.0; //百分数[涨跌价 / 昨收价]
                                                                                                              // 计算公式为：[(当日最高价-当日最低价)÷上一交易日收盘价]x100%
        tick.q_amplitude = ((tick.q_high_price - tick.q_low_price) / cnstock.preclose as f64 * 100.0 * 100.0).round() / 100.0;
    }
    tick.q_total_turnover = cnstock.totalamount.into();

    //扩展
    tick.q_total_shares = extend_snap.total_shares;
    tick.q_market_value = extend_snap.total_market_value;
    tick.q_circulation_amount = extend_snap.circulation_shares;
    tick.q_pe_rate = extend_snap.per_ttm;
    tick.q_dyn_pb_rate = extend_snap.pbr;
    tick.q_turnover_ratio = extend_snap.turnover_rate;
    tick.q_entrust_rate = extend_snap.bid_ask_rate;
    tick.q_vol_ratio = extend_snap.volume_rate;
    tick.q_limit_down_price = extend_snap.limit_down;
    tick.q_limit_up_price = extend_snap.limit_up;
    tick.currency_no = extend_snap.trading_currency.to_owned();

    // 股票：手（1 手=100 股）, 债券：手（1 手=10 张）,基金：手（1 手=100 份）
    // // 单位说明：
    // // 股票：股
    // // 债券：张    ===> 过滤掉了
    // // 基金：份
    // // 指数：手（1 手=100 股）
    if cnstock.r#type == SecurityType::Index as i32 {
        //指数
        tick.q_total_qty = cnstock.totalvol; //手
        tick.q_last_qty = cnstock.lastqty as f64; //手
        let mut price = PRICE.write().await;
        tick.q_average_price = match price.get_mut(&tick.contract_no1) {
            Some((total_price, num)) => {
                *total_price += tick.q_last_price;
                *num += 1;
                (*total_price / *num as f64 * 1000.0).round() / 1000.0
            }
            None => {
                price.insert(tick.contract_no1.to_owned(), (tick.q_last_price, 1));
                tick.q_last_price
            }
        };
    } else {
        tick.q_total_qty = cnstock.totalvol / 100; //手
                                                   // tick.q_last_qty = cnstock.lastqty as f64 / 100.00;//手
        tick.q_last_qty = (cnstock.lastqty / 100) as f64; //手

        if cnstock.totalvol != 0 {
            tick.q_average_price = ((tick.q_total_turnover / cnstock.totalvol as f64) * 1000.0).round() / 1000.0;
            //总成交额 / 总成交量
        }
    }

    let mut pre = HS_TICK.write().await;
    let pre_tick = match pre.get(&tick.contract_no1) {
        Some(v) => v.to_owned(),
        None => YsHqInfo::default(),
    };

    if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
        tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
    }

    pre.insert(tick.contract_no1.to_owned(), tick.clone());

    Some(tick)
}

/// 港股tick
#[allow(dead_code)]
pub async fn convert_pb_snapshot_to_tick(snapshot: &PbSnapshot) -> Option<YsHqInfo> {
    let mut tick = YsHqInfo::default();

    let hk_order = HK_ORDER.read().await;
    let symbol = format!("{:05}.hk", snapshot.code);
    let order = match hk_order.get(&symbol) {
        Some(v) => v.to_owned(),
        None => PbOrder::default(),
    };
    drop(hk_order);

    let extend = HK_EXTEND_SNAP.read().await;
    let extend_snap = match extend.get(&symbol) {
        Some(v) => v.to_owned(),
        // None => return None,
        None => HkExtendSnap::default(),
    };
    drop(extend);

    tick.exchange_id = "XHKG".to_owned();
    tick.contract_no1 = format!("{:05}_XHKG", snapshot.code);

    let year = snapshot.time / 10000000000000; //2023
    let month = snapshot.time / 100000000000; //202312
    let day = snapshot.time / 1000000000; //20231215
    let hour = snapshot.time / 10000000; //2023121516
    let minute = snapshot.time / 100000; //202312151600
    let second = snapshot.time / 1000; //20231215160000
    tick.tapidtstamp = format!(
        "{:04}-{:02}-{:02} {:02}:{:02}:{:02}.{:03}",
        year,
        month % 100,
        day % 100,
        hour % 100,
        minute % 100,
        second % 100,
        snapshot.time % 1000
    );

    tick.q_pre_closing_price = snapshot.preclose as f64 / 1000.00;
    tick.q_last_price = snapshot.lastprice as f64 / 1000.00;
    tick.q_opening_price = snapshot.open as f64 / 1000.00;
    tick.q_high_price = snapshot.high as f64 / 1000.00;
    tick.q_low_price = snapshot.low as f64 / 1000.00;
    tick.q_closing_price = snapshot.close as f64 / 1000.00;
    // tick.q_average_price = snapshot.vwap as f64 / 1000.00;
    tick.q_total_qty = snapshot.sharestraded;
    // tick.q_total_turnover = (snapshot.turnover as f64) / 1000.00;
    tick.q_total_turnover = (snapshot.turnover / 1000) as f64;

    for v in order.bidvec.iter() {
        tick.q_bid_price.push(v.price as f64 / 1000.00);
        tick.q_bid_qty.push(v.qty);
    }

    for v in order.askvec.iter() {
        tick.q_ask_price.push(v.price as f64 / 1000.00);
        tick.q_ask_qty.push(v.qty);
    }

    if tick.q_total_qty != 0 {
        tick.q_average_price = ((tick.q_total_turnover / tick.q_total_qty as f64) * 1000.0).round() / 1000.0;
    } else {
        tick.q_average_price = tick.q_last_price;
    }
    if snapshot.preclose != 0 && snapshot.lastprice != 0 {
        tick.q_change_value = (snapshot.lastprice - snapshot.preclose) as f64 / 1000.00;
        tick.q_change_rate = ((tick.q_change_value / (snapshot.preclose as f64 / 1000.00) * 100.00) * 100.0).round() / 100.0;
        // 计算公式为：[(当日最高价-当日最低价)÷上一交易日收盘价]x100%
        tick.q_amplitude = ((tick.q_high_price - tick.q_low_price) / (snapshot.preclose as f64 / 1000.00) * 100.0 * 100.0).round() / 100.0;
    }

    //扩展
    tick.q_total_shares = extend_snap.total_shares;
    tick.q_market_value = extend_snap.total_market_value;
    tick.q_circulation_amount = extend_snap.circulation_shares;
    tick.q_pe_rate = extend_snap.per_ttm;
    tick.q_dyn_pb_rate = extend_snap.pbr;
    tick.q_turnover_ratio = extend_snap.turnover_rate;
    tick.q_entrust_rate = extend_snap.bid_ask_rate;
    tick.q_vol_ratio = extend_snap.volume_rate;
    tick.currency_no = extend_snap.trading_currency.to_owned();

    let mut pre = HK_TICK.write().await;
    let pre_tick = match pre.get(&tick.contract_no1) {
        Some(v) => v.to_owned(),
        None => YsHqInfo::default(),
    };

    if pre_tick.q_total_qty != 0 && tick.q_total_qty > pre_tick.q_total_qty {
        tick.q_last_qty = (tick.q_total_qty - pre_tick.q_total_qty).abs() as f64;
    }
    if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
        tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
    }

    pre.insert(tick.contract_no1.to_owned(), tick.clone());

    Some(tick)
}

#[allow(dead_code)]
pub async fn convert_local_stock_info(hsbasic: &Vec<HsStockBasicInfo>, hkbasic: &Vec<HkStockBasicInfo>) -> Vec<StockInfo> {
    let mut stockinfo = Vec::new();
    for v in hsbasic.iter() {
        if v.symbol.is_null() || v.market.is_null() || v.security_status.is_null() || v.name.is_null() {
            continue;
        }
        let symbol = v.symbol.as_str().unwrap_or_default().to_owned();
        let market = v.market.as_i64().unwrap_or_default();
        let inner_code = get_inner_code(symbol.split(".").collect::<Vec<&str>>()[0].to_owned(), market);
        if inner_code.is_empty() {
            continue;
        }

        let stock_info = StockInfo {
            name: v.name.as_str().unwrap_or_default().to_owned(),
            inner_code,
            outer_code: symbol.to_owned(),
            market_id: if market == Market::SH as i64 { 101 } else { 102 },
            lot_size: v.lot_size.as_i64().unwrap_or(100) as i32,
            currency: v.currency.as_str().unwrap_or("CNY").to_owned(),
            security_status: v.security_status.as_i64().unwrap_or_default() as i32,
            stock_type: v.stock_type.as_i64().unwrap_or_default() as i32,
            plate_codes: {
                let codes = v.plate_codes.as_array().unwrap();
                codes
                    .into_iter()
                    .filter_map(|val| match val {
                        Value::String(s) => Some(s.clone()),
                        _ => None,
                    })
                    .collect()
            },
            inner_stock_type: {
                let board = v.board.as_i64().unwrap_or(0);
                match board {
                    1 => 1, //主板
                    3 => 4, //创业板
                    6 => 5, //科创板
                    // 7 => 8, //深交所退市整理板
                    // 8 => 8, //上交所风险警示板
                    _ => {
                        if v.stock_type.as_i64().unwrap_or_default() as i32 == 2 || v.stock_type.as_i64().unwrap_or_default() as i32 == 7 {
                            //ETF  基金
                            6
                        } else {
                            0
                        }
                    }
                }
            },
        };
        stockinfo.push(stock_info);
    }

    for v in hkbasic.iter() {
        if v.symbol.is_null() || v.market.is_null() || v.security_status.is_null() || v.name.is_null() {
            continue;
        }

        let symbol = v.symbol.as_str().unwrap_or_default().to_owned();
        let inner_code = get_inner_code(symbol.split(".").collect::<Vec<&str>>()[0].to_owned(), 2);
        if inner_code.is_empty() {
            continue;
        }

        let stock_info = StockInfo {
            name: v.name.as_str().unwrap_or_default().to_owned(),
            inner_code,
            outer_code: symbol.to_owned(),
            market_id: 103,
            lot_size: v.lot_size.as_i64().unwrap_or(100) as i32,
            currency: v.currency.as_str().unwrap_or("HKD").to_owned(),
            security_status: v.security_status.as_i64().unwrap_or_default() as i32,
            stock_type: v.stock_type.as_i64().unwrap_or_default() as i32,
            plate_codes: {
                let codes = v.plate_codes.as_array().unwrap();
                codes
                    .into_iter()
                    .filter_map(|val| match val {
                        Value::String(s) => Some(s.clone()),
                        _ => None,
                    })
                    .collect()
            },
            inner_stock_type: {
                let board = v.market.as_str().unwrap_or("");
                match board {
                    "MAIN" => 2, //主板 板块内码：1002_HK
                    "GEM" => 4,  //创业板 板块内码：1003_HK
                    // "ETS" => 5,//扩充交易证券
                    // "NASD" => 6,//NASDAQ AMX市场
                    _ => {
                        if v.stock_type.as_i64().unwrap_or_default() as i32 == 2 || v.stock_type.as_i64().unwrap_or_default() as i32 == 7 {
                            //ETF
                            6
                        } else {
                            0
                        }
                    }
                }
            },
        };
        stockinfo.push(stock_info);
    }

    stockinfo
}

#[allow(dead_code)]
pub async fn convert_tick(data: HsStockQuote) -> YsHqInfo {
    let mut tick = YsHqInfo::default();
    tick.exchange_id = if data.symbol.contains("sh") { "XSHG".to_owned() } else { "XSHE".to_owned() };
    tick.contract_no1 = format!("{}_{}", data.symbol.split(".").collect::<Vec<&str>>()[0].to_owned(), tick.exchange_id);

    tick.q_total_qty = data.snapshot.volume as i64;
    tick.q_total_turnover = data.snapshot.amount;
    tick.q_amplitude = data.snapshot.amplitude;
    tick.q_average_price = data.snapshot.avg_price;
    tick.q_change_rate = data.snapshot.change_rate;
    tick.q_change_value = data.snapshot.change;
    tick.tapidtstamp = data.snapshot.time.to_string();
    tick.q_closing_price = data.snapshot.close;
    tick.q_high_price = data.snapshot.high;
    tick.q_last_price = data.snapshot.last;
    tick.q_low_price = data.snapshot.low;
    tick.q_opening_price = data.snapshot.open;
    tick.q_pre_closing_price = data.snapshot.pre_close;

    for v in data.order.ask_list.iter() {
        tick.q_ask_price.push(v.price);
        tick.q_ask_qty.push(v.volume as i64);
    }
    for v in data.order.bid_list.iter() {
        tick.q_bid_price.push(v.price);
        tick.q_bid_qty.push(v.volume as i64);
    }

    tick
}

#[tokio::test]
async fn test() {
    let mut price = PRICE.write().await;
    let p = match price.get_mut(&"000001_XSHG".to_owned()) {
        Some((total_price, num)) => {
            *total_price += 1.0;
            *num += 1;
            *total_price / *num as f64
        }
        None => {
            price.insert("000001_XSHG".to_owned(), (1.0, 1));
            0.0
        }
    };
    println!("{}", p);
    println!("{:#?}", price);

    let p = match price.get_mut(&"000001_XSHG".to_owned()) {
        Some((total_price, num)) => {
            *total_price += 1.0;
            *num += 1;
            *total_price / *num as f64
        }
        None => {
            price.insert("000001_XSHG".to_owned(), (1.0, 1));
            0.0
        }
    };
    println!("{}", p);
    println!("{:#?}", price);
}

pub async fn debug_display<T: std::fmt::Debug>(t: &T) {
    // 当前时间在九点半之钱不包括九点半打印t
    if let Some(time930) = chrono::NaiveTime::from_hms_opt(9, 29, 0) {
        if utility::timeutil::current_naive_time().time() < time930 {
            info!("received data: {:?}", t);
        }
    }
}

pub async fn convert_cnstocks_to_tick_v1(cnstock: &CnStock) -> Option<YsHqInfo> {
    let symbol = get_outer_code(cnstock.symbol.clone(), cnstock.market as i64);

    // 获取订单和扩展快照
    let order = {
        let hs_order = HS_ORDER.read().await;
        hs_order.get(&symbol).cloned().unwrap_or_default()
    };
    let extend_snap = {
        let extend = HS_EXTEND_SNAP.read().await;
        extend.get(&symbol)?.clone()
    };

    let mut tick = YsHqInfo::default();
    tick.exchange_id = get_exchange_id(cnstock.market as i64);
    tick.contract_no1 = format!("{}_{}", cnstock.symbol, tick.exchange_id);

    // 时间格式化
    let t = cnstock.time;
    tick.tapidtstamp = format!(
        "{:04}-{:02}-{:02} {:02}:{:02}:{:02}.{:03}",
        t / 10000000000000,
        (t / 100000000000) % 100,
        (t / 1000000000) % 100,
        (t / 10000000) % 100,
        (t / 100000) % 100,
        (t / 1000) % 100,
        t % 1000
    );

    let point = if cnstock.r#type == SecurityType::Stock as i32 { 100.0 } else { 1000.0 };

    tick.q_pre_closing_price = (cnstock.preclose as f64 * point).round() / point;
    tick.q_last_price = (cnstock.lastprice as f64 * point).round() / point;
    tick.q_opening_price = (cnstock.open as f64 * point).round() / point;
    tick.q_high_price = (cnstock.high as f64 * point).round() / point;
    tick.q_low_price = (cnstock.low as f64 * point).round() / point;
    tick.q_closing_price = (cnstock.close as f64 * point).round() / point;

    tick.q_bid_price.extend(order.bidpx.iter().map(|x| (*x as f64 * point).round() / point));
    tick.q_bid_qty.extend_from_slice(&order.bidsize);
    tick.q_ask_price.extend(order.askpx.iter().map(|x| (*x as f64 * point).round() / point));
    tick.q_ask_qty.extend_from_slice(&order.asksize);

    if cnstock.preclose != 0.0 && cnstock.lastprice != 0.0 {
        tick.q_change_value = ((cnstock.lastprice - cnstock.preclose) as f64 * point).round() / point;
        tick.q_change_rate = (tick.q_change_value / cnstock.preclose as f64 * 10000.0).round() / 100.0;
        tick.q_amplitude = ((tick.q_high_price - tick.q_low_price) / cnstock.preclose as f64 * 10000.0).round() / 100.0;
    }
    tick.q_total_turnover = cnstock.totalamount as f64;

    // 扩展
    tick.q_total_shares = extend_snap.total_shares;
    tick.q_market_value = extend_snap.total_market_value;
    tick.q_circulation_amount = extend_snap.circulation_shares;
    tick.q_pe_rate = extend_snap.per_ttm;
    tick.q_dyn_pb_rate = extend_snap.pbr;
    tick.q_turnover_ratio = extend_snap.turnover_rate;
    tick.q_entrust_rate = extend_snap.bid_ask_rate;
    tick.q_vol_ratio = extend_snap.volume_rate;
    tick.q_limit_down_price = extend_snap.limit_down;
    tick.q_limit_up_price = extend_snap.limit_up;
    tick.currency_no = extend_snap.trading_currency.clone();

    // 数量和均价
    if cnstock.r#type == SecurityType::Index as i32 {
        tick.q_total_qty = cnstock.totalvol;
        tick.q_last_qty = cnstock.lastqty as f64;
        let mut price = PRICE.write().await;
        tick.q_average_price = match price.get_mut(&tick.contract_no1) {
            Some((total_price, num)) => {
                *total_price += tick.q_last_price;
                *num += 1;
                (*total_price / *num as f64 * 1000.0).round() / 1000.0
            }
            None => {
                price.insert(tick.contract_no1.clone(), (tick.q_last_price, 1));
                tick.q_last_price
            }
        };
    } else {
        tick.q_total_qty = cnstock.totalvol / 100;
        tick.q_last_qty = (cnstock.lastqty / 100) as f64;
        tick.q_average_price = if cnstock.totalvol != 0 {
            ((tick.q_total_turnover / cnstock.totalvol as f64) * 1000.0).round() / 1000.0
        } else {
            tick.q_last_price
        };
    }

    // 上一笔成交额
    {
        let mut pre = HS_TICK.write().await;
        let pre_tick = pre.get(&tick.contract_no1).cloned().unwrap_or_default();
        if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
            tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
        }
        pre.insert(tick.contract_no1.clone(), tick.clone());
    }
    Some(tick)
}

pub async fn convert_pb_snapshot_to_tick_v1(snapshot: &PbSnapshot) -> Option<YsHqInfo> {
    let symbol = format!("{:05}.hk", snapshot.code);

    // 获取盘口和扩展快照
    let order = {
        let hk_order = HK_ORDER.read().await;
        hk_order.get(&symbol).cloned().unwrap_or_default()
    };
    let extend_snap = {
        let extend = HK_EXTEND_SNAP.read().await;
        extend.get(&symbol).cloned().unwrap_or_default()
    };

    let mut tick = YsHqInfo::default();
    tick.exchange_id = "XHKG".to_owned();
    tick.contract_no1 = format!("{:05}_XHKG", snapshot.code);

    // 时间格式化
    let t = snapshot.time;
    tick.tapidtstamp = format!(
        "{:04}-{:02}-{:02} {:02}:{:02}:{:02}.{:03}",
        t / 10000000000000,
        (t / 100000000000) % 100,
        (t / 1000000000) % 100,
        (t / 10000000) % 100,
        (t / 100000) % 100,
        (t / 1000) % 100,
        t % 1000
    );

    tick.q_pre_closing_price = snapshot.preclose as f64 / 1000.0;
    tick.q_last_price = snapshot.lastprice as f64 / 1000.0;
    tick.q_opening_price = snapshot.open as f64 / 1000.0;
    tick.q_high_price = snapshot.high as f64 / 1000.0;
    tick.q_low_price = snapshot.low as f64 / 1000.0;
    tick.q_closing_price = snapshot.close as f64 / 1000.0;
    tick.q_total_qty = snapshot.sharestraded;
    tick.q_total_turnover = (snapshot.turnover / 1000) as f64;

    for v in &order.bidvec {
        tick.q_bid_price.push(v.price as f64 / 1000.0);
        tick.q_bid_qty.push(v.qty);
    }
    for v in &order.askvec {
        tick.q_ask_price.push(v.price as f64 / 1000.0);
        tick.q_ask_qty.push(v.qty);
    }

    tick.q_average_price = if tick.q_total_qty != 0 {
        ((tick.q_total_turnover / tick.q_total_qty as f64) * 1000.0).round() / 1000.0
    } else {
        tick.q_last_price
    };

    if snapshot.preclose != 0 && snapshot.lastprice != 0 {
        tick.q_change_value = (snapshot.lastprice - snapshot.preclose) as f64 / 1000.0;
        tick.q_change_rate = ((tick.q_change_value / (snapshot.preclose as f64 / 1000.0) * 10000.0).round()) / 100.0;
        tick.q_amplitude = ((tick.q_high_price - tick.q_low_price) / (snapshot.preclose as f64 / 1000.0) * 10000.0).round() / 100.0;
    }

    // 扩展
    tick.q_total_shares = extend_snap.total_shares;
    tick.q_market_value = extend_snap.total_market_value;
    tick.q_circulation_amount = extend_snap.circulation_shares;
    tick.q_pe_rate = extend_snap.per_ttm;
    tick.q_dyn_pb_rate = extend_snap.pbr;
    tick.q_turnover_ratio = extend_snap.turnover_rate;
    tick.q_entrust_rate = extend_snap.bid_ask_rate;
    tick.q_vol_ratio = extend_snap.volume_rate;
    tick.currency_no = extend_snap.trading_currency.clone();

    // 上一笔成交量/成交额
    {
        let mut pre = HK_TICK.write().await;
        let pre_tick = pre.get(&tick.contract_no1).cloned().unwrap_or_default();
        if pre_tick.q_total_qty != 0 && tick.q_total_qty > pre_tick.q_total_qty {
            tick.q_last_qty = (tick.q_total_qty - pre_tick.q_total_qty).abs() as f64;
        }
        if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
            tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
        }
        pre.insert(tick.contract_no1.clone(), tick.clone());
    }

    Some(tick)
}

pub async fn convert_pb_order_to_tick_v1(order: &PbOrder) -> Option<YsHqInfo> {
    let symbol = format!("{:05}_XHKG", order.code);
    let mut tick = {
        let pre = HK_TICK.read().await;
        pre.get(&symbol).cloned()?
    };

    // 时间格式化
    let t = order.time;
    tick.tapidtstamp = format!(
        "{:04}-{:02}-{:02} {:02}:{:02}:{:02}.{:03}",
        t / 10000000000000,
        (t / 100000000000) % 100,
        (t / 1000000000) % 100,
        (t / 10000000) % 100,
        (t / 100000) % 100,
        (t / 1000) % 100,
        t % 1000
    );

    // 清空盘口
    tick.q_bid_price.clear();
    tick.q_bid_qty.clear();
    tick.q_ask_price.clear();
    tick.q_ask_qty.clear();

    // 填充盘口
    for v in &order.bidvec {
        tick.q_bid_price.push(v.price as f64 / 1000.0);
        tick.q_bid_qty.push(v.qty);
    }
    for v in &order.askvec {
        tick.q_ask_price.push(v.price as f64 / 1000.0);
        tick.q_ask_qty.push(v.qty);
    }

    // 重置成交量等
    tick.q_total_qty = 0; //手
    tick.q_last_qty = 0.0; //手
    tick.q_total_turnover = 0.0; //万元
    tick.q_last_turnover = 0.0; //万元

    let mut hk_order = HK_ORDER.write().await;
    hk_order.insert(format!("{:05}.hk", order.code), order.clone());

    Some(tick)
}

pub async fn convert_cn_order_to_tick_v1(order: &CnOrder) -> Option<YsHqInfo> {
    // 构造合约代码
    let exchange_id = get_exchange_id(order.market as i64);
    let contract_no1 = format!("{}_{}", order.symbol, exchange_id);

    // 获取前一笔tick
    let mut tick = HS_TICK.read().await.get(&contract_no1).cloned()?;

    // 时间格式化
    let t = order.time;
    tick.tapidtstamp = format!(
        "{:04}-{:02}-{:02} {:02}:{:02}:{:02}.{:03}",
        t / 10000000000000,
        (t / 100000000000) % 100,
        (t / 1000000000) % 100,
        (t / 10000000) % 100,
        (t / 100000) % 100,
        (t / 1000) % 100,
        t % 1000
    );

    // 清空盘口
    tick.q_bid_price.clear();
    tick.q_bid_qty.clear();
    tick.q_ask_price.clear();
    tick.q_ask_qty.clear();

    let point = if order.r#type == SecurityType::Stock as i32 { 100.0 } else { 1000.0 };
    // 填充盘口
    tick.q_bid_price.extend(order.bidpx.iter().map(|x| (*x as f64 * point).round() / point));
    tick.q_bid_qty.extend_from_slice(&order.bidsize);
    tick.q_ask_price.extend(order.askpx.iter().map(|x| (*x as f64 * point).round() / point));
    tick.q_ask_qty.extend_from_slice(&order.asksize);

    // 重置成交量等
    tick.q_total_qty = 0; //手
    tick.q_last_qty = 0.0; //手
    tick.q_total_turnover = 0.0; //万元
    tick.q_last_turnover = 0.0; //万元

    // 更新订单缓存
    HS_ORDER.write().await.insert(get_outer_code(order.symbol.clone(), order.market as i64), order.clone());

    Some(tick)
}
