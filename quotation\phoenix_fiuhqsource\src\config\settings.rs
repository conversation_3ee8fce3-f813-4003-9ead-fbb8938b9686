use std::collections::HashMap;

use config::{Config, ConfigError, File};
use serde::Deserialize;

#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct Application {
    pub apphost: String,
    pub appport: i32,
}

#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct System {
    pub hsfiusocket: String,
    pub hkfiusocket: String,
    pub hsrestarttime: String,
    pub hkrestarttime: String,
    pub hsfiuapi: String,
    pub hkfiuapi: String,
    pub pullcodetime: String,
    pub hsfiuwebsocket: String,
    pub hkfiuwebsocket: String,
    pub stockcode: Vec<String>,
    pub token: String,
    pub version: String,
    pub logcenterserver: String,
    // pub vhost: String,
}

#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct Settings {
    pub application: Application,
    pub system: System,
    pub mysql: HashMap<String, String>,
}

impl Settings {
    pub fn new() -> Result<Self, ConfigError> {
        let s = Config::builder()
            // Start off by merging in the "default" configuration file
            .add_source(File::with_name("config/fiuhqsource.toml"))
            // // Add in the current environment file
            // // Default to 'development' env
            // // Note that this file is _optional_
            // .add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
            // // Add in a local configuration file
            // // This file shouldn't be checked in to git
            // .add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
            // // Add in settings from the environment (with a prefix of APP)
            // // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
            // .add_source(Environment::with_prefix("app"))
            // // You may also programmatically change settings
            // .set_override("database.url", "postgres://")?
            .build()
            .expect("build config file error");

        s.try_deserialize()
    }
}
