use crate::client::dbclient::DbClient;
// use crate::common::{get_exchange_id, get_inner_code};
use crate::common::StockInfo;
use crate::dataservice::entities::{prelude::*, *};
// use crate::fiuhq::hk::{HkStockBasicInfo, HkStockDefine, HkStockStatus};
// use crate::fiuhq::hs::{HsStockBasicInfo, HsStockDefine};
use anyhow::{anyhow, Result};
use common::logclient::LogClient;
use sea_orm::entity::prelude::*;
// use sea_orm::sea_query::Query;
use sea_orm::QueryFilter;
use serde_json::json;
use tracing::{error, info};
impl SysCommodity {
    #[allow(dead_code)]
    pub async fn select_by_inner_code(db: &DbClient, inner_code: String) -> Result<()> {
        let conn = db.get_conn();

        match SysCommodityEntity::find().filter(sys_commodity::Column::InnerCode.eq(inner_code)).one(conn).await {
            Ok(v) => {
                if v.is_some() {
                    Ok(())
                } else {
                    Err(anyhow!("None"))
                }
            }
            Err(err) => {
                error!("{:?}", err);
                if let Ok(client) = LogClient::get() {
                    client.push_error(&format!("select_by_inner_code error:{}", err.to_string())).await;
                }
                Err(err.into())
            }
        }
    }

    #[allow(dead_code)]
    pub async fn select_all_model(db: &DbClient) -> Vec<SysCommodity> {
        let conn = db.get_conn();
        match SysCommodityEntity::find().all(conn).await {
            Ok(v) => v,
            Err(err) => {
                error!("{:?}", err);
                if let Ok(client) = LogClient::get() {
                    client.push_error(&format!("select_all_model error:{}", err.to_string())).await;
                }
                vec![]
            }
        }
    }

    #[allow(dead_code)]
    pub async fn insert_many(db: &DbClient, data: &Vec<SysCommodity>) -> Result<()> {
        let conn = db.get_conn();
        let bash: Vec<Vec<SysCommodity>> = data.chunks(1024).map(|x| x.into()).collect();

        for v in bash.iter() {
            let models: Vec<sys_commodity::ActiveModel> = v.iter().map(|x| sys_commodity::ActiveModel::from_json(json!(x)).unwrap()).collect();

            match SysCommodityEntity::insert_many(models).exec(conn).await {
                Ok(v) => info!("{:?}", v),
                Err(err) => {
                    error!("{:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("insert_many error:{}", err.to_string())).await;
                    }
                }
            }
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn update_many(db: &DbClient, data: &Vec<SysCommodity>) {
        let conn = db.get_conn();
        for v in data.iter() {
            let act_mod = sys_commodity::ActiveModel::from_json(json!(v)).unwrap();
            // info!("{:?}", act_mod);
            match act_mod.update(conn).await {
                Ok(_) => continue,
                Err(err) => {
                    error!("{:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("update_many error:{}", err.to_string())).await;
                    }
                }
            }
        }
    }

    #[allow(dead_code)]
    pub async fn update(db: &DbClient, data: &Vec<StockInfo>) {
        let conn = db.get_conn();
        for v in data.iter() {
            //stock 停牌  长期停牌  退市:  0 正常 1	未上市 2	熔断 3	停牌 4	退市
            if v.security_status == 1 || v.security_status == 2 || v.security_status == 3 || v.security_status == 4 {
                match SysCommodityEntity::update_many()
                    .col_expr(sys_commodity::Column::ShowState, Expr::value(2))
                    .col_expr(sys_commodity::Column::TradeState, Expr::value(3))
                    .col_expr(sys_commodity::Column::SuspState, Expr::value(1))
                    .col_expr(sys_commodity::Column::ModifyDate, Expr::value(utility::timeutil::current_timestamp()))
                    .col_expr(sys_commodity::Column::Name, Expr::value(v.name.clone()))
                    .col_expr(sys_commodity::Column::Currency, Expr::value(v.currency.clone()))
                    .col_expr(sys_commodity::Column::HandsNum, Expr::value(v.lot_size))
                    .filter(sys_commodity::Column::MarketId.is_in([101, 102, 103]))
                    .filter(sys_commodity::Column::Code.eq(v.inner_code.split("_").collect::<Vec<&str>>()[0]))
                    .exec(conn)
                    .await
                {
                    Ok(_v) => continue, //info!("{:?}", v),
                    Err(err) => {
                        error!("{:?}", err);
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("update error:{}", err.to_string())).await;
                        }
                    }
                }
            } else {
                match SysCommodityEntity::update_many()
                    .col_expr(sys_commodity::Column::ShowState, Expr::value(1))
                    .col_expr(sys_commodity::Column::TradeState, Expr::value(1))
                    .col_expr(sys_commodity::Column::SuspState, Expr::value(0))
                    .col_expr(sys_commodity::Column::ModifyDate, Expr::value(utility::timeutil::current_timestamp()))
                    .col_expr(sys_commodity::Column::Name, Expr::value(v.name.clone()))
                    .col_expr(sys_commodity::Column::Currency, Expr::value(v.currency.clone()))
                    .col_expr(sys_commodity::Column::HandsNum, Expr::value(v.lot_size))
                    .filter(sys_commodity::Column::MarketId.is_in([101, 102, 103]))
                    .filter(sys_commodity::Column::Code.eq(v.inner_code.split("_").collect::<Vec<&str>>()[0]))
                    .exec(conn)
                    .await
                {
                    Ok(_) => continue, //info!("{:?}", v),
                    Err(err) => {
                        error!("{:?}", err);
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("update_many error:{}", err.to_string())).await;
                        }
                    }
                }
            }
        }
    }

    // pub async fn convert_hs_stock_define(data: &Vec<HsStockDefine>) -> Vec<SysCommodity> {
    //     let mut sys_commodity = SysCommodity::default();
    //     let mut models = Vec::new();

    //     for v in data.iter() {
    //         sys_commodity.code = v.symbol.split(".").collect::<Vec<&str>>()[0].to_owned();
    //         sys_commodity.name = v.name.to_owned();
    //         sys_commodity.dic_type = 73;
    //         sys_commodity.currency = v.currency.to_owned();
    //         sys_commodity.show_state = 1;
    //         sys_commodity.trade_state = 1;
    //         if v.market == Market::SH as i32 {
    //             sys_commodity.market_id = 101;
    //         } else {
    //             sys_commodity.market_id = 102;
    //         }
    //         // sys_commodity.goods_id =
    //         sys_commodity.create_date = utility::timeutil::current_timestamp();
    //         sys_commodity.modify_date = utility::timeutil::current_timestamp();
    //         sys_commodity.modify_name = "超级管理员".to_owned();
    //         sys_commodity.business_type = 3;
    //         sys_commodity.hands_num = v.lot_size;
    //         sys_commodity.r#type = 3; //沪深
    //         sys_commodity.inner_code = format!("{}_{}",v.symbol.split(".").collect::<Vec<&str>>()[0], get_exchange_id(v.market as i64));
    //         // ASH 以人民币交易的股票（主板）
    //         // KSH 以人民币交易的股票（科创板）
    //         sys_commodity.stock_type = 1;
    //         sys_commodity.susp_state = Some(0);
    //         models.push(sys_commodity.clone());
    //     }
    //     models
    // }

    // pub async fn convert_hk_stock_define(data: &Vec<HkStockDefine>) -> Vec<SysCommodity> {
    //     let mut sys_commodity = SysCommodity::default();
    //     let mut models = Vec::new();

    //     for v in data.iter() {
    //         let symbol = if v.symbol.is_string() {
    //             v.symbol.as_str().unwrap().to_owned()
    //         } else {
    //             continue;
    //         };
    //         sys_commodity.code = symbol.split(".").collect::<Vec<&str>>()[0].to_owned();
    //         sys_commodity.name = v.name.as_str().unwrap_or_default().to_owned();
    //         sys_commodity.dic_type = 73;
    //         sys_commodity.currency = v.currency.as_str().unwrap_or_default().to_owned();
    //         sys_commodity.show_state = 1;
    //         sys_commodity.trade_state = 1;
    //         sys_commodity.market_id = 103;
    //         // sys_commodity.goods_id =
    //         sys_commodity.create_date = utility::timeutil::current_timestamp();
    //         sys_commodity.modify_date = utility::timeutil::current_timestamp();
    //         sys_commodity.modify_name = "超级管理员".to_owned();
    //         sys_commodity.business_type = 3;
    //         sys_commodity.hands_num = v.lot_size.as_i64().unwrap_or_default().to_owned() as i32;
    //         sys_commodity.r#type = 1; //港股
    //         sys_commodity.inner_code = format!("{}_XHKG", symbol.split(".").collect::<Vec<&str>>()[0]);
    //         // ASH 以人民币交易的股票（主板）
    //         // KSH 以人民币交易的股票（科创板）
    //         sys_commodity.stock_type = 2;
    //         sys_commodity.susp_state = Some(0);
    //         models.push(sys_commodity.clone());
    //     }
    //     models
    // }

    #[allow(dead_code)]
    pub async fn convert_stock_basic_to_model(stockinfo: &Vec<StockInfo>) -> Vec<SysCommodity> {
        let mut sys_commodity = SysCommodity::default();
        let mut models = Vec::new();

        for v in stockinfo.iter() {
            sys_commodity.code = v.outer_code.split(".").collect::<Vec<&str>>()[0].to_owned(); //600000.sh
            sys_commodity.name = v.name.to_owned();
            sys_commodity.dic_type = 73;
            sys_commodity.currency = v.currency.to_owned();
            sys_commodity.show_state = 1;
            sys_commodity.trade_state = 1;
            sys_commodity.market_id = v.market_id;
            // sys_commodity.goods_id =
            sys_commodity.create_date = utility::timeutil::current_timestamp();
            sys_commodity.modify_date = utility::timeutil::current_timestamp();
            sys_commodity.modify_name = "超级管理员".to_owned();
            sys_commodity.business_type = 3;
            sys_commodity.hands_num = v.lot_size;
            sys_commodity.status = 1;
            if v.market_id == 103 {
                // sys_commodity.stock_type = 2;
                sys_commodity.r#type = 1; //港股
            } else {
                // sys_commodity.stock_type = 1;
                sys_commodity.r#type = 3; //沪深
            }
            sys_commodity.stock_type = v.inner_stock_type as i16;
            sys_commodity.inner_code = v.inner_code.to_owned();
            sys_commodity.susp_state = Some(0);
            models.push(sys_commodity.clone());
        }
        models
    }
}
