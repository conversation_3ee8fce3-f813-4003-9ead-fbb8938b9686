use crate::client::dbclient::DbClient;
use crate::common::StockInfo;
use crate::dataservice::entities::{prelude::*, *};
use anyhow::Result;
use common::logclient::LogClient;
use rust_decimal::prelude::*;
use sea_orm::entity::prelude::*;
// use sea_orm::sea_query::Query;
// use sea_orm::{QueryFilter, QueryOrder, QuerySelect, Set, TransactionTrait};
use serde_json::json;
use tracing::{error, info};
impl SysCommodityExt {
    #[allow(dead_code)]
    pub async fn select_all_model(db: &DbClient) -> Vec<SysCommodityExt> {
        let conn = db.get_conn();
        match SysCommodityExtEntity::find().all(conn).await {
            Ok(v) => v,
            Err(err) => {
                error!("{:?}", err);
                vec![]
            }
        }
    }

    #[allow(dead_code)]
    pub async fn insert_many(db: &DbClient, data: &Vec<SysCommodityExt>) -> Result<()> {
        let conn = db.get_conn();
        let bash: Vec<Vec<SysCommodityExt>> = data.chunks(1024).map(|x| x.into()).collect();

        for v in bash.iter() {
            let models: Vec<sys_commodity_ext::ActiveModel> = v.iter().map(|x| sys_commodity_ext::ActiveModel::from_json(json!(x)).unwrap()).collect();

            match SysCommodityExtEntity::insert_many(models).exec(conn).await {
                Ok(v) => info!("{:?}", v),
                Err(err) => {
                    error!("{:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("SysCommodityExtEntity::insert_many error:{}", err.to_string())).await;
                    }
                }
            }
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn to_model(info: &Vec<StockInfo>, sys_commodity: &Vec<SysCommodity>) -> Vec<SysCommodityExt> {
        let mut resp = Vec::new();

        for v in info.iter() {
            let mut sys_ext = SysCommodityExt::default();
            let ret = sys_commodity.iter().find(|x| x.inner_code == v.inner_code);
            if ret.is_none() {
                continue;
            }
            let commodity = ret.unwrap();
            sys_ext.commodity_id = commodity.id;
            sys_ext.hands_num = v.lot_size;
            sys_ext.currency = v.currency.to_owned();
            sys_ext.bond_wc = Decimal::from(1);
            sys_ext.max_hold = Decimal::from(10000000);
            if v.inner_stock_type == 3 {
                resp.push(sys_ext);
            } else {
                //风控规则，控制单笔下单，最大金额max_money，最大数量max_value，最小数量min_value
                sys_ext.min_value = Decimal::from(v.lot_size);
                if v.inner_stock_type == 2 {
                    sys_ext.max_money = 20000000;
                    sys_ext.max_value = Decimal::from(2000000);
                } else {
                    sys_ext.max_money = 3000000;
                    sys_ext.max_value = Decimal::from(300000);
                    if v.inner_stock_type == 5 {
                        sys_ext.min_value = Decimal::from(200);
                        sys_ext.max_value = Decimal::from(100000);
                    }
                }
                resp.push(sys_ext);
            }
        }
        resp
    }
}
