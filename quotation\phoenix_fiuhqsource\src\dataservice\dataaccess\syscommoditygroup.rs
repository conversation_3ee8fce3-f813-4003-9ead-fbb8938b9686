use crate::client::dbclient::DbClient;
// use crate::common::{get_exchange_id, get_inner_code};
// use crate::common::{Market, SecurityType, StockInfo};
use crate::dataservice::entities::{prelude::*, *};
// use crate::fiuhq::hk::{HkStockBasicInfo, HkStockDefine, HkStockStatus};
// use crate::fiuhq::hs::{HsStockBasicInfo, HsStockDefine};
use crate::fiuhq::IndustryList;
use anyhow::Result;
use sea_orm::entity::prelude::*;
// use sea_orm::sea_query::Query;
// use sea_orm::{QueryFilter, QueryOrder, QuerySelect, Set, TransactionTrait};
use serde_json::json;
use tracing::{error, info};
impl SysCommodityGroup {
    #[allow(dead_code)]
    pub async fn select_all_model(db: &DbClient) -> Vec<SysCommodityGroup> {
        let conn = db.get_conn();
        match SysCommodityGroupEntity::find().all(conn).await {
            Ok(v) => v,
            Err(err) => {
                error!("{:?}", err);
                vec![]
            }
        }
    }

    #[allow(dead_code)]
    pub async fn insert_many(db: &DbClient, data: &Vec<SysCommodityGroup>) -> Result<()> {
        let conn = db.get_conn();
        let models: Vec<sys_commodity_group::ActiveModel> = data.iter().map(|x| sys_commodity_group::ActiveModel::from_json(json!(x)).unwrap()).collect();

        match SysCommodityGroupEntity::insert_many(models).exec(conn).await {
            Ok(v) => info!("{:?}", v),
            Err(err) => error!("{:?}", err),
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn convert_industry_to_model(industry_list: &Vec<IndustryList>) -> Vec<SysCommodityGroup> {
        let mut sys_commodity_group = SysCommodityGroup::default();
        let mut models = Vec::new();

        for v in industry_list.iter() {
            sys_commodity_group.business_type = 3;
            sys_commodity_group.code = v.symbol.to_owned();
            sys_commodity_group.name = v.name.to_owned();
            sys_commodity_group.status = 1;
            sys_commodity_group.create_date = utility::timeutil::current_naive_time().and_utc().timestamp();
            if v.symbol.contains(".hk") {
                sys_commodity_group.market_id = 103;
                sys_commodity_group.r#type = 1;
                // sys_commodity_group.inner_code = get_inner_code(v.symbol.split(".").collect::<Vec<_>>()[0].to_owned(), Market::HK as i64);
                sys_commodity_group.inner_code = format!("{}_HK", v.symbol.split(".").collect::<Vec<_>>()[0])
            } else if v.symbol.contains(".sz") {
                sys_commodity_group.market_id = 102;
                sys_commodity_group.r#type = 3;
                // sys_commodity_group.inner_code = get_inner_code(v.symbol.split(".").collect::<Vec<_>>()[0].to_owned(), Market::SZ as i64);
                sys_commodity_group.inner_code = format!("{}_HS", v.symbol.split(".").collect::<Vec<_>>()[0])
            } else {
                sys_commodity_group.market_id = 101;
                sys_commodity_group.r#type = 3;
                // sys_commodity_group.inner_code = get_inner_code(v.symbol.split(".").collect::<Vec<_>>()[0].to_owned(), Market::SH as i64);
                sys_commodity_group.inner_code = format!("{}_HS", v.symbol.split(".").collect::<Vec<_>>()[0])
            }

            if v.concept_flag == "N" {
                //行业板块
                sys_commodity_group.cate = 3;
            }

            models.push(sys_commodity_group.clone());
        }
        models
    }
}
