use crate::client::dbclient::DbClient;
// use crate::common::{get_exchange_id, get_inner_code};
// use crate::common::{Market, SecurityType, StockInfo};
use crate::dataservice::entities::{prelude::*, *};
// use crate::fiuhq::hk::{HkStockBasicInfo, HkStockDefine, HkStockStatus};
// use crate::fiuhq::hs::{HsStockBasicInfo, HsStockDefine};
use anyhow::Result;
use sea_orm::entity::prelude::*;
// use sea_orm::sea_query::Query;
// use sea_orm::{QueryFilter, QueryOrder, QuerySelect, Set, TransactionTrait};
use serde_json::json;
use tracing::{error, info};
impl SysCommodityGroupList {
    #[allow(dead_code)]
    pub async fn select_all_model(db: &DbClient) -> Vec<SysCommodityGroupList> {
        let conn = db.get_conn();
        match SysCommodityGroupListEntity::find().all(conn).await {
            Ok(v) => v,
            Err(err) => {
                error!("{:?}", err);
                vec![]
            }
        }
    }

    #[allow(dead_code)]
    pub async fn insert_many(db: &DbClient, data: &Vec<SysCommodityGroupList>) -> Result<()> {
        let conn = db.get_conn();
        let models: Vec<sys_commodity_group_list::ActiveModel> = data.iter().map(|x| sys_commodity_group_list::ActiveModel::from_json(json!(x)).unwrap()).collect();

        match SysCommodityGroupListEntity::insert_many(models).exec(conn).await {
            Ok(v) => info!("{:?}", v),
            Err(err) => error!("{:?}", err),
        }

        Ok(())
    }
}
