use anyhow::Result;
use sea_orm::entity::prelude::*;
use sea_orm::QueryFilter;
use serde_json::json;

use crate::client::dbclient::DbClient;
// use crate::common::SecurityType;
use crate::common::StockInfo;
use crate::dataservice::entities::{prelude::*, *};
// use crate::fiuhq::hk::{HkStockBasicInfo, HkStockDefine, HkStockStatus};
// use crate::fiuhq::hs::{HsStockBasicInfo, HsStockDefine};
use tracing::{error, info};
use utility::timeutil::current_date;
// use crate::common::{get_exchange_id, get_inner_code};

impl SysCommodityTmp {
    #[allow(dead_code)]
    pub async fn delete_from_exchange(db: &DbClient /*, exchange: i32*/) -> Result<()> {
        let conn = db.get_conn();
        match SysCommodityTmpEntity::delete_many()
            .filter(sys_commodity_tmp::Column::ExchangeType.is_in([1, 2]) /*.eq(exchange)*/)
            .exec(conn)
            .await
        {
            Ok(_v) => {}
            Err(_err) => {}
        }
        Ok(())
    }

    #[allow(dead_code)]
    pub async fn insert_many(db: &DbClient, data: &Vec<SysCommodityTmp>) -> Result<()> {
        let conn = db.get_conn();
        let models: Vec<sys_commodity_tmp::ActiveModel> = data.iter().map(|x| sys_commodity_tmp::ActiveModel::from_json(json!(x)).unwrap()).collect();

        match SysCommodityTmpEntity::insert_many(models).exec(conn).await {
            Ok(v) => info!("{:?}", v),
            Err(err) => error!("{:?}", err),
        }

        Ok(())
    }

    // //A股
    // pub async fn convert_stockdetail_tmp(data: &Vec<HsStockDefine>) -> Vec<SysCommodityTmp> {
    //     let mut sys_commodity_tmp = SysCommodityTmp::default();
    //     let mut sys_commodity_tmps = Vec::new();

    //     for v in data.iter() {
    //         sys_commodity_tmp.inner_code = format!("{}_{}",v.symbol.split(".").collect::<Vec<&str>>()[0], get_exchange_id(v.market as i64));
    //         sys_commodity_tmp.exchange_type = 1;
    //         // info!("{}======={}", v.symbol, v.status.len());
    //         if v.r#type == SecurityType::Index as i32 {//指数
    //             sys_commodity_tmp.trade_status = "TRADE".to_owned();
    //         } else {
    //             if v.market == Market::SH as i32 {
    //                 if v.status[3..4] == "D".to_owned() { //国内正常交易产品
    //                     sys_commodity_tmp.trade_status = "TRADE".to_owned();
    //                 } else if v.status[3..4] == "P".to_owned() {//表示退市整理产品
    //                     sys_commodity_tmp.trade_status = "DELISTED".to_owned();
    //                 }
    //             } else {
    //                 if v.status == "1" {//停牌
    //                     sys_commodity_tmp.trade_status = "HALT".to_owned();
    //                 } else if v.status == "10" {//退市整理期
    //                     sys_commodity_tmp.trade_status = "DELISTED".to_owned();
    //                 } else if v.status == "" {
    //                     sys_commodity_tmp.trade_status = "TRADE".to_owned();
    //                 }
    //             }
    //             sys_commodity_tmp.process_date = current_date();
    //             sys_commodity_tmps.push(sys_commodity_tmp.clone());
    //         }
    //     }
    //     // info!("{:#?}", sys_commodity_tmps);
    //     sys_commodity_tmps

    // }

    // //港股
    // pub async fn convert_stockdefine_tmp(data: &Vec<HkStockDefine>, status: &Vec<HkStockStatus>) -> Vec<SysCommodityTmp> {
    //     let mut sys_commodity_tmp = SysCommodityTmp::default();
    //     let mut sys_commodity_tmps = Vec::new();

    //     for v in data.iter() {
    //         let symbol = if v.symbol.is_string() {
    //             v.symbol.as_str().unwrap().to_owned()
    //         } else {
    //             continue
    //         };
    //         sys_commodity_tmp.inner_code = format!("{}_XHKG", symbol.split(".").collect::<Vec<&str>>()[0]);

    //         //停牌: 股票状态：2（停牌）、3（复牌）
    //         if status.iter().find(|x| x.symbol == symbol && x.suspension == "2".to_owned()).is_some() {
    //             sys_commodity_tmp.trade_status = "HALT".to_owned();
    //         } else {
    //             sys_commodity_tmp.trade_status = "TRADE".to_owned();
    //         }

    //         sys_commodity_tmp.exchange_type = 2;
    //         sys_commodity_tmp.process_date = current_date();
    //         sys_commodity_tmps.push(sys_commodity_tmp.clone());
    //     }

    //     sys_commodity_tmps
    // }

    #[allow(dead_code)]
    pub async fn convert_stock_basic_to_model(stockinfo: &Vec<StockInfo>) -> Vec<SysCommodityTmp> {
        let mut sys_commodity_tmp = SysCommodityTmp::default();
        let mut sys_commodity_tmps = Vec::new();

        for v in stockinfo.iter() {
            sys_commodity_tmp.inner_code = v.inner_code.clone();
            sys_commodity_tmp.process_date = current_date();
            if v.market_id == 103 {
                sys_commodity_tmp.exchange_type = 2;
            } else {
                sys_commodity_tmp.exchange_type = 1;
            }

            // 0正常  1未上市  2熔断  3停牌  4退市
            if v.security_status == 0 {
                sys_commodity_tmp.trade_status = "TRADE".to_owned();
            } else if v.security_status == 1 {
                sys_commodity_tmp.trade_status = "STOPT".to_owned();
            } else if v.security_status == 2 {
                sys_commodity_tmp.trade_status = "HALT".to_owned();
            } else if v.security_status == 3 {
                sys_commodity_tmp.trade_status = "SUSP".to_owned();
            } else {
                sys_commodity_tmp.trade_status = "DELISTED".to_owned();
            }

            sys_commodity_tmps.push(sys_commodity_tmp.clone());
        }
        sys_commodity_tmps
    }
}
