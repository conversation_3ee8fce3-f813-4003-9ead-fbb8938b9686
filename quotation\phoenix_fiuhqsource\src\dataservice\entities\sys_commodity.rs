//! `SeaORM` Entity. Generated by sea-orm-codegen 0.12.15

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Default, serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "sys_commodity")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub code: String,
    pub name: String,
    pub dic_type: i32,
    pub currency: String,
    pub show_state: i8,
    pub trade_state: i8,
    pub market_id: i64,
    pub goods_id: i64,
    pub create_date: i64,
    pub modify_date: i64,
    pub modify_name: String,
    pub business_type: i8,
    pub sort: i32,
    pub status: i8,
    pub import_template: i64,
    pub r#type: i32,
    pub hands_num: i32,
    #[sea_orm(column_type = "Decimal(Some((6, 2)))")]
    pub face_num: Decimal,
    pub inner_code: String,
    pub trade_flag: i8,
    pub stock_type: i16,
    pub susp_state: Option<i8>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
