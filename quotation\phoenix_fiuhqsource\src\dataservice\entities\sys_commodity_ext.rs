//! `SeaORM` Entity, @generated by sea-orm-codegen 1.0.1

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Default, serde::Serialize, serde::Deserialize)]
#[sea_orm(table_name = "sys_commodity_ext")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub commodity_id: i64,
    pub first_trade_date: i64,
    pub first_notify_date: i64,
    pub end_date: i64,
    pub last_trade_date: i64,
    pub day_close_time: String,
    pub overnight_time: String,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub multiplier: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub price_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 8)))")]
    pub price_unit_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub max_value: Decimal,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub max_hold: Decimal,
    pub hands_num: i32,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub bond_money1: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub bond_gy: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 5)))")]
    pub bond_wc: Decimal,
    pub currency: String,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub member_min: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub member_max: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub user_min: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub user_max: Decimal,
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub bond_money2: Decimal,
    pub channel_goods_no: String,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub par_value: Decimal,
    pub max_money: i64,
    #[sea_orm(column_type = "Decimal(Some((16, 2)))")]
    pub min_value: Decimal,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
