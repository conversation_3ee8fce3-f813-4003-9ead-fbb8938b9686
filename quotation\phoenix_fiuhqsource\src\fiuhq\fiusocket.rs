// use crate::common::{insert_cn_order, insert_pb_order};
// use crate::protofiles::{CnOrder, CnStock, PbOrder, PbSnapshot};
use anyhow::{anyhow, Result};
use prost::bytes::BytesMut;
use prost::{bytes, Message};
use tokio::sync::broadcast::Sender;
use tokio::{io::AsyncReadExt, net::TcpStream};
use tracing::{error, info};

use common::logclient::log_error;

use crate::codec::{BtType, FiuHeader, FiuType, MsgHeader};
use crate::protofiles::{hk::*, hs::*};

#[derive(Clone)]
#[allow(dead_code)]
pub enum Exec {
    Stop = 0,
    Start = 1,
}

#[derive(Clone, Debug)]
#[allow(dead_code)]
pub struct HqDataSource {
    hs_uri: String,
    hk_uri: String,
    pub hs_ex: i32,
    pub hk_ex: i32,
    tx_stock: Sender<CnStock>,
    tx_snapshot: Sender<PbSnapshot>,
    tx_cn_order: Sender<CnOrder>,
    tx_pb_order: Sender<PbOrder>,
}

impl HqDataSource {
    #[allow(dead_code)]
    pub async fn new(hs_uri: &String, hk_uri: &String, tx_stock: Sender<CnStock>, tx_snapshot: Sender<PbSnapshot>, tx_cn_order: Sender<CnOrder>, tx_pb_order: Sender<PbOrder>) -> HqDataSource {
        let client = HqDataSource {
            hs_uri: hs_uri.to_owned(),
            hk_uri: hk_uri.to_owned(),
            hs_ex: 0,
            hk_ex: 0,
            tx_stock,
            tx_snapshot,
            tx_cn_order,
            tx_pb_order,
        };

        client
    }

    #[allow(dead_code)]
    pub async fn hs_hq_process(&mut self) -> Result<()> {
        let mut stream = match TcpStream::connect(self.hs_uri.to_owned()).await {
            Ok(tcp_stream) => {
                info!("连接成功: {}", self.hs_uri);
                self.hs_ex = Exec::Start as i32;
                tcp_stream
            }
            Err(err) => {
                error!("{:?}", err);
                log_error(&format!("connect hs fiuhq err: {:?}", err)).await;
                return Err(anyhow!("{:?}", err));
            }
        };

        let mut buf = [0u8; 3];

        // tokio::spawn(async move {
        loop {
            tokio::select! {
                result = stream.read_exact(&mut buf) => match result {
                    Ok(size) => {
                        if size != 3 {
                            info!("size: {}", size);
                            continue
                        }
                        let msg_head = MsgHeader::analysis_head(&buf);
                        if msg_head.us_length < 3 {
                            continue
                        }
                        let msg_len = (msg_head.us_length - 3) as usize;
                        let mut msg_buf = vec![0u8; msg_len];

                        match stream.read_exact(&mut msg_buf).await {
                            Ok(_size) => {
                                // if size != msg_len as usize {
                                //     info!("size: {}", size);
                                //     continue
                                // }
                            }
                            Err(e) => {
                                info!("error = {:?}", e);
                                self.hs_ex = Exec::Stop as i32;
                                break
                            }
                        }

                        let hby = bytes::BytesMut::from_iter(msg_buf);

                        if msg_head.bt_type == BtType::ZlCommonCode as u8 {
                            // info!("{:?}", CnCode::decode(hby).unwrap());
                        } else if msg_head.bt_type == BtType::ZlCommonStock as u8 {
                            // info!("{:?}", CnStock::decode(hby).unwrap());
                            let _ = self.tx_stock.send(CnStock::decode(hby).unwrap_or_default());
                        } else if msg_head.bt_type == BtType::ZlCommonOrder as u8 {
                            // info!("{:?}", CnOrder::decode(hby).unwrap());
                            let _ = self.tx_cn_order.send(CnOrder::decode(hby).unwrap_or_default());
                            // insert_cn_order(&CnOrder::decode(hby).unwrap_or_default()).await;
                        } else if msg_head.bt_type == BtType::ZlClientHeartbeat as u8 {
                            // info!("心跳: {:?}", msg_head);
                        } else if msg_head.bt_type == BtType::ZlClientKcclose as u8 {
                            // info!("{:?}", KcClose::decode(hby).unwrap());
                        } else if msg_head.bt_type == BtType::ZlCodeEnd as u8 {
                            info!("ZL_CODE_END: 标识码表接收完毕: {:?}", msg_head);
                        } else if msg_head.bt_type == BtType::FiuSouth as u8 {
                            // info!("{:?}", South::decode(hby).unwrap());
                        }
                    }
                    Err(e) => {
                        info!("error = {:?}", e);
                        self.hs_ex = Exec::Stop as i32;
                        break
                    }
                }
                //定时心跳
            }
        }
        // });

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn hk_hq_process(&mut self) -> Result<()> {
        let mut stream = match TcpStream::connect(self.hk_uri.to_owned()).await {
            Ok(tcp_stream) => {
                info!("连接成功: {}", self.hk_uri);
                self.hk_ex = Exec::Start as i32;
                tcp_stream
            }
            Err(err) => {
                error!("{:?}", err);
                log_error(&format!("connect hk fiuhq err: {:?}", err)).await;
                return Err(anyhow!("{:?}", err));
            }
        };

        let mut buf = [0u8; 3];
        // tokio::spawn(async move {
        loop {
            tokio::select! {
                result = stream.read_exact(&mut buf) => match result {
                    Ok(size) => {
                        if size != 3 {
                            info!("size: {}", size);
                            continue
                        }
                        let msg_head = FiuHeader::analysis_head(&buf);
                        if msg_head.us_len < 3 {
                            continue
                        }
                        let msg_len = (msg_head.us_len - 3) as usize;
                        let mut msg_buf = vec![0u8; msg_len];
                        match stream.read_exact(&mut msg_buf).await {
                            Ok(_size) => {
                                // if size != msg_len as usize {
                                //     info!("size: {}", size);
                                //     continue
                                // }
                            }
                            Err(e) => {
                                info!("error = {:?}", e);
                                self.hk_ex = Exec::Stop as i32;
                                break
                            }
                        };
                        let hby = bytes::BytesMut::from_iter(msg_buf);
                        pag_hk_msg(msg_head.c_type, hby, self.tx_snapshot.clone(), self.tx_pb_order.clone()).await;
                    }
                    Err(e) => {
                        info!("error = {:?}", e);
                        self.hk_ex = Exec::Stop as i32;
                        break
                    }
                }
                //定时心跳
            }
        }
        // });

        Ok(())
    }
}

pub async fn pag_hk_msg(c_type: u8, hby: BytesMut, tx_hk: Sender<PbSnapshot>, tx_order: Sender<PbOrder>) {
    match c_type {
        v if v == FiuType::FiuHeartbeat as u8 => {
            // info!("Heartbeat");
        }
        v if v == FiuType::FiuCode as u8 => { // PBStockDefine
             // info!("{:?}", PbStockDefine::decode(hby).unwrap_or_default());
        }
        v if v == FiuType::FiuSnapshot as u8 => {
            // PBSnapshot
            // info!("{:?}", PbSnapshot::decode(hby).unwrap_or_default());
            let _ = tx_hk.send(PbSnapshot::decode(hby).unwrap_or_default());
        }
        v if v == FiuType::FiuOrder as u8 => {
            // PBOrderItem PBOrder
            // info!("{:?}", PbOrder::decode(hby)?);
            let _ = tx_order.send(PbOrder::decode(hby).unwrap_or_default());
            // insert_pb_order(&PbOrder::decode(hby).unwrap_or_default()).await;
        }
        v if v == FiuType::FiuIndexDefine as u8 => { // PBIndexDefine
             // info!("{:?}", PbIndexDefine::decode(hby)?);
        }
        v if v == FiuType::FiuTrade as u8 => { // PBTrade
             // info!("{:?}", PbTrade::decode(hby)?);
        }
        v if v == FiuType::FiuIndex as u8 => { // PBIndex
             // info!("{:?}", PbIndex::decode(hby).unwrap_or_default());
        }
        v if v == FiuType::FiuVcmTrigger as u8 => { // PBVcmTrigger
             // info!("{:?}", PbVcmTrigger::decode(hby)?);
        }
        v if v == FiuType::FiuOrderBroker as u8 => { // PBOrderBroker
             // info!("{:?}", PbOrderBroker::decode(hby)?);
        }
        v if v == FiuType::FiuConnectBalance as u8 => { // PBConnectBalance
             // info!("{:?}", PbConnectBalance::decode(hby)?);
        }
        v if v == FiuType::FiuConnectTurnover as u8 => { // PBConnectTurnover
             // info!("{:?}", PbConnectTurnover::decode(hby)?);
        }
        v if v == FiuType::FiuSecurityStatus as u8 => { //PBSecurityStatus
             // info!("{:?}", PbSecurityStatus::decode(hby)?);
        }
        v if v == FiuType::FiuEquipPrice as u8 => { // PBEquipPrice
             // info!("{:?}", PbEquipPrice::decode(hby)?);
        }
        v if v == FiuType::FiuOrderImbalance as u8 => { // PBOrderImbalance
             // info!("{:?}", PbOrderImbalance::decode(hby)?);
        }
        v if v == FiuType::FiuTradeSessionStatus as u8 => { // PBTradeSessionStatus
             // info!("{:?}", PbTradeSessionStatus::decode(hby)?);
        }
        v if v == FiuType::FiuRefPrice as u8 => { // PBRefPrice
             // info!("{:?}", PbRefPrice::decode(hby)?);
        }
        v if v == FiuType::FiuOddLot as u8 => { // PBOddLot
             // info!("{:?}", PbOddLot::decode(hby)?);
        }
        v if v == FiuType::FiuMarketTurnOver as u8 => { // PBMarketTurnOver
             // info!("{:?}", PbMarketTurnOver::decode(hby)?);
        }
        _ => {}
    }
}
