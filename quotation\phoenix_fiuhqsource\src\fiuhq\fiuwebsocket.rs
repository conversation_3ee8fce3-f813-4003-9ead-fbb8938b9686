use reqwest::Client;
// use reqwest_websocket::WebSocket;
use reqwest_websocket::{Message, RequestBuilderExt};
use serde_json::json;
use std::collections::HashMap;
use tokio::sync::broadcast::Sender;
use tokio::sync::RwLock;
use tracing::{error, info};

use futures_util::{SinkExt, TryStreamExt};
use lazy_static::lazy_static;
use prost::{
    bytes::{self, BytesMut},
    Message as ProMsg,
};

use common::logclient::log_error;
use protoes::hqmsg::YsHqInfo;

use super::IndustryList;
use crate::protofiles::fiuwebsocket::{Connect, HkExtendSnap, HkIndexQuote, HkOrder, HkSnapshot, HsExtendSnap, HsIndexQuote, HsOrder, HsSnapshot};
use crate::{
    common::{get_exchange_id, get_inner_code, insert_hk_extend_snap, insert_hs_extend_snap, Market, StockInfo},
    config::settings::Settings,
};

lazy_static! {
    static ref HS_ORDER: RwLock<HashMap<String, HsOrder>> = RwLock::new(HashMap::new());
    static ref HK_ORDER: RwLock<HashMap<String, HkOrder>> = RwLock::new(HashMap::new());
    static ref HS_TICK: RwLock<HashMap<String, YsHqInfo>> = RwLock::new(HashMap::new());
    static ref HK_TICK: RwLock<HashMap<String, YsHqInfo>> = RwLock::new(HashMap::new());
    static ref HS_EXTEND_SNAP: RwLock<HashMap<String, HsExtendSnap>> = RwLock::new(HashMap::new());
    static ref HK_EXTEND_SNAP: RwLock<HashMap<String, HkExtendSnap>> = RwLock::new(HashMap::new());
    static ref HS_INDUSTRY_LIST: RwLock<Vec<String>> = RwLock::new(Vec::new());
    static ref HK_INDUSTRY_LIST: RwLock<Vec<String>> = RwLock::new(Vec::new());
}

#[allow(dead_code)]
enum Command {
    HeartBeat = 0,                       //心跳包
    SubscribeSnapshot = 1,               //订阅快照
    UnsubscribeSnapshot = 2,             //取消订阅快照
    SubscribeTrade = 3,                  //订阅trade
    UnSubscribeTrade = 4,                //取消订阅trade
    SubscribeOrder = 5,                  //订阅order
    UnSubscribeOrder = 6,                //取消订阅order
    SubscribeTimeShare = 7,              //订阅分时
    UnSubscribeTimeShare = 8,            //取消订阅分时
    SubscribeKline = 9,                  //订阅kline
    UnSubscribeKline = 10,               //取消订阅kline
    SubscribeOrderBroker = 12,           //订阅经纪席位
    UnSubscribeOrderBroker = 13,         //取消订阅经纪席位
    SubscribeCapitalFlow = 16,           //订阅资金流向
    UnSubscribeCapitalFlow = 17,         //取消订阅资金流向
    SubscribeExtendSnap = 18,            //订阅扩展行情
    UnSubscribeExtendSnap = 19,          //取消订阅扩展行情
    SubscribeIndexQuote = 20,            //订阅指数quote
    UnSubscribeIndexQuote = 21,          //取消订阅指数quote
    SubscribeIndustryRankList = 24,      //订阅板块
    UnSubscribeIndustryRankList = 25,    //取消订阅板块
    SubscribeRankingList = 26,           //订阅股票排名
    UnSubscribeRankingList = 27,         //取消订阅股票排名
    SubscribeTradeStatisticsList = 28,   //订阅成交统计
    UnSubscribeTradeStatisticsList = 29, //取消订阅成交统计
    SubscribeStockStatus = 30,           //订阅股票状态
    UnSubscribeStockStatus = 31,         //取消订阅股票状态
    SubscribeMultiMarketPremium = 36,    //订阅多市场溢价
    UnSubscribeMultiMarketPremium = 37,  //取消订阅多市场溢价
}

#[allow(dead_code)]
enum Protocol {
    HeartBeat = 1000,           //心跳响应
    Snapshot = 1001,            //快照响应
    Order = 1002,               //挂单响应
    Trade = 1003,               //逐笔响应
    TimeShare = 1004,           //分时响应
    Kline = 1005,               //K线响应
    Resp = 1006,                //消息返回响应
    OrderBroker = 1007,         //经纪席位响应
    CapitalFlow = 1009,         //资金流向响应
    ExtendSnap = 1010,          //扩展行情响应
    IndexQuote = 1011,          //指数行情响应
    IndustryRankList = 1013,    //板块响应
    RankingList = 1014,         //股票排名响应
    TradeStatisticsList = 1015, //成交统计响应
    StockStatus = 1016,         //股票状态响应
    Connect = 1017,             //连接成功响应
    MultiMarketPremium = 1021,  //多市场溢价响应
}

#[allow(dead_code)]
#[derive(Clone)]
pub struct FiuWeb {
    pub indexlist: Vec<String>,
    pub hs_url: String,
    pub hk_url: String,
    pub token: String,
    pub version: String,
}

impl FiuWeb {
    #[allow(dead_code)]
    pub fn new(setting: &Settings) -> Self {
        FiuWeb {
            indexlist: setting.system.stockcode.to_owned(),
            hs_url: setting.system.hsfiuwebsocket.to_owned(),
            hk_url: setting.system.hkfiuwebsocket.to_owned(),
            token: setting.system.token.to_owned(),
            version: setting.system.version.to_owned(),
        }
    }

    // StockInfo {
    //     name: "电投产融",
    //     inner_code: "000958_XSHE",
    //     outer_code: "000958.sz",
    //     market_id: 102,
    //     lot_size: 100,
    //     currency: "CNY",
    //     security_status: 0,
    //     stock_type: 4,
    //     plate_codes: [
    //         "GN0005.SH",
    //         "HY490300.SH",
    //     ],
    //     inner_stock_type: 0,
    // },
    #[allow(dead_code)]
    pub async fn hsfiuweb(&self, list: &Vec<StockInfo>, industry: &Vec<IndustryList>, tx_tick: Sender<YsHqInfo>) {
        let response = Client::default()
            .get(self.hs_url.clone())
            .upgrade() // <-- prepares the websocket upgrade.
            .send()
            .await;
        // .unwrap();
        let mut websocket = match response {
            Ok(up) => match up.into_websocket().await {
                Ok(websocket) => websocket,
                Err(err) => {
                    error!("into websocket err: {:?}", err);
                    log_error(&format!("hsfiuweb into websocket err: {:?}", err)).await;
                    return;
                }
            },
            Err(err) => {
                error!("upgrade websocket err: {:?}", err);
                log_error(&format!("hsfiuweb upgrade websocket err: {:?}", err)).await;
                return;
            }
        };

        // let mut websocket = response.into_websocket().await.unwrap();

        let stock_chunks: Vec<Vec<StockInfo>> = list.chunks(12).map(|x| x.to_vec()).collect();
        for v in stock_chunks {
            let (index, stock): (Vec<StockInfo>, Vec<StockInfo>) = v.into_iter().partition(|x| x.stock_type == 4 && x.outer_code != "000958.sz".to_string());
            if !index.is_empty() {
                let req = json!({
                    "symbols": index.iter().map(|x| x.outer_code.clone()).collect::<Vec<String>>(),
                    "version": self.version.to_owned(),
                    "command": Command::SubscribeIndexQuote as i32, //指数
                    "token": self.token.to_owned(),
                    "timeMode": 0
                });
                websocket.send(Message::Text(req.to_string())).await.unwrap();
            }

            if !stock.is_empty() {
                let req = json!({
                    "symbols": stock.iter().map(|x| x.outer_code.clone()).collect::<Vec<String>>(),
                    "version": self.version.to_owned(),
                    "command": Command::SubscribeExtendSnap as i32, //扩展行情
                    "token": self.token.to_owned(),
                    "timeMode": 0
                });
                websocket.send(Message::Text(req.to_string())).await.unwrap();
            }
        }

        let hs_industry = industry.iter().map(|x| x.symbol.clone()).collect::<Vec<String>>();
        {
            let mut hs_list = HS_INDUSTRY_LIST.write().await;
            hs_list.extend_from_slice(&hs_industry);
        }
        let req = json!({
            "symbols": hs_industry,
            "version": self.version.to_owned(),
            "command": Command::SubscribeIndexQuote as i32, //板块
            "token": self.token.to_owned(),
            "timeMode": 0
        });
        websocket.send(Message::Text(req.to_string())).await.unwrap();

        let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));
        // tokio::spawn(async move{
        loop {
            tokio::select! {
                task = websocket.try_next() => {
                    match task {
                        Ok(Some(message)) => {
                            match message {
                                Message::Text(text)=>{info!("{}",text);},
                                Message::Binary(buf)=>{
                                    // info!("{:?}",std::str::from_utf8(&buf));
                                    let protocol = std::str::from_utf8(&buf[2..6]).unwrap().parse::<i32>().unwrap();
                                    let hby = bytes::BytesMut::from_iter(&buf);
                                    FiuWeb::hs_analyze_msg(protocol, hby, tx_tick.clone()).await;
                                },
                                _=>{}
                                // Message::Ping(_) => todo!(),
                                // Message::Pong(_) => todo!(),
                                // Message::Close { code, reason } => todo!(),
                            }
                        },
                        Ok(None) => {
                            info!("None");
                            break;
                        },
                        Err(e) => {
                            error!("{:?}", e);
                            break;
                        }
                    }
                },
                _ = interval.tick() => {
                    let req = json!({
                        "command": Command::HeartBeat as i32,
                        "version": self.version.to_owned(),
                        "token": self.token.to_owned(),
                        "heartBeat": "heartBeat"
                    });
                    websocket.send(Message::Text(req.to_string())).await.unwrap();
                }
            }
        }
        // });
    }

    #[allow(dead_code)]
    pub async fn hkfiuweb(&self, list: &Vec<StockInfo>, industry: &Vec<IndustryList>, tx_tick: Sender<YsHqInfo>) {
        let response = Client::default()
            .get(self.hk_url.clone())
            .upgrade() // <-- prepares the websocket upgrade.
            .send()
            .await;
        // .unwrap();
        // let mut websocket = response.into_websocket().await.unwrap();
        let mut websocket = match response {
            Ok(up) => match up.into_websocket().await {
                Ok(websocket) => websocket,
                Err(err) => {
                    error!("hkfiuweb into websocket err: {:?}", err);
                    log_error(&format!("hkfiuweb into websocket err: {:?}", err)).await;
                    return;
                }
            },
            Err(err) => {
                error!("hkfiuweb upgrade websocket err: {:?}", err);
                log_error(&format!("hkfiuweb upgrade websocket err: {:?}", err)).await;
                return;
            }
        };

        let stock_chunks: Vec<Vec<StockInfo>> = list.chunks(12).map(|x| x.to_vec()).collect();
        for v in stock_chunks {
            let req = json!({
                "symbols": v.iter().map(|x| x.outer_code.clone()).collect::<Vec<String>>(),
                "version": self.version.to_owned(),
                "command": Command::SubscribeExtendSnap as i32, //扩展行情
                "token": self.token.to_owned(),
                "timeMode": 0
            });
            websocket.send(Message::Text(req.to_string())).await.unwrap();
        }

        let req = json!({
            "symbols": self.indexlist.to_owned(),
            "version": self.version.to_owned(),
            "command": Command::SubscribeIndexQuote as i32, //指数行情
            "token": self.token.to_owned(),
            "timeMode": 0
        });
        websocket.send(Message::Text(req.to_string())).await.unwrap();

        let hk_industry = industry.iter().map(|x| x.symbol.clone()).collect::<Vec<String>>();
        {
            let mut hk_list = HK_INDUSTRY_LIST.write().await;
            hk_list.extend_from_slice(&hk_industry);
        }
        let req = json!({
            "symbols": hk_industry,
            "version": self.version.to_owned(),
            "command": Command::SubscribeIndexQuote as i32, //板块
            "token": self.token.to_owned(),
            "timeMode": 0
        });
        websocket.send(Message::Text(req.to_string())).await.unwrap();

        let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));
        // tokio::spawn(async move{
        loop {
            tokio::select! {
                task = websocket.try_next() => {
                    match task {
                        Ok(Some(message)) => {
                            match message {
                                Message::Text(text)=>{info!("{}",text);},
                                Message::Binary(buf)=>{
                                    let protocol = std::str::from_utf8(&buf[2..6]).unwrap().parse::<i32>().unwrap();
                                    let hby = bytes::BytesMut::from_iter(&buf);
                                    FiuWeb::hk_analyze_msg(protocol, hby, tx_tick.clone()).await;
                                },
                                _=>{}
                                // Message::Ping(_) => todo!(),
                                // Message::Pong(_) => todo!(),
                                // Message::Close { code: code, reason: str } => todo!(),
                            }
                        },
                        Ok(None) => {
                            info!("None");
                            break;
                        },
                        Err(e) => {
                            error!("{:?}", e);
                            break;
                        }
                    }
                },
                _ = interval.tick() => {
                    let req = json!({
                        "command": Command::HeartBeat as i32,
                        "version": self.version.to_owned(),
                        "token": self.token.to_owned(),
                        "heartBeat": "heartBeat"
                    });
                    websocket.send(Message::Text(req.to_string())).await.unwrap();
                }
            }
        }
        // });
    }

    pub async fn hs_analyze_msg(protocol: i32, hby: BytesMut, tx_tick: Sender<YsHqInfo>) {
        match protocol {
            v if v == Protocol::Resp as i32 => {
                // info!("{:?}", RespMsg::decode(hby).unwrap_or_default());
            }
            v if v == Protocol::Connect as i32 => {
                info!("{:?}", Connect::decode(hby).unwrap_or_default());
            }
            v if v == Protocol::ExtendSnap as i32 => {
                // info!("{:?}", HsExtendSnap::decode(hby).unwrap_or_default());
                insert_hs_extend_snap(&HsExtendSnap::decode(hby).unwrap_or_default()).await;
                // insert_hs_extend(&HsExtendSnap::decode(hby).unwrap_or_default()).await;
            }
            v if v == Protocol::IndexQuote as i32 => {
                // info!("{:?}", HsIndexQuote::decode(hby).unwrap_or_default());
                let tick = convert_hs_index_quote_to_tick(&HsIndexQuote::decode(hby).unwrap_or_default()).await;
                let _ = tx_tick.send(tick);
            }
            v if v == Protocol::Order as i32 => {
                //暂未订阅
                // info!("{:?}", HsOrder::decode(hby).unwrap_or_default());
                insert_hs_order(&HsOrder::decode(hby).unwrap_or_default()).await;
            }
            v if v == Protocol::Snapshot as i32 => {
                //暂未订阅
                // info!("{:?}", HsSnapshot::decode(hby).unwrap_or_default());
                convert_hs_snapshot_to_tick(&HsSnapshot::decode(hby).unwrap_or_default()).await;
            }
            _ => {}
        }
    }

    pub async fn hk_analyze_msg(protocol: i32, hby: BytesMut, tx_tick: Sender<YsHqInfo>) {
        match protocol {
            v if v == Protocol::Resp as i32 => {
                // info!("{:?}", RespMsg::decode(hby).unwrap_or_default());
            }
            v if v == Protocol::Connect as i32 => {
                info!("{:?}", Connect::decode(hby).unwrap_or_default());
            }
            v if v == Protocol::ExtendSnap as i32 => {
                // info!("{:?}", HkExtendSnap::decode(hby).unwrap_or_default());
                insert_hk_extend_snap(&HkExtendSnap::decode(hby).unwrap_or_default()).await;
                // insert_hk_extend(&HkExtendSnap::decode(hby).unwrap_or_default()).await;
            }
            v if v == Protocol::IndexQuote as i32 => {
                // info!("{:?}", HkIndexQuote::decode(hby).unwrap_or_default());
                let tick = convert_hk_index_quote_to_tick(&HkIndexQuote::decode(hby).unwrap_or_default()).await;
                // info!("{:?}", tick);
                let _ = tx_tick.send(tick);
            }
            v if v == Protocol::Order as i32 => {
                //暂未订阅
                // info!("{:?}", HkOrder::decode(hby).unwrap_or_default());
                insert_hk_order(&HkOrder::decode(hby).unwrap_or_default()).await;
            }
            v if v == Protocol::Snapshot as i32 => {
                //暂未订阅
                // info!("{:?}", HkSnapshot::decode(hby).unwrap_or_default());
                convert_hk_snapshot_to_tick(&HkSnapshot::decode(hby).unwrap_or_default()).await;
            }
            _ => {}
        }
    }
}

pub async fn insert_hs_order(order: &HsOrder) {
    let mut hs_order = HS_ORDER.write().await;
    hs_order.insert(order.symbol.to_owned(), order.clone());
}

pub async fn insert_hk_order(order: &HkOrder) {
    let mut hk_order = HK_ORDER.write().await;
    hk_order.insert(order.symbol.to_owned(), order.to_owned());
}

#[allow(dead_code)]
pub async fn insert_hs_extend(snap: &HsExtendSnap) {
    let mut hs = HS_EXTEND_SNAP.write().await;
    hs.insert(snap.symbol.to_owned(), snap.clone());
}

#[allow(dead_code)]
pub async fn insert_hk_extend(snap: &HkExtendSnap) {
    let mut hk = HK_EXTEND_SNAP.write().await;
    hk.insert(snap.symbol.to_owned(), snap.clone());
}

//暂未使用
async fn convert_hs_snapshot_to_tick(snapshot: &HsSnapshot) -> Option<YsHqInfo> {
    let mut tick = YsHqInfo::default();

    let hs_order = HS_ORDER.read().await;
    let order = match hs_order.get(&snapshot.symbol) {
        Some(v) => v.to_owned(),
        None => HsOrder::default(),
    };
    drop(hs_order);

    let extend = HS_EXTEND_SNAP.read().await;
    let extend_snap = match extend.get(&snapshot.symbol) {
        Some(v) => v.to_owned(),
        None => return None,
    };
    drop(extend);

    let mut market = Market::SH as i64;
    if snapshot.symbol.contains(".sz") {
        market = Market::SZ as i64;
    }
    let symbol = snapshot.symbol.split(".").collect::<Vec<&str>>()[0]; //600000
    tick.exchange_id = get_exchange_id(market);
    tick.contract_no1 = get_inner_code(symbol.to_owned(), market);
    tick.tapidtstamp = snapshot.time.to_owned();
    tick.q_pre_closing_price = snapshot.pre_close.to_owned();
    tick.q_opening_price = snapshot.open;
    tick.q_high_price = snapshot.high;
    tick.q_low_price = snapshot.low;
    tick.q_closing_price = snapshot.close;
    tick.q_last_price = snapshot.last;

    tick.q_total_qty = snapshot.volume;
    tick.q_total_turnover = snapshot.amount;
    tick.q_change_value = snapshot.change;
    tick.q_change_rate = snapshot.change_rate;
    tick.q_amplitude = snapshot.amplitude;
    tick.q_average_price = snapshot.avg_price;

    //扩展
    tick.q_total_shares = extend_snap.total_shares;
    tick.q_market_value = extend_snap.total_market_value;
    tick.q_circulation_amount = extend_snap.circulation_shares;
    tick.q_pe_rate = extend_snap.per_ttm;
    tick.q_dyn_pb_rate = extend_snap.pbr;
    tick.q_turnover_ratio = extend_snap.turnover_rate;
    tick.q_entrust_rate = extend_snap.bid_ask_rate;
    tick.q_vol_ratio = extend_snap.volume_rate;
    tick.q_limit_down_price = extend_snap.limit_down;
    tick.q_limit_up_price = extend_snap.limit_up;
    tick.currency_no = extend_snap.trading_currency.to_owned();

    for v in order.bid_list.iter() {
        tick.q_bid_price.push(v.price);
        tick.q_bid_qty.push(v.volume);
    }
    for v in order.ask_list.iter() {
        tick.q_ask_price.push(v.price);
        tick.q_ask_qty.push(v.volume);
    }

    let mut pre = HS_TICK.write().await;
    let pre_tick = match pre.get(&tick.contract_no1) {
        Some(v) => v.to_owned(),
        None => YsHqInfo::default(),
    };
    if pre_tick.q_total_qty != 0 && tick.q_total_qty > pre_tick.q_total_qty {
        tick.q_last_qty = (tick.q_total_qty - pre_tick.q_total_qty).abs() as f64;
    }
    if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
        tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
    }
    pre.insert(tick.contract_no1.to_owned(), tick.clone());

    // info!("{:?}", tick);
    Some(tick)
}

//暂未使用
async fn convert_hk_snapshot_to_tick(snapshot: &HkSnapshot) -> Option<YsHqInfo> {
    let mut tick = YsHqInfo::default();

    let hk_order = HK_ORDER.read().await;
    let order = match hk_order.get(&snapshot.symbol) {
        Some(v) => v.to_owned(),
        None => HkOrder::default(),
    };
    drop(hk_order);

    let extend = HS_EXTEND_SNAP.read().await;
    let extend_snap = match extend.get(&snapshot.symbol) {
        Some(v) => v.to_owned(),
        None => return None,
    };
    drop(extend);

    let symbol = snapshot.symbol.split(".").collect::<Vec<&str>>()[0];
    tick.exchange_id = get_exchange_id(Market::HK as i64);
    tick.contract_no1 = get_inner_code(symbol.to_owned(), Market::HK as i64);
    tick.tapidtstamp = snapshot.time.to_owned();
    tick.q_pre_closing_price = snapshot.pre_close.to_owned();
    tick.q_opening_price = snapshot.open;
    tick.q_high_price = snapshot.high;
    tick.q_low_price = snapshot.low;
    tick.q_closing_price = snapshot.close;
    tick.q_last_price = snapshot.last;

    tick.q_total_qty = snapshot.volume;
    tick.q_total_turnover = snapshot.amount;
    tick.q_change_value = snapshot.change;
    tick.q_change_rate = snapshot.change_rate;
    tick.q_amplitude = snapshot.amplitude;
    tick.q_average_price = snapshot.avg_price;

    //扩展
    tick.q_total_shares = extend_snap.total_shares;
    tick.q_market_value = extend_snap.total_market_value;
    tick.q_circulation_amount = extend_snap.circulation_shares;
    tick.q_pe_rate = extend_snap.per_ttm;
    tick.q_dyn_pb_rate = extend_snap.pbr;
    tick.q_turnover_ratio = extend_snap.turnover_rate;
    tick.q_entrust_rate = extend_snap.bid_ask_rate;
    tick.q_vol_ratio = extend_snap.volume_rate;
    tick.q_limit_down_price = extend_snap.limit_down;
    tick.q_limit_up_price = extend_snap.limit_up;
    tick.currency_no = extend_snap.trading_currency.to_owned();

    for v in order.bid_list.iter() {
        tick.q_bid_price.push(v.price);
        tick.q_bid_qty.push(v.volume);
    }
    for v in order.ask_list.iter() {
        tick.q_ask_price.push(v.price);
        tick.q_ask_qty.push(v.volume);
    }

    let mut pre = HK_TICK.write().await;
    let pre_tick = match pre.get(&tick.contract_no1) {
        Some(v) => v.to_owned(),
        None => YsHqInfo::default(),
    };
    if pre_tick.q_total_qty != 0 && tick.q_total_qty > pre_tick.q_total_qty {
        tick.q_last_qty = (tick.q_total_qty - pre_tick.q_total_qty).abs() as f64;
    }
    if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
        tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
    }
    pre.insert(tick.contract_no1.to_owned(), tick.clone());

    // info!("{:?}", tick);
    Some(tick)
}

/// A股指数板块
async fn convert_hs_index_quote_to_tick(quote: &HsIndexQuote) -> YsHqInfo {
    let mut tick = YsHqInfo::default();

    // 判断市场
    let market = if quote.symbol.contains(".sz") { Market::SZ as i64 } else { Market::SH as i64 };
    let symbol = quote.symbol.split('.').next().unwrap_or(""); // 600000

    // 判断是否板块
    {
        let index = HS_INDUSTRY_LIST.read().await;
        if index.iter().any(|x| x == &quote.symbol) {
            tick.exchange_id = "HS".to_string();
            tick.contract_no1 = format!("{}_HS", symbol);
        } else {
            tick.exchange_id = get_exchange_id(market);
            tick.contract_no1 = get_inner_code(symbol.to_string(), market);
        }
    }

    tick.tapidtstamp = quote.time.clone();
    tick.q_opening_price = quote.open;
    tick.q_high_price = quote.high;
    tick.q_low_price = quote.low;
    tick.q_closing_price = quote.close;
    tick.q_pre_closing_price = quote.pre_close;
    tick.q_last_price = quote.last;
    tick.q_total_qty = quote.volume;
    tick.q_total_turnover = quote.amount;
    tick.q_change_value = quote.change;
    tick.q_change_rate = quote.change_rate;
    tick.q_amplitude = quote.amplitude;
    tick.q_market_value = quote.total_market_value;
    tick.q_pe_rate = quote.per_ttm;
    tick.q_average_price = quote.avg_price;
    tick.q_turnover_ratio = quote.turnover_rate;

    // 取上一笔
    let mut pre = HK_TICK.write().await;
    let pre_tick = pre.get(&tick.contract_no1).cloned().unwrap_or_default();
    if pre_tick.q_total_qty != 0 && tick.q_total_qty > pre_tick.q_total_qty {
        tick.q_last_qty = (tick.q_total_qty - pre_tick.q_total_qty) as f64;
    }
    if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
        tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
    }
    pre.insert(tick.contract_no1.clone(), tick.clone());

    tick
}

/// 港股指数板块
async fn convert_hk_index_quote_to_tick(quote: &HkIndexQuote) -> YsHqInfo {
    let mut tick = YsHqInfo::default();

    // 特殊指数优先判断
    match quote.symbol.as_str() {
        "0000100" => {
            //恒生指数
            tick.exchange_id = get_exchange_id(Market::HK as i64);
            tick.contract_no1 = "HSI_HK".to_string();
        }
        "HSCEI" => {
            //国企指数
            tick.exchange_id = get_exchange_id(Market::HK as i64);
            tick.contract_no1 = "HZ5014_HK".to_string();
        }
        "HSCCI" => {
            //红筹指数
            tick.exchange_id = get_exchange_id(Market::HK as i64);
            tick.contract_no1 = "HZ5015_HK".to_string();
        }
        _ => {
            if quote.symbol.contains('.') {
                let symbol = quote.symbol.split('.').next().unwrap_or("");
                let index = HK_INDUSTRY_LIST.read().await;
                if index.iter().any(|x| x == &quote.symbol) {
                    tick.exchange_id = "HK".to_string();
                    tick.contract_no1 = format!("{}_HK", symbol);
                } else {
                    tick.exchange_id = get_exchange_id(Market::HK as i64);
                    tick.contract_no1 = get_inner_code(symbol.to_string(), Market::HK as i64);
                }
            }
        }
    }

    tick.tapidtstamp = quote.time.clone();
    tick.q_opening_price = quote.open;
    tick.q_high_price = quote.high;
    tick.q_low_price = quote.low;
    tick.q_closing_price = quote.close;
    tick.q_pre_closing_price = quote.pre_close;
    tick.q_last_price = quote.last;
    tick.q_total_qty = quote.volume;
    tick.q_total_turnover = quote.amount;
    tick.q_change_value = quote.change;
    tick.q_change_rate = quote.change_rate;
    tick.q_amplitude = quote.amplitude;
    tick.q_market_value = quote.total_market_value;
    tick.q_pe_rate = quote.per_ttm;
    tick.q_average_price = quote.avg_price;
    tick.q_turnover_ratio = quote.turnover_rate;

    // 取上一笔
    let mut pre = HK_TICK.write().await;
    let pre_tick = pre.get(&tick.contract_no1).cloned().unwrap_or_default();
    if pre_tick.q_total_qty != 0 && tick.q_total_qty > pre_tick.q_total_qty {
        tick.q_last_qty = (tick.q_total_qty - pre_tick.q_total_qty) as f64;
    }
    if pre_tick.q_total_turnover != 0.0 && tick.q_total_turnover > pre_tick.q_total_turnover {
        tick.q_last_turnover = tick.q_total_turnover - pre_tick.q_total_turnover;
    }
    pre.insert(tick.contract_no1.clone(), tick.clone());

    tick
}
