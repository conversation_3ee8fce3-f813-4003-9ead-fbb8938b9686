use serde::{Deserialize, Serialize};
// use serde_json::json;
// use serde_json::{self};

// 请求协议

///心跳包
#[derive(Debug, <PERSON>lone, Default, Deserialize, Serialize)]
pub struct HeartBeat {
    pub command: i32,       // 协议类型 0
    pub version: String,    // 协议版本号 1.0.0
    pub token: String,      // 权限控制 szfiu
    pub heart_beat: String, //参数字段，固定值：heartBeat
}

///订阅快照
#[derive(Debug, <PERSON>lone, Default, Deserialize, Serialize)]
pub struct SubscribeSnapshot {
    pub command: i32,         // 协议类型 1
    pub version: String,      // 协议版本号 1.0.0
    pub token: String,        // 权限控制 szfiu
    pub symbols: Vec<String>, // 证券代码 [“600519.sh”]
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///取消订阅快照
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default, Deserialize, Serialize)]
pub struct UnsubscribeSnapshot {
    pub command: i32,         // 协议类型 2
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///订阅trade
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct SubscribeTrade {
    pub command: i32,         // 协议类型 3
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///取消订阅trade
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct UnsubscribeTrade {
    pub command: i32,         // 协议类型 4
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///订阅order
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct SubscribeOrder {
    pub command: i32,         // 协议类型 5
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///取消订阅order
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct UnsubscribeOrder {
    pub command: i32,         // 协议类型 6
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///订阅分时
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct SubscribeMinute {
    pub command: i32,         // 协议类型 7
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub trend_type: i32,      // 分时类型：1：当天分时，5：五日分时
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///取消订阅分时
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct UnsubscribeMinute {
    pub command: i32,         // 协议类型 8
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///订阅k线
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct SubscribeKline {
    pub command: i32,         // 协议类型 9
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub candle_mode: i32,     // 复权类型 0：不复权，1：前复权，2：后复权
    pub r#type: i32,          // K线类型：0,1,2,3,4,5,6,7,8,9,10,11,12分别代表日，周，月，季，年，1分，5分，15分，30分，60分，120分，3分，240分k
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///取消订阅k线
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct UnsubscribeKline {
    pub command: i32,         // 协议类型 10
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub symbols: Vec<String>, // 证券代码
    pub candle_mode: i32,     // 复权类型 0：不复权，1：前复权，2：后复权
    pub r#type: i32,          // K线类型：0,1,2,3,4,5,6,7,8,9,10,11,12分别代表日，周，月，季，年，1分，5分，15分，30分，60分，120分，3分，240分k
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///订阅扩展行情
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct SubscribeExtend {
    pub command: i32,         // 协议类型 18
    pub symbols: Vec<String>, // 证券代码
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
}

///取消订阅扩展行情
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct UnsubscribeExtend {
    pub command: i32,         // 协议类型 19
    pub symbols: Vec<String>, // 证券代码
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
}

//订阅指数行情
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct SubscribeIndex {
    pub command: i32,         // 协议类型 20
    pub symbols: Vec<String>, // 证券代码
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

///取消订阅指数行情
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct UnsubscribeIndex {
    pub command: i32,         // 协议类型 21
    pub symbols: Vec<String>, // 证券代码
    pub version: String,      // 协议版本号
    pub token: String,        // 权限控制
    pub time_mode: i32,       // 快照时间模式：0：实时，1：延时
}

// 响应协议
#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct HsStock {
    pub protocol: String,   //协议类型
    pub symbol: String,     //证券代码
    pub time: String,       //行情时间
    pub open_price: f64,    //开盘价
    pub high_price: f64,    //最高价
    pub low_price: f64,     //最低价
    pub close_price: f64,   //收盘价
    pub last_price: f64,    //最新价
    pub volume: i64,        //成交量
    pub amount: f64,        //成交额
    pub change: f64,        //涨跌幅
    pub change_rate: f64,   //涨跌幅(%)
    pub amplitude: f64,     //振幅
    pub avg_price: f64,     //平均价
    pub turnover_rate: f64, //换手率(%)
}

#[derive(Debug, Clone, Default, Deserialize, Serialize)]

pub struct HsStockShot {
    pub protocol: String,     //协议类型
    pub symbol: String,       //证券代码
    pub time: String,         //行情时间
    pub pre_close_price: f64, //昨收价
    pub open_price: f64,      //开盘价
    pub high_price: f64,      //最高价
    pub low_price: f64,       //最低价
    pub close_price: f64,     //收盘价
    pub last_price: f64,      //最新价
    pub volume: i64,          //成交量
    pub amount: f64,          //成交额
    pub change: f64,          //涨跌幅
    pub change_rate: f64,     //涨跌幅(%)
    pub amplitude: f64,       //振幅
    pub avg_price: f64,       //平均价
                              // pub turnover_rate: f64, //换手率(%)
}

//快照响应
