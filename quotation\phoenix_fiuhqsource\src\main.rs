// #![allow(dead_code, unused_imports)] //功能:没有使用的代码或模块不警告

extern crate anyhow;
extern crate lazy_static;

mod client;
mod codec;
mod common;
mod config;
mod dataservice;
mod fiuhq;
mod protofiles;
mod server;

use anyhow::Result;
use std::time::Duration;
use tracing::info;

use ::common::init_tracing;
use ::common::logclient::*;
use protoes::marketdata::market_data_servers_server::MarketDataServersServer;

use crate::config::settings::Settings;
use crate::server::server::FiuHqServerHandler;

#[tokio::main]
async fn main() -> Result<()> {
    let prefix = "phoenix_fiuhqsource";
    let dir = "./log";

    let settings = Settings::new().expect("read config error");
    let level = "INFO";
    let _guard = init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:#?}", &settings);

    init_logclient(&settings.system.logcenterserver, &format!("{}_{prefix}", "quotation")).await;
    let server = FiuHqServerHandler::new(&settings).await;

    let _ = run_server(server).await.unwrap();
    Ok(())
}

async fn run_server(mut server: FiuHqServerHandler) -> Result<(), Box<dyn std::error::Error>> {
    let app_url = format!("{}:{}", server.settings.application.apphost, server.settings.application.appport);
    let addr = app_url.as_str().parse().unwrap();

    info!("Starting FiuHqSource service on: {}", addr);
    info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    // log_debug(&format!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"))).await;

    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = server.on_leave();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder()
        .timeout(Duration::from_millis(5000)) //设置全部请求超时时间,超过时间直接返回
        .add_service(MarketDataServersServer::new(server))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
