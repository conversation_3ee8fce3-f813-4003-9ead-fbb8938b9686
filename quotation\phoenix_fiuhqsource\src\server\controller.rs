use tokio::sync::broadcast::Sender;
use tracing::*;

use protoes::hqmsg::YsHqInfo;

use crate::client::DbClient;
use crate::common::{convert_local_stock_info, StockInfo};
use crate::config::settings::Settings;
use crate::dataservice::entities::prelude::{SysCommodity, SysCommodityExt, SysCommodityGroup, SysCommodityGroupList, SysCommodityTmp};
use crate::fiuhq::{self, FiuApi, IndustryList};

#[allow(dead_code)]
#[derive(Clone)]
pub struct FiuHqController {
    setting: Settings,
    pub fiu_api: FiuApi,
    // db: Option<DbClient>,
    pub pull_ex: i32,
}

impl FiuHqController {
    #[allow(dead_code)]
    pub async fn new(setting: &Settings) -> Self {
        let fiu_api = FiuApi::new(&setting.system.hsfiuapi, &setting.system.hkfiuapi.to_owned()).await;
        // let mut db = None;
        // if !setting.mysql.url.is_empty() {
        //     let dbcli = DbClient::new(&setting.mysql.url).await;
        //     db = Some(dbcli)
        // }

        Self {
            setting: setting.clone(),
            fiu_api,
            // db,
            pull_ex: 0,
        }
    }

    #[allow(dead_code)]
    pub async fn pull_code(&mut self, tx_tick: Sender<YsHqInfo>) {
        // 股票 ETF 指数 基金
        // let hs_stock = self.fiu_api.hs_post_stock_basic_info().await;
        // let hk_stock = self.fiu_api.hk_post_stock_basic_info().await;
        // 并发获取股票和行业数据
        let (hs_stock, hk_stock, mut industry, hk_industry) = tokio::join!(
            self.fiu_api.hs_post_stock_basic_info(),
            self.fiu_api.hk_post_stock_basic_info(),
            self.fiu_api.hs_post_industry_list(),
            self.fiu_api.hk_post_industry_list()
        );
        let stock_info = convert_local_stock_info(&hs_stock, &hk_stock).await;
        info!("{:#?}", stock_info.len());

        // let mut industry = self.fiu_api.hs_post_industry_list().await;
        // let hk_industry = self.fiu_api.hk_post_industry_list().await;
        industry.extend(hk_industry);
        // info!("{:#?}", industry);

        self.ws_subscribe(tx_tick.clone(), &stock_info, &industry).await;

        // 用并发处理每个数据库
        // 检查当前时间是否为配置的执行时间
        let pull_time = format!("{}", utility::timeutil::build_naive_time(&self.setting.system.pullcodetime));
        let current_time = format!("{}", utility::timeutil::current_naive_time().time().format("%H:%M:%S"));
        info!("配置的执行时间: {}, 当前时间: {}", pull_time, current_time);

        // 只在配置的执行时间的分钟范围内执行，其他时间直接返回
        if pull_time[0..5] != current_time[0..5] {
            info!("不在指定执行时间，跳过执行");
            self.pull_ex = 1;
            return;
        }

        info!("当前时间匹配配置的执行时间，开始执行同步操作");
        for (key, value) in self.setting.mysql.iter() {
            if value.is_empty() {
                continue;
            }
            // 克隆必要数据，避免并发冲突
            let key = key.clone();
            let value = value.clone();
            let stock_info = stock_info.clone();
            let mut industry = industry.clone();
            tokio::spawn(async move {
                info!("pull code start {}：{}", key, value);
                let db = DbClient::new(&value).await;

                let _ = SysCommodityTmp::delete_from_exchange(&db).await;
                let sys_commodity_tmp = SysCommodityTmp::convert_stock_basic_to_model(&stock_info).await;
                let _ = SysCommodityTmp::insert_many(&db, &sys_commodity_tmp).await;
                info!("Insert SysCommodityTmp finished");

                let sys_commodity = SysCommodity::select_all_model(&db).await;
                info!("{:#?}", sys_commodity.len());
                let new_stock_info: Vec<_> = stock_info.iter().filter(|v| !sys_commodity.iter().any(|x| x.inner_code == v.inner_code)).cloned().collect();

                let model = SysCommodity::convert_stock_basic_to_model(&new_stock_info).await;
                info!("新增: {:#?}", model.len());
                if !model.is_empty() {
                    let _ = SysCommodity::insert_many(&db, &model).await;
                    info!("Insert SysCommodity finished");
                }

                let mut sys_commodity = SysCommodity::select_all_model(&db).await;
                let ext = SysCommodityExt::to_model(&new_stock_info, &sys_commodity).await;
                info!("新增ext: {:#?}", ext.len());
                if !model.is_empty() {
                    let _ = SysCommodityExt::insert_many(&db, &ext).await;
                    info!("Insert SysCommodityExt finished");
                }

                sys_commodity.retain(|x| matches!(x.market_id, 101 | 102 | 103));
                update(&stock_info, &mut sys_commodity);
                info!("更新: {:#?}", sys_commodity.len());
                let _ = SysCommodity::update_many(&db, &sys_commodity).await;

                let sys_commodity_group = SysCommodityGroup::select_all_model(&db).await;
                industry.retain(|list| !sys_commodity_group.iter().any(|group| group.code == list.symbol));
                if !industry.is_empty() {
                    let models = SysCommodityGroup::convert_industry_to_model(&industry).await;
                    let _ = SysCommodityGroup::insert_many(&db, &models).await;
                }

                let sys_commodity_group = SysCommodityGroup::select_all_model(&db).await;
                let sys_commodity = SysCommodity::select_all_model(&db).await;
                let mut models = Vec::new();
                for stock in stock_info.iter() {
                    if let Some(commodity) = sys_commodity.iter().find(|x| x.inner_code == stock.inner_code) {
                        for x in stock.plate_codes.iter() {
                            if let Some(group) = sys_commodity_group.iter().find(|y| x.eq_ignore_ascii_case(&y.code)) {
                                let mut group_list = SysCommodityGroupList::default();
                                group_list.commodity_id = commodity.id;
                                group_list.group_id = group.id;
                                models.push(group_list);
                            }
                        }
                    }
                }

                info!("{}", models.len());
                let old = SysCommodityGroupList::select_all_model(&db).await;
                models.retain(|x| !old.iter().any(|y| x.group_id == y.group_id && x.commodity_id == y.commodity_id));
                info!("{:?}", models.len());
                if !models.is_empty() {
                    let _ = SysCommodityGroupList::insert_many(&db, &models).await;
                }

                info!("pull code finished {}：{}", key, value);
            });
        }

        self.pull_ex = 1;
    }

    // pub async fn pull_code(&mut self, tx_tick: Sender<YsHqInfo>) {
    //     // 股票 ETF 指数 基金
    //     // let hs_stock = self.fiu_api.hs_post_stock_basic_info().await;
    //     // let hk_stock = self.fiu_api.hk_post_stock_basic_info().await;
    //     // 并发获取股票和行业数据
    //     let (hs_stock, hk_stock) = tokio::join!(self.fiu_api.hs_post_stock_basic_info(), self.fiu_api.hk_post_stock_basic_info());
    //     let stock_info = convert_local_stock_info(&hs_stock, &hk_stock).await;
    //     info!("{:#?}", stock_info.len());

    //     // let mut industry = self.fiu_api.hs_post_industry_list().await;
    //     // let hk_industry = self.fiu_api.hk_post_industry_list().await;
    //     let (mut industry, hk_industry) = tokio::join!(self.fiu_api.hs_post_industry_list(), self.fiu_api.hk_post_industry_list());
    //     industry.extend(hk_industry);
    //     // info!("{:#?}", industry);

    //     self.ws_subscribe(tx_tick.clone(), &stock_info, &industry).await;

    //     for (key, value) in self.setting.mysql.iter() {
    //         if value.is_empty() {
    //             continue;
    //         }
    //         info!("pull code start {}：{}", key, value);
    //         let db = DbClient::new(&value).await;

    //         let _ = SysCommodityTmp::delete_from_exchange(&db /*, 1*/).await;
    //         // let _ = SysCommodityTmp::delete_from_exchange(&db, 2).await;
    //         let sys_commodity_tmp = SysCommodityTmp::convert_stock_basic_to_model(&stock_info).await;
    //         let _ = SysCommodityTmp::insert_many(&db, &sys_commodity_tmp).await;
    //         info!("Insert SysCommodityTmp finished");

    //         // 主表处理
    //         let sys_commodity = SysCommodity::select_all_model(&db).await;
    //         info!("{:#?}", sys_commodity.len());

    //         // let mut new_stock_info = Vec::new();
    //         // for v in stock_info.iter() {
    //         //     if sys_commodity.iter().find(|x| x.inner_code == v.inner_code).is_none() {
    //         //         new_stock_info.push(v.to_owned())
    //         //     }
    //         // }
    //         let new_stock_info: Vec<_> = stock_info.iter().filter(|v| !sys_commodity.iter().any(|x| x.inner_code == v.inner_code)).cloned().collect();

    //         //未导入的股票
    //         let model = SysCommodity::convert_stock_basic_to_model(&new_stock_info).await;
    //         info!("新增: {:#?}", model.len());
    //         if !model.is_empty() {
    //             let _ = SysCommodity::insert_many(&db, &model).await;
    //             info!("Insert SysCommodity finished");
    //         }

    //         // 扩展表处理
    //         let mut sys_commodity = SysCommodity::select_all_model(&db).await;
    //         let ext = SysCommodityExt::to_model(&new_stock_info, &sys_commodity).await;
    //         info!("新增ext: {:#?}", ext.len());
    //         if !model.is_empty() {
    //             let _ = SysCommodityExt::insert_many(&db, &ext).await;
    //             info!("Insert SysCommodityExt finished");
    //         }

    //         // 只保留A/H/K市场
    //         sys_commodity.retain(|x| matches!(x.market_id, 101 | 102 | 103));
    //         update(&stock_info, &mut sys_commodity);
    //         info!("更新: {:#?}", sys_commodity.len());
    //         let _ = SysCommodity::update_many(&db, &sys_commodity).await;
    //         // let _ = SysCommodity::update(&db, &stock_info).await;

    //         // 行业板块处理
    //         let sys_commodity_group = SysCommodityGroup::select_all_model(&db).await;
    //         // industry.retain(|list| sys_commodity_group.iter().find(|group| group.code == list.symbol).is_none());
    //         industry.retain(|list| !sys_commodity_group.iter().any(|group| group.code == list.symbol));
    //         if !industry.is_empty() {
    //             let models = SysCommodityGroup::convert_industry_to_model(&industry).await;
    //             let _ = SysCommodityGroup::insert_many(&db, &models).await;
    //         }

    //         // 股票-板块关系
    //         let sys_commodity_group = SysCommodityGroup::select_all_model(&db).await;
    //         let sys_commodity = SysCommodity::select_all_model(&db).await;
    //         let mut models = Vec::new();
    //         for stock in stock_info.iter() {
    //             if let Some(commodity) = sys_commodity.iter().find(|x| x.inner_code == stock.inner_code) {
    //                 for x in stock.plate_codes.iter() {
    //                     if let Some(group) = sys_commodity_group.iter().find(|y| x.eq_ignore_ascii_case(&y.code)) {
    //                         let mut group_list = SysCommodityGroupList::default();
    //                         group_list.commodity_id = commodity.id;
    //                         group_list.group_id = group.id;
    //                         models.push(group_list);
    //                     }
    //                 }
    //             }
    //         }

    //         info!("{}", models.len());
    //         let old = SysCommodityGroupList::select_all_model(&db).await;
    //         models.retain(|x| !old.iter().any(|y| x.group_id == y.group_id && x.commodity_id == y.commodity_id));
    //         info!("{:?}", models.len());
    //         if !models.is_empty() {
    //             let _ = SysCommodityGroupList::insert_many(&db, &models).await;
    //         }

    //         info!("pull code finished {}：{}", key, value);
    //     }

    //     self.pull_ex = 1;
    // }

    pub async fn ws_subscribe(&self, tx_tick: Sender<YsHqInfo>, stock_info: &Vec<StockInfo>, industry: &Vec<IndustryList>) {
        let web = fiuhq::FiuWeb::new(&self.setting);
        let hk_web = web.clone();
        // let hs_code = stock_info
        //     .iter()
        //     .filter(|x| x.outer_code.contains(".sh") || x.outer_code.contains(".sz"))
        //     .map(|x| x.clone())
        //     .collect::<Vec<StockInfo>>();
        // let hk_code = stock_info.iter().filter(|x| x.outer_code.contains(".hk")).map(|x| x.clone()).collect::<Vec<StockInfo>>();

        // let hs_industry = industry.iter().filter(|x| x.symbol.contains(".sh") || x.symbol.contains(".sz")).map(|x| x.clone()).collect::<Vec<IndustryList>>();
        // let hk_industry = industry.iter().filter(|x| x.symbol.contains(".hk")).map(|x| x.clone()).collect::<Vec<IndustryList>>();

        // 拆分A股和港股代码、行业
        let (hs_code, hk_code): (Vec<_>, Vec<_>) = stock_info.iter().cloned().partition(|x| x.outer_code.contains(".sh") || x.outer_code.contains(".sz"));
        let (hs_industry, hk_industry): (Vec<_>, Vec<_>) = industry.iter().cloned().partition(|x| x.symbol.contains(".sh") || x.symbol.contains(".sz"));
        info!("====={}", hs_industry.len());
        info!("======{}", hk_industry.len());

        let hs_tick = tx_tick.clone();
        let hk_tick = tx_tick.clone();
        tokio::spawn(async move {
            web.hsfiuweb(&hs_code, &hs_industry, hs_tick).await;
        });
        tokio::spawn(async move {
            hk_web.hkfiuweb(&hk_code, &hk_industry, hk_tick).await;
        });
    }
}

fn update(stockinfo: &Vec<StockInfo>, models: &mut Vec<SysCommodity>) {
    for v in stockinfo.iter() {
        let ret = models.iter_mut().find(|x| x.inner_code == v.inner_code);
        if ret.is_none() {
            continue;
        }
        let model = ret.unwrap();

        // 0正常  1未上市  2熔断  3停牌  4退市
        if v.security_status == 1 || v.security_status == 2 || v.security_status == 3 || v.security_status == 4 {
            model.show_state = 2;
            model.trade_state = 3;
            model.susp_state = Some(1);
        } else {
            model.show_state = 1;
            model.trade_state = 1;
            model.susp_state = Some(0);
        }
        model.modify_date = utility::timeutil::current_timestamp();

        if model.name != v.name {
            model.name = v.name.to_owned();
        }
        if model.hands_num != v.lot_size {
            model.hands_num = v.lot_size;
        }
        if model.currency != v.currency {
            model.currency = v.currency.to_owned();
        }
    }
}
