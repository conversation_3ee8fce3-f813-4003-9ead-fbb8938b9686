// #![allow(dead_code, unused_imports)] //功能:没有使用的代码或模块不警告

// extern crate anyhow;
// extern crate lazy_static;

mod client;
mod codec;
mod common;
mod config;
mod dataservice;
mod fiuhq;
mod protofiles;
mod server;

use anyhow::Result;
use tracing::*;
// use chrono::NaiveDateTime;
// use codec::{BtType, FiuHeader, FiuType, MsgHeader};
// use common::{get_exchange_id, SecurityType};
// use protofiles::{CnStock, PbSnapshot};
// use std::time::Duration;
use utility::loggings;

use crate::config::settings::Settings;

// use crate::protofiles::market_data_servers_server::MarketDataServersServer;
// use crate::server::server::FiuHqServerHandler;
// use prost::bytes::BytesMut;
// use prost::{bytes, Message};
// use tokio::sync::broadcast::Sender;
// use tokio::{io::AsyncReadExt, net::TcpStream};

#[tokio::main]
async fn main() -> Result<()> {
    let cfg = "config/fiuhqsource.yaml";
    loggings::log_init(cfg);
    let settings = Settings::new().expect("read config error");
    info!("初始化配置信息:{:#?}", &settings);
    // tokio::spawn(async move {
    //     let _ = hs_hq_process(settings.system.hsfiusocket).await;
    // });
    // hk_hq_process(settings.system.hkfiusocket).await?;
    Ok(())
}

// pub async fn hs_hq_process(uri: String) -> Result<()> {
//     let mut stream = match TcpStream::connect(uri.to_owned()).await {
//         Ok(tcp_stream) => {
//             info!("连接成功: {}", uri);
//             tcp_stream
//         }
//         Err(err) => {
//             error!("{:?}", err);
//             return Err(anyhow!("{:?}", err));
//         }
//     };

//     let mut buf = [0u8; 3];

//     // tokio::spawn(async move {
//     loop {
//         tokio::select! {
//             result = stream.read_exact(&mut buf) => match result {
//                 Ok(size) => {
//                     if size != 3 {
//                         info!("size: {}", size);
//                         continue
//                     }
//                     let msg_head = MsgHeader::analysis_head(&buf);
//                     if msg_head.us_length < 3 {
//                         continue
//                     }
//                     let msg_len = (msg_head.us_length - 3) as usize;
//                     let mut msg_buf = vec![0u8; msg_len];

//                     match stream.read_exact(&mut msg_buf).await {
//                         Ok(_size) => {
//                             // if size != msg_len as usize {
//                             //     info!("size: {}", size);
//                             //     continue
//                             // }
//                         }
//                         Err(e) => {
//                             info!("error = {:?}", e);
//                             break
//                         }
//                     }

//                     let hby = bytes::BytesMut::from_iter(msg_buf);
//                     if msg_head.bt_type == BtType::ZL_COMMON_CODE as u8 {
//                         // info!("{:?}", CnCode::decode(hby).unwrap());
//                     } else if msg_head.bt_type == BtType::ZL_COMMON_STOCK as u8 {
//                         let stock = CnStock::decode(hby).unwrap_or_default();
//                         if stock.r#type == SecurityType::Stock as i32 || stock.r#type == SecurityType::Fund as i32
//                         /*|| hs_tick.r#type == SecurityType::Index as i32 */|| stock.r#type == SecurityType::BShare as i32 {
//                             let curr_st = utility::timeutil::current_naive_time();
//                             let ret = NaiveDateTime::parse_from_str(&stock.time.to_string(), "%Y%m%d%H%M%S%3f");
//                             if ret.as_ref().is_err() {
//                                 continue
//                             }
//                             let time_diff = curr_st.signed_duration_since(ret.unwrap());
//                             info!("{} {} {} {} {} {}", get_exchange_id(stock.market as i64), stock.symbol, stock.name,stock.time, curr_st.format("%Y%m%d%H%M%S%3f"), time_diff);
//                         }
//                     } else if msg_head.bt_type == BtType::ZL_COMMON_ORDER as u8 {
//                         // info!("{:?}", CnOrder::decode(hby).unwrap());
//                         // let _ = self.tx_cn_order.send(CnOrder::decode(hby).unwrap_or_default());
//                         // insert_cn_order(&CnOrder::decode(hby).unwrap_or_default()).await;
//                     } else if msg_head.bt_type == BtType::ZL_CLIENT_HEARTBEAT as u8 {
//                         // info!("心跳: {:?}", msg_head);
//                     } else if msg_head.bt_type == BtType::ZL_CLIENT_KCCLOSE as u8 {
//                         // info!("{:?}", KcClose::decode(hby).unwrap());
//                     } else if msg_head.bt_type == BtType::ZL_CODE_END as u8 {
//                         info!("ZL_CODE_END: 标识码表接收完毕: {:?}", msg_head);
//                     } else if msg_head.bt_type == BtType::FIU_SOUTH as u8 {
//                         // info!("{:?}", South::decode(hby).unwrap());
//                     }
//                 }
//                 Err(e) => {
//                     info!("error = {:?}", e);
//                     break
//                 }
//             }
//             //定时心跳
//         }
//     }
//     // });

//     Ok(())
// }

// pub async fn hk_hq_process(uri: String) -> Result<()> {
//     let mut stream = match TcpStream::connect(uri.to_owned()).await {
//         Ok(tcp_stream) => {
//             info!("连接成功: {}", uri);
//             tcp_stream
//         }
//         Err(err) => {
//             error!("{:?}", err);
//             return Err(anyhow!("{:?}", err));
//         }
//     };

//     let mut buf = [0u8; 3];
//     // tokio::spawn(async move {
//     loop {
//         tokio::select! {
//             result = stream.read_exact(&mut buf) => match result {
//                 Ok(size) => {
//                     if size != 3 {
//                         info!("size: {}", size);
//                         continue
//                     }
//                     let msg_head = FiuHeader::analysis_head(&buf);
//                     if msg_head.us_len < 3 {
//                         continue
//                     }
//                     let msg_len = (msg_head.us_len - 3) as usize;
//                     let mut msg_buf = vec![0u8; msg_len];
//                     match stream.read_exact(&mut msg_buf).await {
//                         Ok(_size) => {
//                             // if size != msg_len as usize {
//                             //     info!("size: {}", size);
//                             //     continue
//                             // }
//                         }
//                         Err(e) => {
//                             info!("error = {:?}", e);
//                             break
//                         }
//                     };
//                     let hby = bytes::BytesMut::from_iter(msg_buf);
//                     pag_hk_msg(msg_head.c_type, hby).await;
//                 }
//                 Err(e) => {
//                     info!("error = {:?}", e);
//                     break
//                 }
//             }
//             //定时心跳
//         }
//     }
//     // });

//     Ok(())
// }

// pub async fn pag_hk_msg(c_type: u8, hby: BytesMut) {
//     match c_type {
//         // v if v == FiuType::FIUHeartbeat as u8 => {
//         //     // info!("Heartbeat");
//         // }
//         // v if v == FiuType::FIUCODE as u8 => { // PBStockDefine
//         //      // info!("{:?}", PbStockDefine::decode(hby).unwrap_or_default());
//         // }
//         // v if v == FiuType::FIU_SNAPSHOT as u8 => {
//         //     // PBSnapshot
//         //     // info!("{:?}", PbSnapshot::decode(hby).unwrap_or_default());
//         //     // let _ = tx_hk.send(PbSnapshot::decode(hby).unwrap_or_default());
//         //     let stock = PbSnapshot::decode(hby).unwrap_or_default();
//         //     let curr_st = utility::timeutil::current_naive_time();
//         //     let ret = NaiveDateTime::parse_from_str(&stock.time.to_string(), "%Y%m%d%H%M%S%3f");
//         //     if ret.as_ref().is_err() {
//         //         return;
//         //     }
//         //     let time_diff = curr_st.signed_duration_since(ret.unwrap());
//         //     info!("XHKG {:05} {} {} {} {}", stock.code, stock.name, stock.time, curr_st.format("%Y%m%d%H%M%S%3f"), time_diff);
//         // }
//         // v if v == FiuType::FIU_ORDER as u8 => { // PBOrderItem PBOrder
//         //      // info!("{:?}", PbOrder::decode(hby)?);
//         //      // let _ = tx_order.send(PbOrder::decode(hby).unwrap_or_default());
//         //      // insert_pb_order(&PbOrder::decode(hby).unwrap_or_default()).await;
//         // }
//         // v if v == FiuType::FIU_IndexDefine as u8 => { // PBIndexDefine
//         //      // info!("{:?}", PbIndexDefine::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_Trade as u8 => { // PBTrade
//         //      // info!("{:?}", PbTrade::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_Index as u8 => { // PBIndex
//         //      // info!("{:?}", PbIndex::decode(hby).unwrap_or_default());
//         // }
//         // v if v == FiuType::FIU_VcmTrigger as u8 => { // PBVcmTrigger
//         //      // info!("{:?}", PbVcmTrigger::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_OrderBroker as u8 => { // PBOrderBroker
//         //      // info!("{:?}", PbOrderBroker::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_ConnectBalance as u8 => { // PBConnectBalance
//         //      // info!("{:?}", PbConnectBalance::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_ConnectTurnover as u8 => { // PBConnectTurnover
//         //      // info!("{:?}", PbConnectTurnover::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_SecurityStatus as u8 => { //PBSecurityStatus
//         //      // info!("{:?}", PbSecurityStatus::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_EquipPrice as u8 => { // PBEquipPrice
//         //      // info!("{:?}", PbEquipPrice::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_OrderImbalance as u8 => { // PBOrderImbalance
//         //      // info!("{:?}", PbOrderImbalance::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_TradeSessionStatus as u8 => { // PBTradeSessionStatus
//         //      // info!("{:?}", PbTradeSessionStatus::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_RefPrice as u8 => { // PBRefPrice
//         //      // info!("{:?}", PbRefPrice::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_OddLot as u8 => { // PBOddLot
//         //      // info!("{:?}", PbOddLot::decode(hby)?);
//         // }
//         // v if v == FiuType::FIU_MarketTurnOver as u8 => { // PBMarketTurnOver
//         //      // info!("{:?}", PbMarketTurnOver::decode(hby)?);
//         // }
//         _ => {}
//     }
// }
