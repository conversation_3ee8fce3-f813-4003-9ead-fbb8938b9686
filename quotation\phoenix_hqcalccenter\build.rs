// // use std::{env, path::PathBuf};
// // extern crate protoc_rust;
// fn build_grpc() {
//     tonic_build::configure()
//         .out_dir("src/protofiles")
//         .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
//         .compile_protos(&["protos/hqmsg.proto", "protos/phoenixklinecenter.proto", "protos/marketdata.proto"], &["protos"])
//         .unwrap();
// }

fn main() {
    //     build_grpc();
}
