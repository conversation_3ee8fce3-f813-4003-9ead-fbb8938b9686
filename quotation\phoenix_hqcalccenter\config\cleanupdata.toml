

[cassandra]
username = "uatcass"
password = "uatcass!2"
# addr = ""
addr = "8.154.33.91:11092"
hs_namespace = "stockhqklinedata_kyc"
hk_namespace = "stockhqklinedata_hk"

[fiusource]
hsfiuapi = "https://fat-hs-api-sdk.szfiu.com"
# hkfiuapi = "https://fat-hk-api-sdk.szfiu.com"
hkfiuapi = ""


[kline]
tablename = [
    "tstockhq1kline",
    "tstockhq5kline",
    "tstockhq10kline",
    "tstockhq30kline",
    "tstockhq60kline",
    "tstockhqdaykline",
]
start_time = "20241214090000"
end_time = "20241214160000"

[tick]
tablename = "tstockhqtick"
start_time = "1734106076"
end_time = "1734192476"

[timeshare]
tablename = "tstockhqtimeshare"
start_time = "20241214"
end_time = "20241214"
