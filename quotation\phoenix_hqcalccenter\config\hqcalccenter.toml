[application]
apphost = "127.0.0.1"
appport = 10001

[cassandra]
username = "uatcass"
password = "uatcass!2"
addr = ""
# addr = "***********:11092"
namespace = "stockhqklinedata_kyc"
# namespace = "stockhqklinedata_hk"

[rabbitmq]
msgvhost = "flower"
amqpaddr = "amqp://pp:pp123@***********:5672/"

[system]
exchangeno = "HS,XSHE,XSHG"
# quotationserver = "http://uat-stock.chinaeast2.cloudapp.chinacloudapi.cn:50021"#A股
# quotationserver = "http://**************:8460"
quotationserver = "http://************:8460"
akaserver = "http://************:7402"
logserver = "http://************:8420"
# exchangeno = "HK,XHKG"
# quotationserver = "http://uat-stock.chinaeast2.cloudapp.chinacloudapi.cn:50039"#港股
# exchangeno = ""
# quotationserver = "http://uat-stock.chinaeast2.cloudapp.chinacloudapi.cn:50050"#美股
cassinterval = 12
channelcap = 204800
filepath = "hqcalccenter"

[quotaiontime]
XSHG = ["09:30:00-11:30:00", "13:00:00-15:00:00"] #上交所
XSHE = ["09:30:00-11:30:00", "13:00:00-15:00:00"] #深交所
HS = ["09:30:00-11:30:00", "13:00:00-15:00:00"]   #A股板块
# XSHG = "09:30:00-11:30:00,13:00:00-15:00:00"        #A股指数
# XSHE = "09:30:00-11:30:00,13:00:00-15:00:00"        #A股指数
XHKG = ["09:30:00-12:00:00", "13:00:00-16:00:00"] #港交所
HK = ["09:30:00-12:00:00", "13:00:00-16:00:00"]   #港股板块
# SE_HKI = "09:30:00-12:00:00,13:00:00-16:00:00"      #港股指数
XASE = ["09:30:00-16:00:00"] #美交所
XNYS = ["09:30:00-16:00:00"] #纽交所
XNAS = ["09:30:00-16:00:00"] #纳斯达克
US = ["09:30:00-16:00:00"]   #美股板块
# SE_USI = "09:30:00-16:00:00"                        #美股指数

#主程序不需要填//只修复日Kline
# [repairconfig]
# hsfiuapi = "https://fat-hs-api-sdk.szfiu.com"
# # hsfiuapi = ""
# # hkfiuapi = "https://fat-hk-api-sdk.szfiu.com"
# hkfiuapi = ""
# tablename = "ls"
# start_time = "20241108150000"
# end_time = "20241109150000"
# kline_time = "2024-11-08"      #日期为单位格式： 2024-9-11, 时间为单位：2024-9-11 13:00:00
# page_size = 1                 #数据量
# kline_type = 0                 #K线类型：0（日）、1（周）、2（月）、3（季）、4（年）、5 （1分）、6（5分）、7（15分）、8（30分）、9（60分）、10（120分）、11（3分）、11（240分）。实际情况以数据字典为准。,示例值(1)


[repairconfig]
hsfiuapi = "https://fat-hs-api-sdk.szfiu.com"
# hsfiuapi = ""
# hkfiuapi = "https://fat-hk-api-sdk.szfiu.com"
hkfiuapi = ""
tablename = "tstockhqdaykline"
start_time = "20241111150000"
end_time = "20241111150000"
kline_time = "2024-11-11"      #日期为单位格式： 2024-9-11, 时间为单位：2024-9-11 13:00:00
page_size = 1                  #数据量
kline_type = 0                 #K线类型：0（日）、1（周）、2（月）、3（季）、4（年）、5 （1分）、6（5分）、7（15分）、8（30分）、9（60分）、10（120分）、11（3分）、11（240分）。实际情况以数据字典为准。,示例值(1)
