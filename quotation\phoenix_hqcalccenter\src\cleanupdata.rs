#[macro_use]
extern crate anyhow;
extern crate chrono;

mod client;
mod commonutil;
mod config;
mod server;

use anyhow::Result;
use reqwest;
use reqwest::header::HeaderMap;
use reqwest::header::HeaderValue;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::json;
use serde_json::{self, Value};
use tracing::{error, info};

use common::logclient::LogClient;

use crate::client::cassandraclient::CassandraClient;
use crate::config::cleanconfig::DeleteConfig;

// use utility::loggings;
#[tokio::main]
async fn main() -> Result<()> {
    // let cfg = "config/hqcalccenter.yaml";
    // loggings::log_init(cfg);

    let prefix = "cleanupdata";
    let dir = "./log";
    let level = "INFO";
    let _guard = common::init_tracing(prefix, dir, level);

    let settings = DeleteConfig::new().unwrap();
    info!("初始化配置信息:{:#?}", &settings);
    let fiu_api = FiuApi::new(&settings.fiusource.hsfiuapi, &settings.fiusource.hkfiuapi).await;
    let cassandra_client = CassandraClient::new(&settings.cassandra.addr, &settings.cassandra.username, &settings.cassandra.password, &settings.cassandra.hs_namespace)
        .await
        .expect("cassandra连接出错..");

    if !settings.fiusource.hsfiuapi.is_empty() {
        //A股 stockhqklinedata_kyc
        let hs_stock = fiu_api.hs_post_stock_basic_info().await;
        let stock_info = convert_local_stock_info(&hs_stock, &Vec::new()).await; //获取所有股票信息
                                                                                 // info!("获取所有股票信息:{:#?}", &stock_info);
        if !settings.tick.tablename.is_empty() {
            info!("开始删除A股Tick数据");
            if let Ok(_) = cassandra_client
                .delete_tick(
                    &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                    &settings.cassandra.hs_namespace,
                    &settings.tick.tablename,
                    &settings.tick.start_time,
                    &settings.tick.end_time,
                )
                .await
            {
                info!("删除A股Tick数据成功");
            } else {
                error!("删除A股Tick数据失败");
            }
        }

        if !settings.timeshare.tablename.is_empty() {
            info!("开始删除A股分时数据");
            if let Ok(_) = cassandra_client
                .delete_time_line(
                    &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                    &settings.cassandra.hs_namespace,
                    &settings.timeshare.tablename,
                    &settings.timeshare.start_time,
                    &settings.timeshare.end_time,
                )
                .await
            {
                info!("删除A股分时数据成功");
            } else {
                error!("删除A股分时数据失败");
            }
        }

        for v in settings.kline.tablename.iter() {
            info!("开始删除A股{}数据", v);
            if let Ok(_) = cassandra_client
                .delete_kline(
                    &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                    &settings.cassandra.hs_namespace,
                    &v,
                    &settings.kline.start_time,
                    &settings.kline.end_time,
                )
                .await
            {
                info!("删除A股{}数据成功", v);
            } else {
                error!("删除A股{}数据失败", v);
            }
        }
    }

    if !settings.fiusource.hkfiuapi.is_empty() {
        //港股 stockhqklinedata_hk
        let hk_stock = fiu_api.hk_post_stock_basic_info().await;
        let stock_info = convert_local_stock_info(&Vec::new(), &hk_stock).await;
        // info!("获取所有股票信息:{:#?}", &stock_info);
        if !settings.tick.tablename.is_empty() {
            info!("开始删除港股Tick数据");
            if let Ok(_) = cassandra_client
                .delete_tick(
                    &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                    &settings.cassandra.hk_namespace,
                    &settings.tick.tablename,
                    &settings.tick.start_time,
                    &settings.tick.end_time,
                )
                .await
            {
                info!("删除港股Tick数据成功");
            } else {
                error!("删除港股Tick数据失败");
            }
        }
        if !settings.timeshare.tablename.is_empty() {
            info!("开始删除港股分时数据");
            if let Ok(_) = cassandra_client
                .delete_time_line(
                    &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                    &settings.cassandra.hk_namespace,
                    &settings.timeshare.tablename,
                    &settings.timeshare.start_time,
                    &settings.timeshare.end_time,
                )
                .await
            {
                info!("删除港股股分时数据成功");
            } else {
                error!("删除港股股分时数据失败");
            }
        }
        for v in settings.kline.tablename.iter() {
            info!("开始删除港股{}数据", v);
            if let Ok(_) = cassandra_client
                .delete_kline(
                    &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                    &settings.cassandra.hk_namespace,
                    &v,
                    &settings.kline.start_time,
                    &settings.kline.end_time,
                )
                .await
            {
                info!("删除{}数据成功", v);
            } else {
                error!("删除{}数据失败", v);
            }
        }
    }

    Ok(())
}

#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct StockInfo {
    pub name: String,
    pub inner_code: String,
    pub outer_code: String,
    pub market_id: i64,
    pub lot_size: i32, //每手股数
    pub currency: String,
    pub security_status: i32,     //股票状态 0	正常 1	未上市 2	熔断 3	停牌 4	退市
    pub stock_type: i32,          //股票类型1	正股 2	ETF 3	涡轮 4	指数 5	板块 6	其他 7	基金 8	债券 9	ETN 10	B股
    pub plate_codes: Vec<String>, //所属板块代码(预留字段)
    pub inner_stock_type: i32,
}

pub enum Market {
    SH = 0, //上海 XSHG
    SZ = 1, //深圳 XSHE
    // HS
    HK = 2, //香港 XHKG
            // SG
            // US
}

pub fn get_exchange_id(market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => "XSHG".to_string(),
        v if v == Market::SZ as i64 => "XSHE".to_string(),
        v if v == Market::HK as i64 => "XHKG".to_string(),
        _ => "".to_string(),
    }
}

pub fn get_inner_code(symbol: String, market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => format!("{}_XSHG", symbol),
        v if v == Market::SZ as i64 => format!("{}_XSHE", symbol),
        v if v == Market::HK as i64 => format!("{}_XHKG", symbol),
        _ => "".to_string(),
    }
}

pub fn get_outer_code(symbol: String, market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => format!("{}.sh", symbol),
        v if v == Market::SZ as i64 => format!("{}.sz", symbol),
        v if v == Market::HK as i64 => format!("{}.hk", symbol),
        _ => "".to_string(),
    }
}

pub enum SecurityType {
    Stock = 0,             //股票
    Fund = 1,              //基金
    Bond = 2,              //债券
    Index = 3,             //指数
    Forward = 4,           //期货
    BuyBack = 5,           //回购
    StockIndexFutures = 6, //股指期货
    ForeignExchange = 7,   //外汇
    Warrant = 8,           //权证
    BShare = 9,            //B股
}

#[derive(Debug, Clone)]
pub struct FiuApi {
    pub hs_host: String,
    pub hk_host: String,
    pub client: Client,
    pub headers: HeaderMap,
}

impl FiuApi {
    pub async fn new(hs_host: &String, hk_host: &String) -> FiuApi {
        let mut headers = HeaderMap::new();
        headers.insert("Content-Type", HeaderValue::from_static("application/json"));
        FiuApi {
            hs_host: hs_host.to_owned(),
            hk_host: hk_host.to_owned(),
            client: Client::new(),
            headers,
        }
    }

    pub async fn hs_post_stock_list(&self) -> Vec<String> {
        let url = format!("{}/v1/stock/list", self.hs_host);

        let values = [
            json!({"market": "SH", "type": SecurityType::Stock as i32}),
            json!({"market": "SH", "type": SecurityType::Fund as i32}),
            json!({"market": "SH", "type": SecurityType::Index as i32}),
            json!({"market": "SH", "type": SecurityType::BShare as i32}),
            json!({"market": "SZ", "type": SecurityType::Stock as i32}),
            json!({"market": "SZ", "type": SecurityType::Fund as i32}),
            json!({"market": "SZ", "type": SecurityType::Index as i32}),
            json!({"market": "SZ", "type": SecurityType::BShare as i32}),
        ];

        let mut stock_list: Vec<String> = Vec::new();
        for v in values.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;

            match ret {
                Ok(stock_resp) => {
                    if stock_resp.status() != 200 {
                        error!("post hs stock list err code: {:?}", stock_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hs_post_stock_list error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = stock_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hs::HsStockListResp>(resp).unwrap_or_default();
                    stock_list.extend(resp.body.iter().map(|x| x.symbol.clone()));
                }
                Err(err) => {
                    error!("post hs stock list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hs_post_stock_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hs stock list ok: {}", stock_list.len());
        stock_list
    }

    pub async fn hs_post_industry_list(&self) -> Vec<IndustryList> {
        let url = format!("{}/v1/industry/list", self.hs_host);

        let values = [json!({ "conceptFlag": "Y"}), json!({ "conceptFlag": "N"})];

        let mut industry_list: Vec<IndustryList> = Vec::new();
        for v in values.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(industry_resp) => {
                    if industry_resp.status() != 200 {
                        error!("post hs industry list err code: {:?}", industry_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hs_post_industry_list error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = industry_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<IndustryListResp>(resp).unwrap_or_default();
                    industry_list.extend(resp.body);
                }
                Err(err) => {
                    error!("post hs industry list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hs_post_industry_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hs industry list ok: {}", industry_list.len());
        industry_list
    }

    pub async fn hs_post_stock_basic_info(&self) -> Vec<hs::HsStockBasicInfo> {
        let stock_list = self.hs_post_stock_list().await;
        let list: Vec<Vec<String>> = stock_list.chunks(64).map(|x| x.into()).collect();

        let url = format!("{}/v1/stock/basic/info", self.hs_host);

        let mut hs_stock_infos = Vec::new();

        for v in list.iter() {
            let req = json!({
                "symbols": v
            });
            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(basic_resp) => {
                    if basic_resp.status() != 200 {
                        error!("post hs stock basic info err code: {:?}", basic_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hs_post_stock_basic_info error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = basic_resp.json().await.unwrap_or_default();
                    // info!("{:#?}", resp);
                    let mut resp = serde_json::from_value::<hs::HsStockBasicInfoResp>(resp).unwrap_or_default();
                    // 1	股票 2	ETF 4	指数 7	基金
                    resp.body.retain(|x| x.stock_type == 1 || x.stock_type == 2 || x.stock_type == 4 || x.stock_type == 7);
                    hs_stock_infos.extend(resp.body);
                }
                Err(err) => {
                    error!("post hs stock basic info err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hs_post_stock_basic_info error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hs stock basic info ok: {}", hs_stock_infos.len());
        hs_stock_infos
    }

    pub async fn hk_post_stock_list(&self) -> Vec<String> {
        let url = format!("{}/v3/stock/list", self.hk_host);

        let value = [
            json!({"market": "MAIN", "types": ["EQTY"]}), //港股主板
            json!({"market": "GEM", "types": ["EQTY"]}),  //港股创业板
        ];

        let mut stock_list: Vec<String> = Vec::new();
        for v in value.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(stock_resp) => {
                    if stock_resp.status() != 200 {
                        error!("post hk stock list code: {:?}", stock_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hk_post_stock_list error")).await;
                        }
                        return Vec::new();
                    }
                    // info!("{}", ret.status());
                    let resp: serde_json::Value = stock_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hk::HkStockListResp>(resp).unwrap_or_default();
                    stock_list.extend(resp.body.iter().map(|x| x.symbol.clone()));
                }
                Err(err) => {
                    error!("post hk stock list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_post_stock_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hk stock list ok: {}", stock_list.len());
        stock_list
    }

    pub async fn hk_post_industry_list(&self) -> Vec<IndustryList> {
        let url = format!("{}/v3/industry/list", self.hk_host);

        let values = [json!({ "conceptFlag": "Y"}), json!({ "conceptFlag": "N"})];

        let mut industry_list: Vec<IndustryList> = Vec::new();
        for v in values.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(industry_resp) => {
                    if industry_resp.status() != 200 {
                        error!("post hk industry list code: {:?}", industry_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hk_post_industry_list error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = industry_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<IndustryListResp>(resp).unwrap_or_default();
                    industry_list.extend(resp.body);
                }
                Err(err) => {
                    error!("post hk industry list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_post_industry_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hk industry list ok: {}", industry_list.len());
        industry_list
    }

    pub async fn hk_post_stock_basic_info(&self) -> Vec<hk::HkStockBasicInfo> {
        let symbols: Vec<String> = self.hk_post_stock_list().await;
        let list: Vec<Vec<String>> = symbols.chunks(64).map(|x| x.into()).collect();

        let url = format!("{}/v3/stock/basic/info", self.hk_host);

        let mut hk_stock_infos = Vec::new();
        for v in list.iter() {
            let req = json!({
                "symbols": v
            });

            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(basic_resp) => {
                    if basic_resp.status() != 200 {
                        error!("post hk stock basic info code: {:?}", basic_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hk_post_stock_basic_info error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = basic_resp.json().await.unwrap_or_default();
                    // info!("{:#?}", resp);
                    let mut resp = serde_json::from_value::<hk::HkStockBasicInfoResp>(resp).unwrap_or_default();
                    // 1	股票 2	ETF 4	指数 7	基金
                    resp.body.retain(|x| x.stock_type == 1 || x.stock_type == 2 || x.stock_type == 4 || x.stock_type == 7);
                    hk_stock_infos.extend(resp.body);
                }
                Err(err) => {
                    error!("post hk stock basic info err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_post_stock_basic_info error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hk stock basic info ok: {}", hk_stock_infos.len());
        hk_stock_infos
    }
}

pub async fn convert_local_stock_info(hsbasic: &Vec<hs::HsStockBasicInfo>, hkbasic: &Vec<hk::HkStockBasicInfo>) -> Vec<StockInfo> {
    let mut stockinfo = Vec::new();
    for v in hsbasic.iter() {
        if v.symbol.is_null() || v.market.is_null() || v.security_status.is_null() || v.name.is_null() {
            continue;
        }
        let symbol = v.symbol.as_str().unwrap_or_default().to_owned();
        let market = v.market.as_i64().unwrap_or_default();
        let inner_code = get_inner_code(symbol.split(".").collect::<Vec<&str>>()[0].to_owned(), market);
        if inner_code.is_empty() {
            continue;
        }

        let stock_info = StockInfo {
            name: v.name.as_str().unwrap_or_default().to_owned(),
            inner_code,
            outer_code: symbol.to_owned(),
            market_id: if market == Market::SH as i64 { 101 } else { 102 },
            lot_size: v.lot_size.as_i64().unwrap_or(100) as i32,
            currency: v.currency.as_str().unwrap_or("CNY").to_owned(),
            security_status: v.security_status.as_i64().unwrap_or_default() as i32,
            stock_type: v.stock_type.as_i64().unwrap_or_default() as i32,
            plate_codes: {
                let codes = v.plate_codes.as_array().unwrap();
                codes
                    .into_iter()
                    .filter_map(|val| match val {
                        Value::String(s) => Some(s.clone()),
                        _ => None,
                    })
                    .collect()
            },
            inner_stock_type: {
                let board = v.board.as_i64().unwrap_or(0);
                match board {
                    1 => 1, //主板
                    3 => 4, //创业板
                    6 => 5, //科创板
                    // 7 => 8, //深交所退市整理板
                    // 8 => 8, //上交所风险警示板
                    _ => {
                        if v.security_status.as_i64().unwrap_or_default() as i32 == 7 {
                            //ETF
                            6
                        } else {
                            0
                        }
                    }
                }
            },
        };
        stockinfo.push(stock_info);
    }

    for v in hkbasic.iter() {
        if v.symbol.is_null() || v.market.is_null() || v.security_status.is_null() || v.name.is_null() {
            continue;
        }

        let symbol = v.symbol.as_str().unwrap_or_default().to_owned();
        let inner_code = get_inner_code(symbol.split(".").collect::<Vec<&str>>()[0].to_owned(), 2);
        if inner_code.is_empty() {
            continue;
        }

        let stock_info = StockInfo {
            name: v.name.as_str().unwrap_or_default().to_owned(),
            inner_code,
            outer_code: symbol.to_owned(),
            market_id: 103,
            lot_size: v.lot_size.as_i64().unwrap_or(100) as i32,
            currency: v.currency.as_str().unwrap_or("HKD").to_owned(),
            security_status: v.security_status.as_i64().unwrap_or_default() as i32,
            stock_type: v.stock_type.as_i64().unwrap_or_default() as i32,
            plate_codes: {
                let codes = v.plate_codes.as_array().unwrap();
                codes
                    .into_iter()
                    .filter_map(|val| match val {
                        Value::String(s) => Some(s.clone()),
                        _ => None,
                    })
                    .collect()
            },
            inner_stock_type: {
                let board = v.market.as_str().unwrap_or("");
                match board {
                    "MAIN" => 2, //主板 板块内码：1002_HK
                    "GEM" => 4,  //创业板 板块内码：1003_HK
                    // "ETS" => 5,//扩充交易证券
                    // "NASD" => 6,//NASDAQ AMX市场
                    _ => {
                        if v.security_status.as_i64().unwrap_or_default() as i32 == 7 {
                            ////ETF
                            6
                        } else {
                            0
                        }
                    }
                }
            },
        };
        stockinfo.push(stock_info);
    }

    stockinfo
}

pub fn get_kline_type(kline_type: i32) -> i32 {
    match kline_type {
        0 => 1440,
        5 => 1,
        6 => 5,
        8 => 30,
        9 => 60,
        _ => 0,
    }
}

#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct IndustryList {
    #[serde(rename = "conceptFlag")]
    pub concept_flag: String,
    pub name: String,
    pub symbol: String,
}

#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct IndustryListResp {
    pub code: String,
    pub msg: String,
    pub body: Vec<IndustryList>,
}

pub mod hs {
    use serde::{Deserialize, Serialize};
    use serde_json::{self};

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockList {
        pub name: String,
        pub symbol: String,
    }

    /// 全部证券列表
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockListResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HsStockList>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockBasicInfo {
        pub board: serde_json::Value,    //board	交易板块	integer(int32)	1	主板 3	创业板 6	科创板 7	深交所退市整理板 8	上交所风险警示板
        pub currency: serde_json::Value, //currency	币种	string
        #[serde(rename = "delistingDate")]
        pub delisting_date: serde_json::Value, //delistingDate	退市日期	string
        pub exchange: serde_json::Value, //exchange	交易所	integer(int32)
        #[serde(rename = "hkSymbol")]
        pub hk_symbol: serde_json::Value, //hkSymbol	港股上市证券代码	string
        #[serde(rename = "listingDate")]
        pub listing_date: serde_json::Value, //listingDate	上市日期	string
        #[serde(rename = "lotSize")]
        pub lot_size: serde_json::Value, //lotSize	每手股数	integer(int32)
        pub market: serde_json::Value,   //market	市场代码	integer(int32)
        #[serde(rename = "marketStatus")]
        pub market_status: serde_json::Value, //marketStatus	市场状态	integer(int32)
        pub name: serde_json::Value,     //name	名称	string
        #[serde(rename = "plateCodes")]
        pub plate_codes: serde_json::Value, //plateCodes	所属板块代码(预留字段)	array	string
        #[serde(rename = "preClose")]
        pub pre_close: serde_json::Value, //preClose	昨收价	number(bigdecimal)
        #[serde(rename = "securityStatus")]
        pub security_status: serde_json::Value, //securityStatus	股票状态	integer(int32)	0	正常 1	未上市 2	熔断 3	停牌 4	退市
        #[serde(rename = "shConnectHk")]
        pub sh_connect_hk: serde_json::Value, //shConnectHk	沪股通股票标记	string
        #[serde(rename = "shortName")]
        pub short_name: serde_json::Value, //shortName	英文简称	string
        #[serde(rename = "stockType")]
        pub stock_type: serde_json::Value, //stockType	股票类型	integer(int32)	1	正股 2	ETF 3	涡轮 4	指数 5	板块 6	其他 7	基金 8	债券 9	ETN 10	B股
        pub symbol: serde_json::Value,   //symbol	代码 证券代码、指数代码等	string
        #[serde(rename = "szConnectHk")]
        pub sz_connect_hk: serde_json::Value, //szConnectHk	深股通股票标记	string
        #[serde(rename = "usSymbol")]
        pub us_symbol: serde_json::Value, //usSymbol	美股上市证券代码	string
    }

    ///证券静态基本数据
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockBasicInfoResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HsStockBasicInfo>,
    }
}

// HsStockBasicInfo {
//     board: Null,
//     currency: Null,
//     delisting_date: Null,
//     exchange: Null,
//     hk_symbol: Null,
//     listing_date: Null,
//     lot_size: Null,
//     market: Null,
//     market_status: Null,
//     name: Null,
//     plate_codes: Null,
//     pre_close: Null,
//     security_status: Null,
//     sh_connect_hk: Null,
//     short_name: Null,
//     stock_type: Null,
//     symbol: String("980028.sz"),
//     sz_connect_hk: Null,
//     us_symbol: Null,
// },

pub mod hk {
    use serde::{Deserialize, Serialize};
    // use serde_json::json;
    use serde_json::{self};

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockList {
        pub name: String,
        pub symbol: String,
    }

    /// 全部证券列表
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]

    pub struct HkStockListResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HkStockList>,
    }

    ///证券静态基本数据
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockBasicInfo {
        pub currency: serde_json::Value, //currency	币种。	string
        #[serde(rename = "delistingDate")]
        pub delisting_date: serde_json::Value, //delistingDate	退市日期。	string
        #[serde(rename = "enName")]
        pub en_name: serde_json::Value, //enName	英文名称	string
        #[serde(rename = "hkConnectSh")]
        pub hk_connect_sh: serde_json::Value, //hkConnectSh	沪港通股票标记。	string
        #[serde(rename = "hkConnectSz")]
        pub hk_connect_sz: serde_json::Value, //hkConnectSz	深港通股票标记。	string
        #[serde(rename = "hsSymbol")]
        pub hs_symbol: serde_json::Value, //hsSymbol	A股上市证券代码。	string
        #[serde(rename = "ipoStatus")]
        pub ipo_status: serde_json::Value, //ipoStatus	IPO状态。	integer(int32)
        #[serde(rename = "listingDate")]
        pub listing_date: serde_json::Value, //listingDate	上市日期。	string
        #[serde(rename = "lotSize")]
        pub lot_size: serde_json::Value, //lotSize	每手股数。	integer(int32)
        pub market: serde_json::Value,   //market	市场代码。	string
        #[serde(rename = "marketStatus")]
        pub market_status: serde_json::Value, //marketStatus	市场状态。	integer(int32)
        pub name: serde_json::Value,     //name	名称。	string
        #[serde(rename = "plateCodes")]
        pub plate_codes: serde_json::Value, //plateCodes	所属板块代码。	array	string
        #[serde(rename = "preClose")]
        pub pre_close: serde_json::Value, //preClose	昨收价。	number(bigdecimal)
        #[serde(rename = "securityStatus")]
        pub security_status: serde_json::Value, //securityStatus	股票状态。	integer(int32)	0	正常 1	未上市 2	熔断 3	停牌 4	退市
        #[serde(rename = "shortName")]
        pub short_name: serde_json::Value, //shortName	简称。	string
        pub spread: serde_json::Value,   //spread	价差标识。	string
        #[serde(rename = "stockOwner")]
        pub stock_owner: serde_json::Value, //stockOwner	权证所属正股的代码。	string
        #[serde(rename = "stockType")]
        pub stock_type: serde_json::Value, //stockType	股票类型。	integer(int32)1	股票 2	ETF 3	权证 4	指数 5	板块 6	其他 7	基金 8	债券
        pub symbol: serde_json::Value,   //symbol	代码。证券代码、指数代码等	string
        #[serde(rename = "tcName")]
        pub tc_name: serde_json::Value, //tcName	繁体中文名称(Traditional Chinese Name)	string
        #[serde(rename = "usSymbol")]
        pub us_symbol: serde_json::Value, //usSymbol	美股上市证券代码。	string
        #[serde(rename = "warrantType")]
        pub warrant_type: serde_json::Value, //warrantType	权证子类型。string
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockBasicInfoResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HkStockBasicInfo>,
    }
}
