use anyhow::Result;
use common::logclient::log_error;
use tokio::sync::broadcast::Sender;
// use tokio_stream::StreamExt;
use tonic::transport::Channel;
use tracing::{error, info};

use protoes::{hqmsg::YsHqInfo, marketdata::market_data_servers_client::MarketDataServersClient, marketdata::ContractMsg};

// use crate::server::service::common::push_log;

// #[derive(Debug)]
#[allow(dead_code)]
pub struct MarketDataclient {
    exchangeno: Vec<String>,
    pub client: Option<MarketDataServersClient<Channel>>,
    // pub client_hk: MarketDataServersClient<Channel>,
    tx_tick: Sender<YsHqInfo>,
    uri: String,
}

impl MarketDataclient {
    #[allow(dead_code)]
    pub async fn new(uri: &String, exchangenos: &String, tx_tick: Sender<YsHqInfo>) -> Self {
        let mut market_uri = uri.clone();
        if !uri.contains("http://") {
            market_uri = format!("http://{}", uri);
        }
        let exchangeno: Vec<String> = exchangenos.split(',').map(|x| x.to_string()).collect();
        let mut market_dataclient = MarketDataclient {
            client: None,
            exchangeno,
            tx_tick,
            uri: market_uri,
        };
        let ret = MarketDataServersClient::connect(market_dataclient.uri.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(format!("connect to MarketDataServer failed: {:?}", market_dataclient.uri).as_str()).await;
        } else {
            info!("行情数据服务连接成功....");
            market_dataclient.client = Some(ret.expect("connect to MarketDataServer failed"))
        }
        market_dataclient
    }

    #[allow(dead_code)]
    pub async fn init_client(&mut self) -> Result<MarketDataServersClient<Channel>> {
        if self.client.is_some() {
            return Ok(self.client.clone().unwrap());
        } else {
            let ret = MarketDataServersClient::connect(self.uri.to_owned()).await;
            if ret.as_ref().is_err() {
                // push_log(format!("connect to MarketDataServer failed: {:?}", self.uri).as_str()).await;
                log_error(format!("connect to MarketDataServer failed: {:?}", self.uri).as_str()).await;
                return Err(anyhow!(format!("connect to MarketDataServer failed")));
            }
            let client = ret.expect("connect to MarketDataServer failed");
            info!("行情数据服务连接成功....");
            self.client = Some(client);
            return Ok(self.client.clone().unwrap());
        }
    }

    #[allow(dead_code)]
    pub async fn do_subscribe_market_data(&mut self) -> Result<()> {
        let ret = self.init_client().await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();

        let response = match client
            .subscribe_market_data(ContractMsg {
                exchange_no: "ALL".to_owned(),
                commodity_no: "ALL".to_string(),
                contract_no: "ALL".to_string(),
                channel_no: 0,
                subscribe: true,
            })
            .await
        {
            Ok(val) => val,
            Err(status) => {
                self.client = None;
                error!("{:?}", status);
                log_error(format!("MarketDataServer err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!(format!("MarketDataServer err")));
            }
        };

        let mut inbound = response.into_inner();
        while let Ok(inbound_data) = inbound.message().await {
            if inbound_data.is_some() {
                let hqinfo = inbound_data.unwrap();
                // if hqinfo.contract_no1 == "600000_XSHG" {
                if self.exchangeno.iter().find(|&x| x == &hqinfo.exchange_id).is_some() {
                    // info!("code: {}, time: {}", &hqinfo.contract_no1, &hqinfo.tapidtstamp);
                    if let Err(e) = self.tx_tick.send(hqinfo.to_owned()) {
                        error!("send to channel error...........:{:?}", &e);
                    }
                }
                // }
            } else {
                error!("inbound data empty");
                break;
            }
        }
        error!("gRPC error");
        Ok(())
    }

    // pub async fn retry_do_subscribe_market_data(&mut self) -> Result<()>{
    //     let client = MarketDataServersClient::connect(self.uri.to_owned()).await;//.expect("行情服务连接失败");
    //     if client.as_ref().is_err() {
    //         error!("{:?}", client);
    //         return Err(anyhow!("{:?}", client));
    //     }
    //     self.client = client.unwrap();
    //     let get_ret = self.do_subscribe_market_data().await;
    //     if get_ret.as_ref().is_err() {
    //         error!("can't subscribe market data......{:?}", get_ret.as_ref().err().unwrap());
    //         return Err(anyhow!("{:?}", get_ret));
    //     }
    //     Ok(())
    // }

    // async fn get_client(&self) -> MarketDataServersClient<Channel> {
    //     self.client.clone()
    // }

    // pub fn convert_market_data_to_quotationinfo(mkdata: &MarketData) -> QuotationInfo {
    //     QuotationInfo { ..Default::default() }
    // }
}
