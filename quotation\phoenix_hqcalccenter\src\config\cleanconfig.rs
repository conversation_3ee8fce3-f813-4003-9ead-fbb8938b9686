use config::{Config, ConfigError, File};
use serde::Deserialize;
// use std::collections::HashMap;
#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct CassandraConfig {
    pub addr: String,         //地址：data.metestsvr.com:11092
    pub username: String,     //用户名：cassdbuser
    pub password: String,     //密码：cassdbuser4321
    pub hs_namespace: String, //
    pub hk_namespace: String, //
}

// #[derive(Debug, <PERSON>lone, Deserialize)]
// pub struct Tablename {
//     pub tstockhqtimeshare: String,
//     pub tstockhq1kline: String,
//     pub tstockhq5kline: String,
//     pub tstockhq10kline: String,
//     pub tstockhq30kline: String,
//     pub tstockhq60kline: String,
//     pub tstockhqdaykline: String,
//     pub tstockhqtick: String,
// }

#[derive(Debug, <PERSON>lone, Deserialize)]
#[allow(dead_code)]
pub struct FiuSource {
    pub hsfiuapi: String,
    pub hkfiuapi: String,
}
#[derive(Debug, <PERSON>lone, Deserialize)]
#[allow(dead_code)]
pub struct Kline {
    pub tablename: Vec<String>,
    pub start_time: String,
    pub end_time: String,
}
#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct Timeshare {
    pub tablename: String,
    pub start_time: String,
    pub end_time: String,
}
#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct Tick {
    pub tablename: String,
    pub start_time: String,
    pub end_time: String,
}

#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct DeleteConfig {
    pub cassandra: CassandraConfig,
    pub fiusource: FiuSource,
    pub kline: Kline,
    pub timeshare: Timeshare,
    pub tick: Tick,
}

impl DeleteConfig {
    #[allow(dead_code)]
    pub fn new() -> Result<Self, ConfigError> {
        let s = Config::builder().add_source(File::with_name("config/cleanupdata.toml")).build().expect("build config file error");

        s.try_deserialize()
    }
}
