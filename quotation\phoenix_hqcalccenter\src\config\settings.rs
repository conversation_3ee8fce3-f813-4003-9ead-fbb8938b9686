use config::{Config, ConfigError, File};
use serde::Deserialize;
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Deserialize)]
#[allow(dead_code)]
pub struct Application {
    pub apphost: String,
    pub appport: i32,
}

// #[derive(Debug, Clone, Deserialize)]
// pub struct Database {
//     pub uri: String,
// }
#[derive(Debug, <PERSON>lone, Deserialize)]
#[allow(dead_code)]
pub struct CassandraConfig {
    pub addr: String,      //地址：data.metestsvr.com:11092
    pub username: String,  //用户名：cassdbuser
    pub password: String,  //密码：cassdbuser4321
    pub namespace: String, //
}

// #[derive(Debug, <PERSON>lone, Deserialize)]
// pub struct RedisConfig {
//     pub password: String,//密码
//     pub redisaddrs: String,
//     pub redisdb: i32,
// }

#[derive(Debug, <PERSON>lone, Deserialize)]
#[allow(dead_code)]
pub struct RabbitMQ {
    // pub hqvhost: String,
    pub msgvhost: String,
    pub amqpaddr: String,
}

#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct System {
    pub exchangeno: String,
    pub quotationserver: String,
    pub akaserver: String,
    pub logserver: String,
    pub cassinterval: i64,
    pub channelcap: i64,
    pub filepath: String,
    pub repairklineflag: bool,
}

#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct RepairConfig {
    pub hsfiuapi: String,
    pub hkfiuapi: String,
    pub tablename: String,
    pub start_time: String,
    pub end_time: String,
    pub kline_time: String,
    pub page_size: i32,
    pub kline_type: i32,
}

#[derive(Debug, Clone, Deserialize)]
#[allow(dead_code)]
pub struct Settings {
    pub application: Application,
    pub system: System,
    pub cassandra: CassandraConfig,
    pub quotaiontime: HashMap<String, Vec<String>>,
    pub repairconfig: RepairConfig,
    // pub database: Database,
    pub rabbitmq: RabbitMQ,
    // pub redis: RedisConfig,
}

impl Settings {
    #[allow(dead_code)]
    pub fn new() -> Result<Self, ConfigError> {
        let s = Config::builder()
            // Start off by merging in the "default" configuration file
            .add_source(File::with_name("config/hqcalccenter.toml"))
            // // Add in the current environment file
            // // Default to 'development' env
            // // Note that this file is _optional_
            // .add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
            // // Add in a local configuration file
            // // This file shouldn't be checked in to git
            // .add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
            // // Add in settings from the environment (with a prefix of APP)
            // // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
            // .add_source(Environment::with_prefix("app"))
            // // You may also programmatically change settings
            // .set_override("database.url", "postgres://")?
            .build()
            .expect("build config file error");

        s.try_deserialize()
    }
}
