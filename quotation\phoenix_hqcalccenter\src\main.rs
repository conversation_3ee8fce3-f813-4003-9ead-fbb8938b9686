#[macro_use]
extern crate anyhow;
extern crate chrono;

mod client;
mod commonutil;
mod config;
mod server;

use anyhow::Result;
use std::process;
use tracing::{error, info};

use common::{init_tracing, logclient::init_logclient};
use protoes::phoenixklinecenter::phoenix_kline_center_server::PhoenixKlineCenterServer;

use crate::config::settings::Settings;
use crate::server::server::CalcServerHandler;

#[tokio::main]
async fn main() -> Result<()> {
    let prefix = "phoenix_hqcalccenter";
    let dir = "./log";

    let settings = Settings::new().unwrap();
    let level = "INFO";
    let _guard = init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:#?}", &settings);

    init_logclient(&settings.system.logserver, &format!("{}_{prefix}", "quotation")).await;

    let server = CalcServerHandler::new(&settings).await.unwrap_or_else(|err| {
        error!("{:?} 服务启动失败...", err);
        // panic!("{}", err)
        process::exit(1);
    });

    let _ = run_server(server).await.unwrap();

    Ok(())
}

async fn run_server(mut server: CalcServerHandler) -> Result<(), Box<dyn std::error::Error>> {
    let app_url = format!("{}:{}", server.settings.application.apphost, server.settings.application.appport);
    let addr = app_url.as_str().parse().unwrap();

    info!("Starting tickceter service on: {}", addr);
    info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    // log_debug(&format!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"))).await;

    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = server.on_leave();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder() //创建可以配置[server]的新服务器生成器。
        .add_service(PhoenixKlineCenterServer::new(server))
        .serve_with_shutdown(addr, async {
            //使用该服务器创建一个未来，在tokio的服务器上执行该服务器,并在接收到所提供的信号时关闭。
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
