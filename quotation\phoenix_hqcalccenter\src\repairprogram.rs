#[macro_use]
extern crate anyhow;
extern crate chrono;

mod client;
mod commonutil;
mod config;
mod server;

use anyhow::Result;
use chrono::Datelike;
use tracing::{error, info};

use common::logclient::LogClient;

use crate::client::cassandraclient::CassandraClient;
use crate::config::settings::Settings;
use crate::server::service::persistservice::CassandraKLineData;

// use utility::loggings;
#[tokio::main]
async fn main() -> Result<()> {
    // let cfg = "config/hqcalccenter.yaml";
    // loggings::log_init(cfg);
    let prefix = "repairprogram";
    let dir = "./log";
    let level = "INFO";
    let _guard = common::init_tracing(prefix, dir, level);

    let settings = Settings::new().unwrap();
    info!("初始化配置信息:{:#?}", &settings);
    let fiu_api = FiuApi::new(&settings.repairconfig.hsfiuapi, &settings.repairconfig.hkfiuapi).await;
    let cassandra_client = CassandraClient::new(&settings.cassandra.addr, &settings.cassandra.username, &settings.cassandra.password, &settings.cassandra.namespace)
        .await
        .expect("cassandra连接出错..");

    correct_hk_kline(&fiu_api, &cassandra_client, &settings).await;
    correct_hs_kline(&fiu_api, &cassandra_client, &settings).await;

    Ok(())
}

async fn correct_hs_kline(fiuapi: &FiuApi, cass_client: &CassandraClient, settings: &Settings) {
    if fiuapi.hs_host.is_empty() {
        info!("不用修正hs kline");
        return;
    }
    info!("开始修正A股kline");
    let hs_stock = fiuapi.hs_post_stock_basic_info().await;
    let stock_info = convert_local_stock_info(&hs_stock, &Vec::new()).await;

    let kline = if !settings.repairconfig.kline_time.is_empty() {
        let kline = fiuapi
            .post_hs_daykline(&stock_info, &settings.repairconfig.kline_time, settings.repairconfig.page_size, settings.repairconfig.kline_type)
            .await;
        info!("hs_stock: {:#?}", kline);
        kline
    } else {
        Vec::new()
    };
    if !settings.repairconfig.tablename.is_empty() {
        if let Err(err) = cass_client
            .delete_data(
                &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                &settings.repairconfig.tablename,
                &settings.repairconfig.start_time,
                &settings.repairconfig.end_time,
            )
            .await
        {
            error!("{:?}", err);
            if let Ok(client) = LogClient::get() {
                client.push_error(&format!("cass_client.delete_data error:{}", err.to_string())).await;
            }
        } else {
            info!("Cassandra K线数据删除完成...");
        }
    }
    if !settings.repairconfig.kline_time.is_empty() {
        if let Err(err) = cass_client.execute_batch_k(&kline).await {
            error!("{:?}", err);
            if let Ok(client) = LogClient::get() {
                client.push_error(&format!("cass_client.execute_batch_k error:{}", err.to_string())).await;
            }
        } else {
            info!("Cassandra hs日K线数据入库完成...");
        }
    }
}

async fn correct_hk_kline(fiuapi: &FiuApi, cass_client: &CassandraClient, settings: &Settings) {
    if fiuapi.hk_host.is_empty() {
        info!("不用修正hk kline");
        return;
    }
    info!("开始修正港股kline");
    let hk_stock = fiuapi.hk_post_stock_basic_info().await;
    let stock_info = convert_local_stock_info(&Vec::new(), &hk_stock).await;

    let kline = if !settings.repairconfig.kline_time.is_empty() {
        let kline = fiuapi
            .post_hk_daykline(&stock_info, &settings.repairconfig.kline_time, settings.repairconfig.page_size, settings.repairconfig.kline_type)
            .await;
        info!("hk_stock: {:#?}", kline);
        kline
    } else {
        Vec::new()
    };
    if !settings.repairconfig.tablename.is_empty() {
        if let Err(err) = cass_client
            .delete_data(
                &stock_info.iter().map(|x| x.inner_code.clone()).collect::<Vec<String>>(),
                &settings.repairconfig.tablename,
                &settings.repairconfig.start_time,
                &settings.repairconfig.end_time,
            )
            .await
        {
            error!("{:?}", err);
            if let Ok(client) = LogClient::get() {
                client.push_error(&format!("cass_client.delete_data error:{}", err.to_string())).await;
            }
        } else {
            info!("Cassandra K线数据删除完成...");
        }
    }
    if !settings.repairconfig.kline_time.is_empty() {
        if let Err(err) = cass_client.execute_batch_k(&kline).await {
            error!("{:?}", err);
            if let Ok(client) = LogClient::get() {
                client.push_error(&format!("cass_client.execute_batch_k error:{}", err.to_string())).await;
            }
        } else {
            info!("Cassandra hk日K线数据入库完成...");
        }
    }
}

use reqwest::header::HeaderMap;
use reqwest::header::HeaderValue;
use reqwest::Client;
use reqwest::{self};
use serde::{Deserialize, Serialize};
use serde_json::json;
use serde_json::{self, Value};

#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct StockInfo {
    pub name: String,
    pub inner_code: String,
    pub outer_code: String,
    pub market_id: i64,
    pub lot_size: i32, //每手股数
    pub currency: String,
    pub security_status: i32,     //股票状态 0	正常 1	未上市 2	熔断 3	停牌 4	退市
    pub stock_type: i32,          //股票类型1	正股 2	ETF 3	涡轮 4	指数 5	板块 6	其他 7	基金 8	债券 9	ETN 10	B股
    pub plate_codes: Vec<String>, //所属板块代码(预留字段)
    pub inner_stock_type: i32,
}

pub enum Market {
    SH = 0, //上海 XSHG
    SZ = 1, //深圳 XSHE
    // HS
    HK = 2, //香港 XHKG
            // SG
            // US
}

pub fn get_exchange_id(market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => "XSHG".to_string(),
        v if v == Market::SZ as i64 => "XSHE".to_string(),
        v if v == Market::HK as i64 => "XHKG".to_string(),
        _ => "".to_string(),
    }
}

pub fn get_inner_code(symbol: String, market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => format!("{}_XSHG", symbol),
        v if v == Market::SZ as i64 => format!("{}_XSHE", symbol),
        v if v == Market::HK as i64 => format!("{}_XHKG", symbol),
        _ => "".to_string(),
    }
}

pub fn get_outer_code(symbol: String, market: i64) -> String {
    match market {
        v if v == Market::SH as i64 => format!("{}.sh", symbol),
        v if v == Market::SZ as i64 => format!("{}.sz", symbol),
        v if v == Market::HK as i64 => format!("{}.hk", symbol),
        _ => "".to_string(),
    }
}

pub enum SecurityType {
    Stock = 0,             //股票
    Fund = 1,              //基金
    Bond = 2,              //债券
    Index = 3,             //指数
    Forward = 4,           //期货
    BuyBack = 5,           //回购
    StockIndexFutures = 6, //股指期货
    ForeignExchange = 7,   //外汇
    Warrant = 8,           //权证
    BShare = 9,            //B股
}

pub mod hs {
    use serde::{Deserialize, Serialize};
    use serde_json::{self};

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockList {
        pub name: String,
        pub symbol: String,
    }

    /// 全部证券列表
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockListResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HsStockList>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockTodayList {
        pub name: String,
        #[serde(rename = "preClose")]
        pub pre_close: f64,
        pub symbol: String,
    }

    /// 当日全部证券列表
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockTodayListResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HsStockTodayList>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockDefine {
        pub currency: String, //币种
        pub date: String,     //数据日期
        #[serde(rename = "hkSymbol")]
        pub hk_symbol: serde_json::Value, //预留字段，港股股上市证券代码。
        #[serde(rename = "listingDate")]
        pub listing_date: serde_json::Value, //上市日期
        #[serde(rename = "lotSize")]
        pub lot_size: i32, //每手股数
        pub market: i32,      //市场，0 上海；1 深圳
        pub name: String,     //证券名称
        #[serde(rename = "preClose")]
        pub pre_close: f64, //昨收价
        #[serde(rename = "shConnectHk")]
        pub sh_connect_hk: String, //沪股通
        pub status: String,   //产品状态
        #[serde(rename = "subType")]
        pub sub_type: String, //证券子类型
        pub symbol: String,   //证券代码
        #[serde(rename = "szConnectHk")]
        pub sz_connect_hk: String, //深股通
        pub r#type: i32,      //证券类型,0:股票、1:基金、2:基金、3:指数、4:期货、5:回购、6:股指期货、7:外汇、8:权证、9:B股
        #[serde(rename = "usSymbol")]
        pub us_symbol: serde_json::Value, //预留字段,美股上市证券代码
    }

    ///股票码表详情
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockDefineResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HsStockDefine>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockQuoteResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HsStockQuote>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsBuySellOrder {
        pub price: f64,
        pub volume: i32,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsOrder {
        #[serde(rename = "askList")]
        pub ask_list: Vec<HsBuySellOrder>,
        #[serde(rename = "bidList")]
        pub bid_list: Vec<HsBuySellOrder>,
        pub time: String,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsSnapshot {
        pub amount: f64,    // "amount": 0,
        pub amplitude: f64, // "amplitude": 0,
        #[serde(rename = "avgPrice")]
        pub avg_price: f64, // "avgPrice": 0,
        pub change: f64,    // "change": 0,
        #[serde(rename = "changeRate")]
        pub change_rate: f64, // "changeRate": 0,
        pub close: f64,     // "close": 0,
        pub high: f64,      // "high": 0,
        pub last: f64,      // "last": 0,
        pub low: f64,       // "low": 0,
        pub open: f64,      // "open": 0,
        #[serde(rename = "preClose")]
        pub pre_close: f64, // "preClose": 0,
        pub time: String,   // "time": "",
        pub volume: f64,    // "volume": 0
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockQuote {
        pub name: String,
        pub order: HsOrder,
        pub snapshot: HsSnapshot,
        pub symbol: String,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockBasicInfo {
        pub board: serde_json::Value,    //board	交易板块	integer(int32)	1	主板 3	创业板 6	科创板 7	深交所退市整理板 8	上交所风险警示板
        pub currency: serde_json::Value, //currency	币种	string
        #[serde(rename = "delistingDate")]
        pub delisting_date: serde_json::Value, //delistingDate	退市日期	string
        pub exchange: serde_json::Value, //exchange	交易所	integer(int32)
        #[serde(rename = "hkSymbol")]
        pub hk_symbol: serde_json::Value, //hkSymbol	港股上市证券代码	string
        #[serde(rename = "listingDate")]
        pub listing_date: serde_json::Value, //listingDate	上市日期	string
        #[serde(rename = "lotSize")]
        pub lot_size: serde_json::Value, //lotSize	每手股数	integer(int32)
        pub market: serde_json::Value,   //market	市场代码	integer(int32)
        #[serde(rename = "marketStatus")]
        pub market_status: serde_json::Value, //marketStatus	市场状态	integer(int32)
        pub name: serde_json::Value,     //name	名称	string
        #[serde(rename = "plateCodes")]
        pub plate_codes: serde_json::Value, //plateCodes	所属板块代码(预留字段)	array	string
        #[serde(rename = "preClose")]
        pub pre_close: serde_json::Value, //preClose	昨收价	number(bigdecimal)
        #[serde(rename = "securityStatus")]
        pub security_status: serde_json::Value, //securityStatus	股票状态	integer(int32)	0	正常 1	未上市 2	熔断 3	停牌 4	退市
        #[serde(rename = "shConnectHk")]
        pub sh_connect_hk: serde_json::Value, //shConnectHk	沪股通股票标记	string
        #[serde(rename = "shortName")]
        pub short_name: serde_json::Value, //shortName	英文简称	string
        #[serde(rename = "stockType")]
        pub stock_type: serde_json::Value, //stockType	股票类型	integer(int32)	1	正股 2	ETF 3	涡轮 4	指数 5	板块 6	其他 7	基金 8	债券 9	ETN 10	B股
        pub symbol: serde_json::Value,   //symbol	代码 证券代码、指数代码等	string
        #[serde(rename = "szConnectHk")]
        pub sz_connect_hk: serde_json::Value, //szConnectHk	深股通股票标记	string
        #[serde(rename = "usSymbol")]
        pub us_symbol: serde_json::Value, //usSymbol	美股上市证券代码	string
    }

    ///证券静态基本数据
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HsStockBasicInfoResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HsStockBasicInfo>,
    }

    // #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    // pub struct HsIndustryList {
    //     #[serde(rename = "conceptFlag")]
    //     pub concept_flag: String,
    //     pub name: String,
    //     pub symbol: String,
    // }

    // #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    // pub struct HsIndustryListResp {
    //     pub code: String,
    //     pub msg: String,
    //     pub body: Vec<HsIndustryList>,
    // }
}

// HsStockBasicInfo {
//     board: Null,
//     currency: Null,
//     delisting_date: Null,
//     exchange: Null,
//     hk_symbol: Null,
//     listing_date: Null,
//     lot_size: Null,
//     market: Null,
//     market_status: Null,
//     name: Null,
//     plate_codes: Null,
//     pre_close: Null,
//     security_status: Null,
//     sh_connect_hk: Null,
//     short_name: Null,
//     stock_type: Null,
//     symbol: String("980028.sz"),
//     sz_connect_hk: Null,
//     us_symbol: Null,
// },

pub mod hk {
    use serde::{Deserialize, Serialize};
    // use serde_json::json;
    use serde_json::{self};

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockStatus {
        pub date: String,
        pub suspension: String, //获取证券停复牌数据。2代表停牌，3代表复牌。
        pub symbol: String,
    }

    /// 证券停复牌状态
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockStatusResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HkStockStatus>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    #[serde(default)]
    pub struct HkStockDefine {
        pub accured: serde_json::Value, //利息
        #[serde(rename = "callPrice")]
        pub call_price: serde_json::Value, //回收价
        #[serde(rename = "callPut")]
        pub call_put: serde_json::Value, //认购沽空标识
        #[serde(rename = "casFlag")]
        pub cas_flag: serde_json::Value, //CAS标识
        #[serde(rename = "ccasFlag")]
        pub ccas_flag: serde_json::Value, //CCASS标识
        #[serde(rename = "conversionRatio")]
        pub conversion_ratio: serde_json::Value, //兑换比率
        #[serde(rename = "couponRate")]
        pub coupon_rate: serde_json::Value, //债券担保息票率
        pub currency: serde_json::Value, //币种
        #[serde(rename = "decimalEntitlement")]
        pub decimal_entitlement: serde_json::Value, //
        pub decimal_price: serde_json::Value, //回收价小数位
        #[serde(rename = "delistingDate")]
        pub delisting_date: serde_json::Value, //退市日期
        #[serde(rename = "dummyFlag")]
        pub dummy_flag: serde_json::Value, //虚拟证券标识
        #[serde(rename = "efnFlag")]
        pub efn_flag: serde_json::Value, //EFN标识
        pub entitlement: serde_json::Value,
        #[serde(rename = "freeText")]
        pub free_text: serde_json::Value, //空闲文本
        pub instrument: serde_json::Value, //品种
        #[serde(rename = "isinCode")]
        pub isin_code: serde_json::Value, //ISIN码
        #[serde(rename = "listingDate")]
        pub listing_date: serde_json::Value, //上市日期
        #[serde(rename = "lotSize")]
        pub lot_size: serde_json::Value, //每手股数
        pub market: serde_json::Value,     //市场代码
        #[serde(rename = "maturityDate")]
        pub maturity_date: serde_json::Value, //到期日
        pub name: serde_json::Value,       //证券名称
        #[serde(rename = "noUnderlying")]
        pub no_underlying: serde_json::Value, //NoUnderlyingSecurities
        #[serde(rename = "noWarrants")]
        pub no_warrants: serde_json::Value, //NoWarrantsPerEntitlement
        #[serde(rename = "posFlag")]
        pub pos_flag: serde_json::Value, //POS标识
        #[serde(rename = "posLower")]
        pub pos_lower: serde_json::Value, //POS最低限价
        #[serde(rename = "posUpper")]
        pub pos_upper: serde_json::Value, //POS最高限价
        #[serde(rename = "preClose")]
        pub pre_close: serde_json::Value, //昨收价
        #[serde(rename = "productType")]
        pub product_type: serde_json::Value, //产品类型
        #[serde(rename = "shortName")]
        pub short_name: serde_json::Value, //证券简称
        #[serde(rename = "shortSell")]
        pub short_sell: serde_json::Value, //沽空标识
        pub spread: serde_json::Value,     //差价标识
        #[serde(rename = "stampDutyFlag")]
        pub stamp_duty_flag: serde_json::Value, //印花税标识
        #[serde(rename = "strikePrice1")]
        pub strike_price1: serde_json::Value, //行使价1
        #[serde(rename = "strikePrice2")]
        pub strike_price2: serde_json::Value, //行使价2
        pub style: serde_json::Value,      //权证类型
        pub symbol: serde_json::Value,     //证券代码
        #[serde(rename = "testFlag")]
        pub test_flag: serde_json::Value, //测试标识
        #[serde(rename = "vcmFlag")]
        pub vcm_flag: serde_json::Value, //VCM标识
        #[serde(rename = "warrantType")]
        pub warrant_type: serde_json::Value, //涡轮类型
    }

    /// 股票码表详情(原始数据)
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockDefineResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HkStockDefine>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockList {
        pub name: String,
        pub symbol: String,
    }

    /// 全部证券列表
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]

    pub struct HkStockListResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HkStockList>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockQuoteResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HkStockQuote>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkBuySellOrder {
        pub number: i32,
        pub price: f64,
        pub volume: i32,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkOrder {
        #[serde(rename = "askList")]
        pub ask_list: Vec<HkBuySellOrder>,
        #[serde(rename = "bidList")]
        pub bid_list: Vec<HkBuySellOrder>,
        pub time: String,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkSnapshot {
        pub amount: f64,    // "amount": 0,
        pub amplitude: f64, // "amplitude": 0,
        #[serde(rename = "avgPrice")]
        pub avg_price: f64, // "avgPrice": 0,
        pub change: f64,    // "change": 0,
        #[serde(rename = "changeRate")]
        pub change_rate: f64, // "changeRate": 0,
        pub close: f64,     // "close": 0,
        pub high: f64,      // "high": 0,
        pub last: f64,      // "last": 0,
        pub low: f64,       // "low": 0,
        pub open: f64,      // "open": 0,
        #[serde(rename = "preClose")]
        pub pre_close: f64, // "preClose": 0,
        #[serde(rename = "shortShares")]
        pub short_shares: f64,
        #[serde(rename = "shortTurnover")]
        pub short_turnover: f64,
        pub time: String, // "time": "",
        pub volume: f64,  // "volume": 0
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockQuote {
        pub name: String,
        pub order: HkOrder,
        pub snapshot: HkSnapshot,
        pub symbol: String,
    }

    ///证券静态基本数据
    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockBasicInfo {
        pub currency: serde_json::Value, //currency	币种。	string
        #[serde(rename = "delistingDate")]
        pub delisting_date: serde_json::Value, //delistingDate	退市日期。	string
        #[serde(rename = "enName")]
        pub en_name: serde_json::Value, //enName	英文名称	string
        #[serde(rename = "hkConnectSh")]
        pub hk_connect_sh: serde_json::Value, //hkConnectSh	沪港通股票标记。	string
        #[serde(rename = "hkConnectSz")]
        pub hk_connect_sz: serde_json::Value, //hkConnectSz	深港通股票标记。	string
        #[serde(rename = "hsSymbol")]
        pub hs_symbol: serde_json::Value, //hsSymbol	A股上市证券代码。	string
        #[serde(rename = "ipoStatus")]
        pub ipo_status: serde_json::Value, //ipoStatus	IPO状态。	integer(int32)
        #[serde(rename = "listingDate")]
        pub listing_date: serde_json::Value, //listingDate	上市日期。	string
        #[serde(rename = "lotSize")]
        pub lot_size: serde_json::Value, //lotSize	每手股数。	integer(int32)
        pub market: serde_json::Value,   //market	市场代码。	string
        #[serde(rename = "marketStatus")]
        pub market_status: serde_json::Value, //marketStatus	市场状态。	integer(int32)
        pub name: serde_json::Value,     //name	名称。	string
        #[serde(rename = "plateCodes")]
        pub plate_codes: serde_json::Value, //plateCodes	所属板块代码。	array	string
        #[serde(rename = "preClose")]
        pub pre_close: serde_json::Value, //preClose	昨收价。	number(bigdecimal)
        #[serde(rename = "securityStatus")]
        pub security_status: serde_json::Value, //securityStatus	股票状态。	integer(int32)	0	正常 1	未上市 2	熔断 3	停牌 4	退市
        #[serde(rename = "shortName")]
        pub short_name: serde_json::Value, //shortName	简称。	string
        pub spread: serde_json::Value,   //spread	价差标识。	string
        #[serde(rename = "stockOwner")]
        pub stock_owner: serde_json::Value, //stockOwner	权证所属正股的代码。	string
        #[serde(rename = "stockType")]
        pub stock_type: serde_json::Value, //stockType	股票类型。	integer(int32)1	股票 2	ETF 3	权证 4	指数 5	板块 6	其他 7	基金 8	债券
        pub symbol: serde_json::Value,   //symbol	代码。证券代码、指数代码等	string
        #[serde(rename = "tcName")]
        pub tc_name: serde_json::Value, //tcName	繁体中文名称(Traditional Chinese Name)	string
        #[serde(rename = "usSymbol")]
        pub us_symbol: serde_json::Value, //usSymbol	美股上市证券代码。	string
        #[serde(rename = "warrantType")]
        pub warrant_type: serde_json::Value, //warrantType	权证子类型。string
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkStockBasicInfoResp {
        pub code: String,
        pub msg: String,
        pub body: Vec<HkStockBasicInfo>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkKLineResp {
        pub code: String,
        pub msg: String,
        pub body: Option<HkKLine>,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct HkKLine {
        #[serde(rename = "endRow")]
        pub end_row: i32,
        #[serde(rename = "hasNextPage")]
        pub has_next_page: bool,
        #[serde(rename = "hasPreviousPage")]
        pub has_previous_page: bool,
        #[serde(rename = "isFirstPage")]
        pub is_first_page: bool,
        #[serde(rename = "isLastPage")]
        pub is_last_page: bool,
        pub list: Vec<KLine>,
        #[serde(rename = "navigateFirstPage")]
        pub navigate_first_page: i32,
        #[serde(rename = "navigateLastPage")]
        pub mnavigate_last_pagesg: i32,
        #[serde(rename = "navigatePages")]
        pub navigate_pages: i32,
        #[serde(rename = "navigatepageNums")]
        pub navigatepage_nums: Vec<i32>,
        #[serde(rename = "nextPage")]
        pub next_page: i32,
        #[serde(rename = "pageNum")]
        pub page_num: i32,
        #[serde(rename = "pageSize")]
        pub page_size: i32,
        pub pages: i32,
        #[serde(rename = "prePage")]
        pub pre_page: i32,
        pub size: i32,
        #[serde(rename = "startRow")]
        pub start_row: i32,
        pub total: i32,
    }

    #[derive(Debug, Clone, Default, Deserialize, Serialize)]
    pub struct KLine {
        pub amount: f64,
        pub change: f64,
        #[serde(rename = "changeRate")]
        pub change_rate: f64,
        pub close: f64,
        pub date: String,
        pub high: f64,
        pub low: f64,
        pub open: f64,
        #[serde(rename = "preClose")]
        pub pre_close: f64,
        pub volume: f64,
    }
}

#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct IndustryList {
    #[serde(rename = "conceptFlag")]
    pub concept_flag: String,
    pub name: String,
    pub symbol: String,
}

#[derive(Debug, Clone, Default, Deserialize, Serialize)]
pub struct IndustryListResp {
    pub code: String,
    pub msg: String,
    pub body: Vec<IndustryList>,
}

#[derive(Debug, Clone)]
pub struct FiuApi {
    pub hs_host: String,
    pub hk_host: String,
    pub client: Client,
    pub headers: HeaderMap,
}

impl FiuApi {
    pub async fn new(hs_host: &String, hk_host: &String) -> FiuApi {
        let mut headers = HeaderMap::new();
        headers.insert("Content-Type", HeaderValue::from_static("application/json"));
        FiuApi {
            hs_host: hs_host.to_owned(),
            hk_host: hk_host.to_owned(),
            client: Client::new(),
            headers,
        }
    }

    pub async fn hs_post_stock_list(&self) -> Vec<String> {
        let url = format!("{}/v1/stock/list", self.hs_host);

        let values = [
            json!({"market": "SH", "type": SecurityType::Stock as i32}),
            json!({"market": "SH", "type": SecurityType::Fund as i32}),
            json!({"market": "SH", "type": SecurityType::Index as i32}),
            json!({"market": "SH", "type": SecurityType::BShare as i32}),
            json!({"market": "SZ", "type": SecurityType::Stock as i32}),
            json!({"market": "SZ", "type": SecurityType::Fund as i32}),
            json!({"market": "SZ", "type": SecurityType::Index as i32}),
            json!({"market": "SZ", "type": SecurityType::BShare as i32}),
        ];

        let mut stock_list: Vec<String> = Vec::new();
        for v in values.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;

            match ret {
                Ok(stock_resp) => {
                    if stock_resp.status() != 200 {
                        error!("post hs stock list err code: {:?}", stock_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hs_post_stock_list error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = stock_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hs::HsStockListResp>(resp).unwrap_or_default();
                    stock_list.extend(resp.body.iter().map(|x| x.symbol.clone()));
                }
                Err(err) => {
                    error!("post hs stock list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hs_post_stock_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hs stock list ok: {}", stock_list.len());
        stock_list
    }

    pub async fn hs_post_industry_list(&self) -> Vec<IndustryList> {
        let url = format!("{}/v1/industry/list", self.hs_host);

        let values = [json!({ "conceptFlag": "Y"}), json!({ "conceptFlag": "N"})];

        let mut industry_list: Vec<IndustryList> = Vec::new();
        for v in values.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(industry_resp) => {
                    if industry_resp.status() != 200 {
                        error!("post hs industry list err code: {:?}", industry_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hs_post_industry_list error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = industry_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<IndustryListResp>(resp).unwrap_or_default();
                    industry_list.extend(resp.body);
                }
                Err(err) => {
                    error!("post hs industry list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hs_post_industry_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hs industry list ok: {}", industry_list.len());
        industry_list
    }

    pub async fn hs_post_stock_basic_info(&self) -> Vec<hs::HsStockBasicInfo> {
        let stock_list = self.hs_post_stock_list().await;
        let list: Vec<Vec<String>> = stock_list.chunks(64).map(|x| x.into()).collect();

        let url = format!("{}/v1/stock/basic/info", self.hs_host);

        let mut hs_stock_infos = Vec::new();

        for v in list.iter() {
            let req = json!({
                "symbols": v
            });
            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(basic_resp) => {
                    if basic_resp.status() != 200 {
                        error!("post hs stock basic info err code: {:?}", basic_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hs_post_stock_basic_info error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = basic_resp.json().await.unwrap_or_default();
                    // info!("{:#?}", resp);
                    let mut resp = serde_json::from_value::<hs::HsStockBasicInfoResp>(resp).unwrap_or_default();
                    // 1	股票 2	ETF 4	指数 7	基金
                    resp.body.retain(|x| x.stock_type == 1 || x.stock_type == 2 || x.stock_type == 4 || x.stock_type == 7);
                    hs_stock_infos.extend(resp.body);
                }
                Err(err) => {
                    error!("post hs stock basic info err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hs_post_stock_basic_info error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hs stock basic info ok: {}", hs_stock_infos.len());
        hs_stock_infos
    }

    pub async fn hs_post_stock_define_info(&self) -> Vec<hs::HsStockDefine> {
        let stock_list = self.hs_post_stock_list().await;
        let list: Vec<Vec<String>> = stock_list.chunks(64).map(|x| x.into()).collect();

        let url = format!("{}/v1/stock/define", self.hs_host);

        let mut hs_stock_infos = Vec::new();

        for v in list.iter() {
            let req = json!({
                "symbols": v
            });
            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(define_resp) => {
                    if define_resp.status() != 200 {
                        error!("post hs stock define info err code: {:?}", define_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hs_post_stock_define_info error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = define_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hs::HsStockDefineResp>(resp).unwrap_or_default();
                    hs_stock_infos.extend(resp.body);
                }
                Err(err) => {
                    error!("post hs stock define info info err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hs_post_stock_define_info error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hs stock define info ok: {}", hs_stock_infos.len());
        hs_stock_infos
    }

    pub async fn hk_post_stock_list(&self) -> Vec<String> {
        let url = format!("{}/v3/stock/list", self.hk_host);

        let value = [
            json!({"market": "MAIN", "types": ["EQTY"]}), //港股主板
            json!({"market": "GEM", "types": ["EQTY"]}),  //港股创业板
        ];

        let mut stock_list: Vec<String> = Vec::new();
        for v in value.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(stock_resp) => {
                    if stock_resp.status() != 200 {
                        error!("post hk stock list code: {:?}", stock_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hk_post_stock_list error")).await;
                        }
                        return Vec::new();
                    }
                    // info!("{}", ret.status());
                    let resp: serde_json::Value = stock_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hk::HkStockListResp>(resp).unwrap_or_default();
                    stock_list.extend(resp.body.iter().map(|x| x.symbol.clone()));
                }
                Err(err) => {
                    error!("post hk stock list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_post_stock_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hk stock list ok: {}", stock_list.len());
        stock_list
    }

    pub async fn hk_post_industry_list(&self) -> Vec<IndustryList> {
        let url = format!("{}/v3/industry/list", self.hk_host);

        let values = [json!({ "conceptFlag": "Y"}), json!({ "conceptFlag": "N"})];

        let mut industry_list: Vec<IndustryList> = Vec::new();
        for v in values.iter() {
            let body = serde_json::to_vec(&v).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(industry_resp) => {
                    if industry_resp.status() != 200 {
                        error!("post hk industry list code: {:?}", industry_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hk_post_industry_list error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = industry_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<IndustryListResp>(resp).unwrap_or_default();
                    industry_list.extend(resp.body);
                }
                Err(err) => {
                    error!("post hk industry list err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_post_industry_list error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hk industry list ok: {}", industry_list.len());
        industry_list
    }

    pub async fn hk_stock_status(&self) -> Vec<hk::HkStockStatus> {
        let url = format!("{}/v3/stock/status", self.hk_host);

        let ret = self.client.post(url).headers(self.headers.clone()).send().await;
        // .unwrap();

        match ret {
            Ok(status_resp) => {
                if status_resp.status() != 200 {
                    error!("post hk stock status list code: {:?}", status_resp.status());
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_stock_status error")).await;
                    }
                    return Vec::new();
                }
                let resp: serde_json::Value = status_resp.json().await.unwrap_or_default();
                let resp = serde_json::from_value::<hk::HkStockStatusResp>(resp).unwrap_or_default();
                info!("post hk stock status ok: {:?}", resp.body.len());
                return resp.body;
            }
            Err(err) => {
                error!("post hk stock status list err: {:?}", err);
                if let Ok(client) = LogClient::get() {
                    client.push_error(&format!("hk_stock_status error")).await;
                }
                return Vec::new();
            }
        }
    }

    pub async fn hk_post_stock_basic_info(&self) -> Vec<hk::HkStockBasicInfo> {
        let symbols: Vec<String> = self.hk_post_stock_list().await;
        let list: Vec<Vec<String>> = symbols.chunks(64).map(|x| x.into()).collect();

        let url = format!("{}/v3/stock/basic/info", self.hk_host);

        let mut hk_stock_infos = Vec::new();
        for v in list.iter() {
            let req = json!({
                "symbols": v
            });

            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(basic_resp) => {
                    if basic_resp.status() != 200 {
                        error!("post hk stock basic info code: {:?}", basic_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hk_post_stock_basic_info error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = basic_resp.json().await.unwrap_or_default();
                    // info!("{:#?}", resp);
                    let mut resp = serde_json::from_value::<hk::HkStockBasicInfoResp>(resp).unwrap_or_default();
                    // 1	股票 2	ETF 4	指数 7	基金
                    resp.body.retain(|x| x.stock_type == 1 || x.stock_type == 2 || x.stock_type == 4 || x.stock_type == 7);
                    hk_stock_infos.extend(resp.body);
                }
                Err(err) => {
                    error!("post hk stock basic info err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_post_stock_basic_info error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hk stock basic info ok: {}", hk_stock_infos.len());
        hk_stock_infos
    }

    pub async fn hk_post_stock_define(&self) -> Vec<hk::HkStockDefine> {
        let symbols: Vec<String> = self.hk_post_stock_list().await;
        let list: Vec<Vec<String>> = symbols.chunks(64).map(|x| x.into()).collect();

        let url = format!("{}/v3/stock/define", self.hk_host);

        let mut hk_stock_infos = Vec::new();
        for v in list.iter() {
            let req = json!({
                "symbols": v
            });

            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(define_resp) => {
                    if define_resp.status() != 200 {
                        error!("post hk stock define info code: {:?}", define_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("hk_post_stock_define error")).await;
                        }
                        return Vec::new();
                    }
                    let resp: serde_json::Value = define_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hk::HkStockDefineResp>(resp).unwrap_or_default();
                    hk_stock_infos.extend(resp.body);
                }
                Err(err) => {
                    error!("post hk stock define info err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("hk_post_stock_define error")).await;
                    }
                    return Vec::new();
                }
            }
        }

        info!("post hk stock define info ok: {}", hk_stock_infos.len());
        hk_stock_infos
    }

    pub async fn post_hs_hq(&self) {
        let url = format!("{}/v1/stock/quote", self.hs_host);
        let req = json!({
            "fields": ["snapshot","order"],
            "symbols": ["000001.sz"],
            "timeMode": 0
        });

        let body = serde_json::to_vec(&req).unwrap();
        let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await.unwrap();

        let resp: serde_json::Value = ret.json().await.unwrap_or_default();
        info!("{:#?}", resp);
        let resp = serde_json::from_value::<hs::HsStockQuoteResp>(resp).unwrap_or_default();

        info!("{:#?}", resp);
    }

    pub async fn post_hk_hq(&self) {
        let url = format!("{}/v3/stock/quote", self.hk_host);
        let req = json!({
            "fields": ["snapshot","order"],
            "symbols": ["03690.hk"],
            "timeMode": 0
        });

        let body = serde_json::to_vec(&req).unwrap();
        let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await.unwrap();

        let resp: serde_json::Value = ret.json().await.unwrap_or_default();
        info!("{:#?}", resp);
        let resp = serde_json::from_value::<hk::HkStockQuoteResp>(resp).unwrap_or_default();

        info!("{:#?}", resp);
    }

    async fn post_hk_daykline(&self, stock: &Vec<StockInfo>, date: &String, page_size: i32, kline_type: i32) -> Vec<CassandraKLineData> {
        info!("date: {}, page_size: {}, kline_type: {}", date, page_size, kline_type);
        let url = format!("{}/v3/chart/kline/list", self.hk_host);

        let mut hk_kline = Vec::new();
        for v in stock.iter() {
            let req = json!({
                "candleMode": 0,
                "date": date,
                "pageNum": 1,
                "pageSize": page_size,
                "symbol": v.outer_code,
                "timeMode": 0,
                "type": kline_type
            });

            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(basic_resp) => {
                    if basic_resp.status() != 200 {
                        error!("post hk k_linecode: {:?}", basic_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("post_hk_daykline error")).await;
                        }
                        continue;
                    }
                    let resp: serde_json::Value = basic_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hk::HkKLineResp>(resp).unwrap_or_default();
                    if resp.body.is_none() {
                        continue;
                    }
                    for x in resp.body.unwrap().list.iter() {
                        let kline = CassandraKLineData {
                            peroid: { get_kline_type(kline_type) },
                            vc_code: v.inner_code.to_owned(),
                            l_update_time: {
                                let st = utility::timeutil::build_naive_date(&x.date);
                                format!("{:04}{:02}{:02}160000", &st.year(), &st.month(), &st.day())
                            },
                            en_close_price: x.close,
                            en_high_price: x.high,
                            en_last_price: x.close,
                            en_low_price: x.low,
                            en_open_price: x.open,
                            l_volume: x.volume,
                        };
                        hk_kline.push(kline);
                    }
                }
                Err(err) => {
                    error!("post hk k_line err: {:?}", err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("post_hk_daykline error")).await;
                    }
                    continue;
                }
            }
        }
        hk_kline
    }

    async fn post_hs_daykline(&self, stock: &Vec<StockInfo>, date: &String, page_size: i32, kline_type: i32) -> Vec<CassandraKLineData> {
        info!("date: {}, page_size: {}, kline_type: {}", date, page_size, kline_type);
        let url = format!("{}/v1/chart/kline/list", self.hs_host);

        let mut hs_kline = Vec::new();
        for v in stock.iter() {
            let req = json!({
                "candleMode": 0,
                "date": date,
                "pageNum": 1,
                "pageSize": page_size,
                "symbol": v.outer_code,
                "timeMode": 0,
                "type": kline_type
            });

            let body = serde_json::to_vec(&req).unwrap();
            let ret = self.client.post(url.clone()).headers(self.headers.clone()).body(body).send().await;
            // .unwrap();

            match ret {
                Ok(basic_resp) => {
                    if basic_resp.status() != 200 {
                        error!("post hs k_line code: {:?}", basic_resp.status());
                        if let Ok(client) = LogClient::get() {
                            client.push_error(&format!("post_hs_daykline error")).await;
                        }
                        // return Vec::new();
                        continue;
                    }
                    let resp: serde_json::Value = basic_resp.json().await.unwrap_or_default();
                    let resp = serde_json::from_value::<hk::HkKLineResp>(resp).unwrap_or_default();
                    if resp.body.is_none() {
                        continue;
                    }
                    for x in resp.body.unwrap().list.iter() {
                        let kline = CassandraKLineData {
                            peroid: { get_kline_type(kline_type) },
                            vc_code: v.inner_code.clone(),
                            l_update_time: {
                                let st = utility::timeutil::build_naive_date(&x.date);
                                format!("{:04}{:02}{:02}150000", &st.year(), &st.month(), &st.day())
                            },
                            en_close_price: x.close,
                            en_high_price: x.high,
                            en_last_price: x.close,
                            en_low_price: x.low,
                            en_open_price: x.open,
                            l_volume: {
                                if v.stock_type == 4 {
                                    //指数
                                    x.volume
                                } else {
                                    x.volume / 100.0
                                }
                            },
                        };
                        // if v.inner_code == "000001_XSHG" {
                        //     info!("kline: {:#?}", kline);
                        // }
                        hs_kline.push(kline);
                    }
                }
                Err(err) => {
                    error!("post hs k_line err: {}, {:?}", v.inner_code, err);
                    if let Ok(client) = LogClient::get() {
                        client.push_error(&format!("post_hs_daykline error")).await;
                    }
                    continue;
                }
            }
        }
        hs_kline
    }
}

pub async fn convert_local_stock_info(hsbasic: &Vec<hs::HsStockBasicInfo>, hkbasic: &Vec<hk::HkStockBasicInfo>) -> Vec<StockInfo> {
    let mut stockinfo = Vec::new();
    for v in hsbasic.iter() {
        if v.symbol.is_null() || v.market.is_null() || v.security_status.is_null() || v.name.is_null() {
            continue;
        }
        let symbol = v.symbol.as_str().unwrap_or_default().to_owned();
        let market = v.market.as_i64().unwrap_or_default();
        let inner_code = get_inner_code(symbol.split(".").collect::<Vec<&str>>()[0].to_owned(), market);
        if inner_code.is_empty() {
            continue;
        }

        let stock_info = StockInfo {
            name: v.name.as_str().unwrap_or_default().to_owned(),
            inner_code,
            outer_code: symbol.to_owned(),
            market_id: if market == Market::SH as i64 { 101 } else { 102 },
            lot_size: v.lot_size.as_i64().unwrap_or(100) as i32,
            currency: v.currency.as_str().unwrap_or("CNY").to_owned(),
            security_status: v.security_status.as_i64().unwrap_or_default() as i32,
            stock_type: v.stock_type.as_i64().unwrap_or_default() as i32,
            plate_codes: {
                let codes = v.plate_codes.as_array().unwrap();
                codes
                    .into_iter()
                    .filter_map(|val| match val {
                        Value::String(s) => Some(s.clone()),
                        _ => None,
                    })
                    .collect()
            },
            inner_stock_type: {
                let board = v.board.as_i64().unwrap_or(0);
                match board {
                    1 => 1, //主板
                    3 => 4, //创业板
                    6 => 5, //科创板
                    // 7 => 8, //深交所退市整理板
                    // 8 => 8, //上交所风险警示板
                    _ => {
                        if v.security_status.as_i64().unwrap_or_default() as i32 == 7 {
                            //ETF
                            6
                        } else {
                            0
                        }
                    }
                }
            },
        };
        stockinfo.push(stock_info);
    }

    for v in hkbasic.iter() {
        if v.symbol.is_null() || v.market.is_null() || v.security_status.is_null() || v.name.is_null() {
            continue;
        }

        let symbol = v.symbol.as_str().unwrap_or_default().to_owned();
        let inner_code = get_inner_code(symbol.split(".").collect::<Vec<&str>>()[0].to_owned(), 2);
        if inner_code.is_empty() {
            continue;
        }

        let stock_info = StockInfo {
            name: v.name.as_str().unwrap_or_default().to_owned(),
            inner_code,
            outer_code: symbol.to_owned(),
            market_id: 103,
            lot_size: v.lot_size.as_i64().unwrap_or(100) as i32,
            currency: v.currency.as_str().unwrap_or("HKD").to_owned(),
            security_status: v.security_status.as_i64().unwrap_or_default() as i32,
            stock_type: v.stock_type.as_i64().unwrap_or_default() as i32,
            plate_codes: {
                let codes = v.plate_codes.as_array().unwrap();
                codes
                    .into_iter()
                    .filter_map(|val| match val {
                        Value::String(s) => Some(s.clone()),
                        _ => None,
                    })
                    .collect()
            },
            inner_stock_type: {
                let board = v.market.as_str().unwrap_or("");
                match board {
                    "MAIN" => 2, //主板 板块内码：1002_HK
                    "GEM" => 4,  //创业板 板块内码：1003_HK
                    // "ETS" => 5,//扩充交易证券
                    // "NASD" => 6,//NASDAQ AMX市场
                    _ => {
                        if v.security_status.as_i64().unwrap_or_default() as i32 == 7 {
                            ////ETF
                            6
                        } else {
                            0
                        }
                    }
                }
            },
        };
        stockinfo.push(stock_info);
    }

    stockinfo
}

pub fn get_kline_type(kline_type: i32) -> i32 {
    match kline_type {
        0 => 1440,
        5 => 1,
        6 => 5,
        8 => 30,
        9 => 60,
        _ => 0,
    }
}
