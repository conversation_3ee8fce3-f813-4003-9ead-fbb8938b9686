use anyhow::Result;
use chrono::prelude::*;
use dashmap::DashMap;
use std::{collections::HashMap, sync::Arc};
use tokio::sync::{mpsc::Sender, RwLock};
use tracing::{error, info};

use protoes::hqmsg::YsHqInfo;
use utility::timeutil::{build_naive_date_time, from_timestamp_to_naive_date_time};

use super::common::{KLineData, KLineType};
use crate::{commonutil::commonutil::CommonUtil, server::controller::TickCenterController};

const DAY_TOTAL_MINUTES: i64 = 1440;

//每个合约的最新处理时间，用于数据补全
//第一个为行情时间分钟,第二个为行情服务器时间戳
#[derive(Clone, Debug, Default)]
#[allow(dead_code)]
pub struct TimePair {
    pub minute: i64,
    pub timestamp: i64,
}
pub enum KlinePeriod {
    Min1 = 1,
    Min5 = 5,
    Min10 = 10,
    Min15 = 15,
    Min30 = 30,
    Min60 = 60,
    Day = 1440,
    Week = 10080,
    Month = 43200,
}
#[derive(Clone, Debug)]
pub struct KLineService {
    // 每周期缓存
    // cache: Arc<RwLock<HashMap<KlinePeriod, HashMap<String, KLineData>>>>,
    kline_1: Arc<DashMap<String, KLineData>>,
    kline_5: Arc<DashMap<String, KLineData>>,
    kline_10: Arc<DashMap<String, KLineData>>,
    kline_30: Arc<DashMap<String, KLineData>>,
    kline_60: Arc<DashMap<String, KLineData>>,
    kline_day: Arc<DashMap<String, KLineData>>,
    tx_kline: Sender<KLineData>,
    pub contract_fill: Arc<DashMap<String, TimePair>>, //补数用
}

impl KLineService {
    #[allow(dead_code)]
    pub fn new(tx_kline: Sender<KLineData>) -> Self {
        KLineService {
            // cache: Arc::new(RwLock::new(HashMap::new())),
            kline_1: Arc::new(DashMap::new()),
            kline_5: Arc::new(DashMap::new()),
            kline_10: Arc::new(DashMap::new()),
            kline_30: Arc::new(DashMap::new()),
            kline_60: Arc::new(DashMap::new()),
            kline_day: Arc::new(DashMap::new()),
            contract_fill: Arc::new(DashMap::new()),
            tx_kline,
        }
    }
    pub async fn check_kline_is_none(&self, kline_type: KLineType, key: &String) -> bool {
        match kline_type {
            KLineType::Min1 => self.kline_1.get(key).is_none(),
            KLineType::Min5 => self.kline_5.get(key).is_none(),
            KLineType::Min10 => self.kline_10.get(key).is_none(),
            KLineType::Min30 => self.kline_30.get(key).is_none(),
            KLineType::Min60 => self.kline_60.get(key).is_none(),
            KLineType::Day => self.kline_day.get(key).is_none(),
        }
    }

    //插入新值到map中, prev_minutes根据tick时间来计算或直接给出
    async fn insert_kline_data(&self, tick: &YsHqInfo, kline_type: KLineType, common_util: &CommonUtil, prev_minutes: i32) -> Result<()> {
        let mut kline_data = KLineData::new().await;
        let curr_tick_time = build_naive_date_time(&tick.tapidtstamp);

        kline_data.tick_time = tick.tapidtstamp.clone();
        kline_data.stock_code = tick.contract_no1.clone();
        kline_data.period = kline_type as i32;

        kline_data.pre_close_price = tick.q_pre_closing_price; //昨收价
        kline_data.close_price = tick.q_last_price; //收盘价
        kline_data.last_price = tick.q_last_price; //最新价
        kline_data.average_price = tick.q_average_price; //均价

        // kline_data.open_price = tick.q_opening_price; //开盘价
        // kline_data.high_price = tick.q_high_price; //最高价
        // kline_data.low_price = tick.q_low_price; //最低价
        kline_data.current_period_volume += tick.q_last_qty;
        kline_data.current_period_turnover += tick.q_last_turnover;

        //交易开始分钟数
        let ret_begin_time = common_util.get_begin_time(&tick.exchange_id).await;
        if ret_begin_time.as_ref().is_err() {
            error!("{:?}", ret_begin_time);
            return Err(anyhow!("{:?}", ret_begin_time));
        }
        let begin_time = ret_begin_time.unwrap();
        kline_data.begin_minutes = (begin_time.hour() * 60 + begin_time.minute()) as i32;

        //交易结束分钟数
        let ret_end_time = common_util.get_close_time(&tick.exchange_id).await;
        if ret_end_time.as_ref().is_err() {
            error!("{:?}", &ret_end_time);
            return Err(anyhow!("{:?}", ret_end_time));
        }
        let end_time = ret_end_time.unwrap();
        kline_data.end_minutes = (end_time.hour() * 60 + end_time.minute()) as i32;

        if prev_minutes == -1 {
            //日K 或者9:30以后才有的行情
            if kline_type as i32 == 1440 {
                kline_data.prev_minutes = kline_data.begin_minutes; //最新已形成的周期
            } else {
                //09:30:00 or 13:00:00
                let ret_time = common_util.get_interval_begin_time(&tick.tapidtstamp[11..19].to_string(), &tick.exchange_id).await;
                if ret_time.as_ref().is_err() {
                    error!("{:?}", ret_time);
                    return Err(anyhow!("{:?}", ret_time));
                }
                let time = ret_time.unwrap();
                let mut begin_min = (time.hour() * 60 + time.minute()) as i32;
                if (kline_type as i32 == 60) && (begin_min == 13 * 60) && tick.exchange_id == "XHKG" {
                    begin_min -= 30;
                }
                // info!("{}, {}", curr_tick_time, begin_min);
                //距离上一个周期的分钟数，如：10分钟K线，第15分钟此值为5
                let interval = ((curr_tick_time.hour() * 60 + curr_tick_time.minute()) as i32 - begin_min) % kline_type as i32;
                // info!("[insert_kline_data] {} kline 距离上一个周期的分钟数: {}", kline_data.period, interval);
                //上一个保存周期
                kline_data.prev_minutes = (curr_tick_time.hour() * 60 + curr_tick_time.minute()) as i32 - interval;
            }
        } else {
            kline_data.prev_minutes = prev_minutes;
        }

        if kline_type as i32 == 1440 {
            if tick.q_opening_price > 0.00 {
                //开盘价
                kline_data.open_price = tick.q_opening_price;
            } else {
                kline_data.open_price = tick.q_last_price;
            }
            if tick.q_high_price > 0.00 {
                //最高价
                kline_data.high_price = tick.q_high_price;
            } else {
                kline_data.high_price = tick.q_last_price;
            }
            if tick.q_low_price > 0.00 {
                //最低价
                kline_data.low_price = tick.q_low_price
            } else {
                kline_data.low_price = tick.q_last_price;
            }
        } else {
            //不是日K
            kline_data.open_price = tick.q_last_price;
            kline_data.high_price = tick.q_last_price;
            kline_data.low_price = tick.q_last_price;
        }

        if tick.q_last_qty == -1 as f64 {
            kline_data.is_fake = true;
            if kline_type as i64 != 1400 {
                kline_data.current_period_volume += 1.0;
            }
        } else {
            kline_data.is_fake = false;
        }

        match kline_type {
            KLineType::Min1 => self.kline_1.insert(kline_data.stock_code.clone(), kline_data.to_owned()),
            KLineType::Min5 => self.kline_5.insert(kline_data.stock_code.clone(), kline_data.to_owned()),
            KLineType::Min10 => self.kline_10.insert(kline_data.stock_code.clone(), kline_data.to_owned()),
            KLineType::Min30 => self.kline_30.insert(kline_data.stock_code.clone(), kline_data.to_owned()),
            KLineType::Min60 => self.kline_60.insert(kline_data.stock_code.clone(), kline_data.to_owned()),
            KLineType::Day => self.kline_day.insert(kline_data.stock_code.clone(), kline_data.to_owned()),
        };
        Ok(())
    }

    pub async fn updata_open_call_auction_kline_data(&self, kline_type: &KLineType, tick: &YsHqInfo) {
        // 获取并 clone 数据
        let mut kline_data = {
            let kline = match kline_type {
                KLineType::Min1 => self.kline_1.get(&tick.contract_no1).unwrap(),
                KLineType::Min5 => self.kline_5.get(&tick.contract_no1).unwrap(),
                KLineType::Min10 => self.kline_10.get(&tick.contract_no1).unwrap(),
                KLineType::Min30 => self.kline_30.get(&tick.contract_no1).unwrap(),
                KLineType::Min60 => self.kline_60.get(&tick.contract_no1).unwrap(),
                KLineType::Day => self.kline_day.get(&tick.contract_no1).unwrap(),
            };
            kline.value().clone()
        };

        // 修改数据
        kline_data.tick_time = tick.tapidtstamp.clone();
        kline_data.close_price = tick.q_last_price;
        kline_data.last_price = tick.q_last_price;
        kline_data.average_price = tick.q_average_price;
        kline_data.pre_close_price = tick.q_pre_closing_price; //昨收价

        // kline_data.open_price = tick.q_opening_price; //开盘价
        // kline_data.high_price = tick.q_high_price; //最高价
        // kline_data.low_price = tick.q_low_price; //最低价

        kline_data.current_period_volume += tick.q_last_qty;
        kline_data.current_period_turnover += tick.q_last_turnover;

        if kline_data.current_period_volume == 0 as f64 {
            //成交量为0
            kline_data.open_price = tick.q_last_price;
            kline_data.high_price = tick.q_last_price;
            kline_data.low_price = tick.q_last_price;
        } else {
            if kline_data.high_price < tick.q_last_price {
                //K线 最高阶 < tick 最新价,则更新
                kline_data.high_price = tick.q_last_price;
            }
            if kline_data.low_price > tick.q_last_price {
                //K线 最低价 > tick 最新价, 更新
                kline_data.low_price = tick.q_last_price;
            }
        }

        // 写回 DashMap
        match kline_type {
            KLineType::Min1 => self.kline_1.insert(tick.contract_no1.clone(), kline_data),
            KLineType::Min5 => self.kline_5.insert(tick.contract_no1.clone(), kline_data),
            KLineType::Min10 => self.kline_10.insert(tick.contract_no1.clone(), kline_data),
            KLineType::Min30 => self.kline_30.insert(tick.contract_no1.clone(), kline_data),
            KLineType::Min60 => self.kline_60.insert(tick.contract_no1.clone(), kline_data),
            KLineType::Day => self.kline_day.insert(tick.contract_no1.clone(), kline_data),
        };
    }

    //更新K线过程
    async fn update_kline_data(&self, kline_data: &mut KLineData, tick: &YsHqInfo, is_same_period: bool) {
        // info!("update {} {}", &tick.contract_no1, &tick.tapidtstamp);
        kline_data.tick_time = tick.tapidtstamp.clone();
        kline_data.close_price = tick.q_last_price; //收盘价
        kline_data.last_price = tick.q_last_price; //最新价
        kline_data.average_price = tick.q_average_price; //均价
        kline_data.pre_close_price = tick.q_pre_closing_price; //昨收价

        // kline_data.open_price = tick.q_opening_price; //开盘价
        // kline_data.high_price = tick.q_high_price; //最高价
        // kline_data.low_price = tick.q_low_price; //最低价

        kline_data.current_period_volume += tick.q_last_qty;
        kline_data.current_period_turnover += tick.q_last_turnover;

        //同周期且不是1分钟, 是每天刚开始时候
        if is_same_period && (kline_data.period != 1) && (kline_data.prev_minutes == kline_data.begin_minutes) {
            if kline_data.is_fake {
                kline_data.open_price = tick.q_last_price;
                kline_data.high_price = tick.q_last_price;
                kline_data.low_price = tick.q_last_price;
                kline_data.is_fake = false;
            }
            if tick.q_opening_price > 0.00 {
                kline_data.open_price = tick.q_opening_price;
            }
            if tick.q_high_price > 0.00 {
                kline_data.high_price = tick.q_high_price;
            } else if kline_data.high_price < tick.q_last_price {
                kline_data.high_price = tick.q_last_price;
            }

            if tick.q_low_price > 0.00 {
                kline_data.low_price = tick.q_low_price;
            } else if kline_data.low_price > tick.q_last_price {
                kline_data.low_price = tick.q_last_price;
            }

            return;
        }

        if is_same_period {
            if kline_data.is_fake {
                kline_data.open_price = tick.q_last_price;
                kline_data.high_price = tick.q_last_price;
                kline_data.low_price = tick.q_last_price;
                kline_data.is_fake = false;
            } else {
                if kline_data.high_price < tick.q_last_price {
                    kline_data.high_price = tick.q_last_price;
                }
                if kline_data.low_price > tick.q_last_price {
                    kline_data.low_price = tick.q_last_price;
                }
            }
        } else {
            //跨周期了, 全部更新
            kline_data.prev_minutes = (kline_data.prev_minutes + kline_data.period) % 1440;

            kline_data.open_price = tick.q_last_price;
            kline_data.high_price = tick.q_last_price;
            kline_data.low_price = tick.q_last_price;
            if tick.q_last_qty == -1 as f64 {
                kline_data.is_fake = true;
                kline_data.current_period_volume += 1 as f64;
            } else {
                kline_data.is_fake = false;
            }
        }
    }

    #[allow(dead_code)]
    pub async fn insert_or_update(&self, tick: &YsHqInfo, kline_type: KLineType, common_util: &CommonUtil) {
        if self.check_kline_is_none(kline_type, &tick.contract_no1).await {
            if kline_type as i64 == 1440 {
                self.insert_kline_data(tick, kline_type, common_util, -1).await.unwrap();
            } else {
                if kline_type as i64 == 1 {
                    // yyyy-mm-dd 09:29:59
                    self.insert_kline_data(tick, kline_type, common_util, 569).await.unwrap();
                } else {
                    // yyyy-mm-dd 09:30:00
                    self.insert_kline_data(tick, kline_type, common_util, 570).await.unwrap();
                }
            }
        } else {
            self.updata_open_call_auction_kline_data(&kline_type, tick).await;
        }
    }

    // 对TICK数据分段处理
    #[allow(dead_code)]
    pub async fn deal_tick_range(&self, tick: &YsHqInfo, kline_type: KLineType, center: &TickCenterController) -> Result<()> {
        // 1. 先获取 kline_data 的副本或必要字段
        let (mut kline_data, exists) = match kline_type {
            KLineType::Min1 => self.kline_1.get(&tick.contract_no1),
            KLineType::Min5 => self.kline_5.get(&tick.contract_no1),
            KLineType::Min10 => self.kline_10.get(&tick.contract_no1),
            KLineType::Min30 => self.kline_30.get(&tick.contract_no1),
            KLineType::Min60 => self.kline_60.get(&tick.contract_no1),
            KLineType::Day => self.kline_day.get(&tick.contract_no1),
        }
        .map(|kline| (kline.value().clone(), true))
        .unwrap_or_default();

        if exists {
            // 2. 在锁外处理业务逻辑和 await
            // info!("prev tick: {} {}", &kline.stock_code, &kline.tick_time);
            // info!("current tick: {} {}", &tick.contract_no1, &tick.tapidtstamp);
            let naive_time = build_naive_date_time(&tick.tapidtstamp);
            //当前tick分钟
            let current_tick_min = (naive_time.hour() * 60 + naive_time.minute()) as i32;
            //前一条数据时间
            // let prev_tick_time = kline_data.tick_time.clone();
            // let ret = build_naive_date_time(&prev_tick_time);
            // if ret.as_ref().is_err() {
            //     error!("error: {:?}", ret);
            //     return Err(anyhow!("error: {:?}", ret));
            // }
            // let prev_time = ret.unwrap();
            // let prev_tick_min = (prev_time.hour() * 60 + prev_time.minute()) as i32;
            // info!("current tick data minutes: {}, prev_tick data minutes: {}", current_tick_min, prev_tick_min);

            if kline_data.period == 1440 {
                self.update_kline_data(&mut kline_data, tick, true).await;
            } else {
                //当前与上一周期的时间间隔
                let mut diff_minutes = if current_tick_min > kline_data.prev_minutes { current_tick_min - kline_data.prev_minutes } else { 0 };

                if diff_minutes < kline_data.period {
                    //[0,period)本周期内, 更新
                    self.update_kline_data(&mut kline_data, tick, true).await;
                } else if diff_minutes == kline_data.period {
                    //跨1、5、10.。。。分钟了，整理上一周期的数据，存入K线数据中存入Cassandra
                    if let Err(err) = self.tx_kline.send(kline_data.to_owned()).await {
                        error!("send error: {:?}", err);
                        return Err(anyhow!("send error: {:?}", err));
                    }
                    kline_data.current_period_turnover = 0.0;
                    kline_data.current_period_volume = 0.0;
                    self.update_kline_data(&mut kline_data, tick, false).await;
                } else {
                    //diff_minutes > kline_data.period
                    //如果相差N(N>1)个周期, 启用补数据逻辑, 最多补到tick_minutes
                    info!(
                        "[deal_tick_range] {} 启用补数逻辑: 上一周期: {}. 时间间隔:{}, {} kline",
                        kline_data.stock_code, kline_data.prev_minutes, diff_minutes, kline_data.period
                    );
                    let mut min = kline_data.prev_minutes + kline_data.period;
                    while min < current_tick_min + 1 {
                        if let Err(err) = self.deal_fill(&mut kline_data, center).await {
                            return Err(anyhow!("{:?}", err));
                        }
                        //午休时间没有行情时
                        // prev tick: 00819_XHKG 2023-08-16 11:59:38.561
                        // current tick: 00819_XHKG 2023-08-16 13:00:00.785
                        // if min == 720  && (tick.exchange_id == "XHKG" || tick.exchange_id == "HK") {
                        //     kline_data.prev_minutes = 780;
                        //     kline_data.tick_time = tick.tapidtstamp.clone();
                        //     if kline_data.period == 60 {
                        //         kline_data.prev_minutes -= 30;
                        //     }
                        //     min = kline_data.prev_minutes + kline_data.period;
                        //     continue
                        // }
                        min += kline_data.period;
                        diff_minutes -= kline_data.period;
                    }
                    self.update_kline_data(&mut kline_data, tick, true).await;
                }
            }
            // 3. 业务处理完后再写回 DashMap
            match kline_type {
                KLineType::Min1 => self.kline_1.insert(tick.contract_no1.clone(), kline_data),
                KLineType::Min5 => self.kline_5.insert(tick.contract_no1.clone(), kline_data),
                KLineType::Min10 => self.kline_10.insert(tick.contract_no1.clone(), kline_data),
                KLineType::Min30 => self.kline_30.insert(tick.contract_no1.clone(), kline_data),
                KLineType::Min60 => self.kline_60.insert(tick.contract_no1.clone(), kline_data),
                KLineType::Day => self.kline_day.insert(tick.contract_no1.clone(), kline_data),
            };
        } else {
            self.insert_kline_data(tick, kline_type, &center.common_util, -1).await.unwrap();
        }
        Ok(())
    }

    pub async fn deal_fill(&self, kline_data: &mut KLineData, center: &TickCenterController) -> Result<()> {
        let tt = build_naive_date_time(&kline_data.tick_time);

        let tt_fake_tick = tt.and_utc().timestamp() + (60 * kline_data.period) as i64; //下一周期时间
        let mut st_fake_tick = from_timestamp_to_naive_date_time(tt_fake_tick);

        if let Some(mut fake_tick) = center.tick_ctl.query_tick(&kline_data.stock_code).await {
            //查询最近一条数据
            //补的tick 的交易量为0
            fake_tick.q_last_qty = 0.00;
            fake_tick.q_last_turnover = 0.00;
            // fake_tick.tapidtstamp = format!("{}.000", st_fake_tick);
            st_fake_tick = st_fake_tick.with_second(0).unwrap(); //更改秒数为0
            fake_tick.tapidtstamp = format!("{}", st_fake_tick);
            // fake_tick.tapidtstamp = format!("{:04}-{:02}-{:02} {:02}:{:02}:00.000", st_fake_tick.year(), st_fake_tick.month(), st_fake_tick.day(), st_fake_tick.hour(), st_fake_tick.minute());
            // info!("补数: {} {} prev {:02}:{:02}:00", &fake_tick.contract_no1, &fake_tick.tapidtstamp, kline_data.prev_minutes/60, kline_data.prev_minutes%60);

            // info!("保存上一周期：{:#?}", &kline_data);//上一周期的先保存
            if let Err(err) = self.tx_kline.send(kline_data.to_owned()).await {
                error!("send error: {:?}", err);
                return Err(anyhow!("send error: {:?}", err));
            }
            fake_tick.q_last_qty = -1 as f64; //设置为-1 标识此条tick为伪造的
            kline_data.current_period_turnover = 0.0;
            kline_data.current_period_volume = 0.0;
            // info!("补发假的一条tick：{:?}", &fake_tick);
            info!("===================");
            self.update_kline_data(kline_data, &fake_tick, false).await;
            info!("===================");
        }
        Ok(())
    }

    //补全K线数据
    #[allow(dead_code)]
    pub async fn fill_kline_data(&self, local_current_minutes: i64, center: &TickCenterController) -> Result<()> {
        // info!("fill_kline_data start, fill size {}", self.contract_fill.len());
        // 记录需要删除的合约代码
        let mut del_codes = Vec::new();
        // 遍历所有合约
        let exchange_contract = center.exchange_contract.clone();
        for val in exchange_contract.iter() {
            let stock_code = val.key();
            let exchange = val.value();
            // 先获取 timepair 副本，避免长时间持有 DashMap 锁
            let (mut timepair, exists) = self.contract_fill.get(stock_code).map(|v| (v.value().clone(), true)).unwrap_or_default();
            if !exists {
                continue;
            }

            let mut local_deal_minutes = timepair.minute;

            // 补数据主循环
            //如果本地处理分钟小于本地当前分钟, 本地处理时间一分一分的累加, 并判断是否补分时K线
            //contract_fill中本地处理时间决定补多少个，tick时间决定补哪些时间的
            while local_deal_minutes < local_current_minutes {
                info!("{} 处理分钟: {}, 当前: {}", stock_code, local_deal_minutes, local_current_minutes);
                local_deal_minutes = (timepair.minute + 1) % DAY_TOTAL_MINUTES;
                //加1分钟,如午休,此时为11:29的时间戳,改为11:30
                let tt = timepair.timestamp + 60;
                timepair.minute = local_deal_minutes;
                timepair.timestamp = tt;
                //相当于来了一条tick_time的tick, 此时tick_time已加了一分钟
                let mut tick_tm = from_timestamp_to_naive_date_time(tt).with_second(0).unwrap();
                // info!("fill_kline_data {} falsify tick {}", stock_code, tick_tm);
                // 检查 tick 时间
                let (_, mut flag) = center.common_util.check_hqtick_time(&tick_tm.time().to_string(), exchange).await;

                let delay = match exchange.as_str() {
                    "XHKG" | "HK" => 10,
                    "XASE" | "XNYS" | "XNAS" | "US" => 15,
                    _ => 0,
                };
                if flag == 3 && (local_deal_minutes < ((tick_tm.hour() * 60 + tick_tm.minute() + delay) % 1440).into()) && matches!(exchange.as_str(), "XHKG" | "HK" | "XASE" | "XNYS" | "XNAS" | "US") {
                    //闭市
                    //HK 16:25:00之前不补 US 04:30:00之前不补 一般都是 16:15 04:15 触发这里
                    //16:24 04:30
                    //15:59:59 03:59:59
                    timepair.minute = local_deal_minutes + delay as i64 - 1;
                    timepair.timestamp = tick_tm.and_utc().timestamp() - 1;
                    info!("fill_kline_data skip, {} waiting for close time reach", stock_code);
                    continue;
                }
                if matches!(exchange.as_str(), "XASE" | "XNYS" | "XNAS" | "US") {
                    if flag != 3 {
                        // 不是闭市时间
                        //假设来了一条闭市时间的tick
                        let tick_time = if let Some(tick) = center.tick_ctl.query_tick(stock_code).await {
                            tick.tapidtstamp
                        } else {
                            info!("fill_kline_data {} has no previous tick, do not fill", stock_code);
                            break;
                        };
                        let ret = center.common_util.get_close_time(exchange).await;
                        if let Err(e) = ret {
                            return Err(anyhow!("{:?}", e));
                        }
                        let close_time = format!("{}", ret.unwrap());
                        let naive_date_time = build_naive_date_time(&(tick_time[0..11].to_string() + &close_time));
                        let tt = naive_date_time.and_utc().timestamp();
                        tick_tm = from_timestamp_to_naive_date_time(tt);
                        flag = 3;
                    }
                } else {
                    // 不在交易时间范围内且不正好是结束时间
                    if center.common_util.trade_area_index(&tick_tm.time().to_string(), exchange).await.is_err() {
                        //不在交易时间
                        if flag != 2 && flag != 3 {
                            //不是结束时间, 不是午休时间
                            info!("fill_kline_data skip, {} falsify tick {}", stock_code, tick_tm);
                            continue;
                        }
                    }
                }
                info!("=================");
                // 补各周期K线
                let kline_types = [KLineType::Min1, KLineType::Min5, KLineType::Min10, KLineType::Min30, KLineType::Min60];
                for &kline_type in &kline_types {
                    if let Err(err) = self.fill_contract_data(kline_type, center, stock_code, &tick_tm).await {
                        return Err(anyhow!("{:?}", err));
                    }
                }
                info!("=================");
                if flag == 3 {
                    //交易日结束
                    info!("save day kline: {}", stock_code);
                    if let Err(err) = self.save_trade_end_data(stock_code, exchange).await {
                        info!("{:?}", err);
                        return Err(anyhow!("{:?}", err));
                    }
                    del_codes.push(stock_code.to_string());
                    //如果是结束时间, 后面无论是否还有要补的, 都不补了
                    break;
                } else if flag == 2 {
                    // 午休，设置为下一个时间段的开始时间
                    //相当于在13:00:00来了一条13:00:00的tick
                    //下一个时间段的开始时间 如沪深的13:00:00
                    // info!("===== : {}", tick_tm.time());
                    let ret = center.common_util.get_next_interval_begin_time(&tick_tm.time().to_string(), exchange).await;
                    if let Err(e) = ret {
                        return Err(anyhow!("{:?}", e));
                    }
                    let btime = format!("{}", ret.unwrap());
                    // info!("获取时间: {}", &btime);
                    //年月日不变时分秒变为下一个时间段开始时间, 如沪深的13:00:00
                    if let Ok(hour) = btime[0..2].parse::<u32>() {
                        tick_tm = tick_tm.with_hour(hour).unwrap();
                    } else {
                        error!("取小时转换失败");
                    }
                    if let Ok(min) = btime[3..5].parse::<u32>() {
                        tick_tm = tick_tm.with_minute(min).unwrap();
                    } else {
                        error!("取分钟转换失败");
                    }
                    tick_tm = tick_tm.with_second(0).unwrap();
                    //将<localtime,ticktime>都设置为下一个时间段的开始时间 如沪深的13:00:00
                    timepair.minute = (tick_tm.hour() * 60 + tick_tm.minute()) as i64;
                    timepair.timestamp = tick_tm.and_utc().timestamp();

                    // info!("设置为下一开始时间: {}", tick_tm);
                    for &kline_type in &kline_types {
                        self.set_next_save_minute(&center.exchange_contract, kline_type, stock_code, &tick_tm).await;
                    }
                    info!("fill_kline_data set {} save minutes {}", stock_code, tick_tm.hour() * 60 + tick_tm.minute());
                    break;
                }
            }
            // 补完后写回
            self.contract_fill.insert(stock_code.to_string(), timepair);
        }
        // 统一删除已结束的合约
        for code in del_codes {
            self.contract_fill.remove(&code);
            self.kline_1.remove(&code);
            self.kline_5.remove(&code);
            self.kline_10.remove(&code);
            self.kline_30.remove(&code);
            self.kline_60.remove(&code);
            self.kline_day.remove(&code);
        }

        info!("fill_kline_data finish, fill size {}", self.contract_fill.len());
        Ok(())
    }

    // 午休时设置KLineDataType里的pre_minutes为下一交易时间段的开始时间13:00
    pub async fn set_next_save_minute(&self, map: &Arc<DashMap<String, String>>, kline_type: KLineType, stock_code: &String, tick_st: &NaiveDateTime) {
        let (mut kline_data, exists) = match kline_type {
            KLineType::Min1 => self.kline_1.get(stock_code),
            KLineType::Min5 => self.kline_5.get(stock_code),
            KLineType::Min10 => self.kline_10.get(stock_code),
            KLineType::Min30 => self.kline_30.get(stock_code),
            KLineType::Min60 => self.kline_60.get(stock_code),
            KLineType::Day => self.kline_day.get(stock_code),
        }
        .map(|kline| (kline.value().clone(), true))
        .unwrap_or_default();

        if exists {
            kline_data.tick_time = format!("{}", tick_st);
            kline_data.prev_minutes = (tick_st.hour() * 60 + tick_st.minute()) as i32;

            if kline_data.period == 60 && map.get(stock_code).unwrap().value() == "XHKG" {
                kline_data.prev_minutes -= 30;
            }

            match kline_type {
                KLineType::Min1 => self.kline_1.insert(stock_code.to_string(), kline_data),
                KLineType::Min5 => self.kline_5.insert(stock_code.to_string(), kline_data),
                KLineType::Min10 => self.kline_10.insert(stock_code.to_string(), kline_data),
                KLineType::Min30 => self.kline_30.insert(stock_code.to_string(), kline_data),
                KLineType::Min60 => self.kline_60.insert(stock_code.to_string(), kline_data),
                KLineType::Day => self.kline_day.insert(stock_code.to_string(), kline_data),
            };
        }
    }

    async fn fill_contract_data(&self, kline_type: KLineType, center: &TickCenterController, stock_code: &String, fake_tick_tm: &NaiveDateTime) -> Result<()> {
        let (mut kline_data, exists) = match kline_type {
            KLineType::Min1 => self.kline_1.get(stock_code),
            KLineType::Min5 => self.kline_5.get(stock_code),
            KLineType::Min10 => self.kline_10.get(stock_code),
            KLineType::Min30 => self.kline_30.get(stock_code),
            KLineType::Min60 => self.kline_60.get(stock_code),
            KLineType::Day => self.kline_day.get(stock_code),
        }
        .map(|kline| (kline.value().clone(), true))
        .unwrap_or_default();
        info!("===================");
        if exists {
            let tick_minutes = (fake_tick_tm.hour() * 60 + fake_tick_tm.minute()) as i32;
            let mut diff_minutes = if tick_minutes > kline_data.prev_minutes { tick_minutes - kline_data.prev_minutes } else { 0 };

            //如果相差N(N>1)个周期, 启用补数据逻辑, 最多补到tick_minutes - 1
            // info!("开始===================1: {}, {}", kline_data.period, diff_minutes);
            if diff_minutes > kline_data.period {
                let mut min = kline_data.prev_minutes + kline_data.period;
                info!("===================");
                while min < tick_minutes {
                    if let Err(err) = self.deal_fill(&mut kline_data, center).await {
                        error!("{:?}", err);
                        return Err(anyhow!("{:?}", err));
                    }
                    min += kline_data.period;
                    diff_minutes -= kline_data.period;
                }
                info!("===================");
            }
            //剩下最多一个周期
            if diff_minutes == kline_data.period {
                let fake_time = format!("{}", fake_tick_tm);
                let tt = build_naive_date_time(&fake_time);

                let tt_fake_tick = tt.and_utc().timestamp() - (60 * kline_data.period) as i64;
                let ret = from_timestamp_to_naive_date_time(tt_fake_tick);
                info!("===================");
                kline_data.tick_time = format!("{}", ret);
                if let Err(err) = self.deal_fill(&mut kline_data, center).await {
                    error!("{:?}", err);
                    return Err(anyhow!("{:?}", err));
                }
                info!("===================");
            }

            match kline_type {
                KLineType::Min1 => self.kline_1.insert(stock_code.to_string(), kline_data),
                KLineType::Min5 => self.kline_5.insert(stock_code.to_string(), kline_data),
                KLineType::Min10 => self.kline_10.insert(stock_code.to_string(), kline_data),
                KLineType::Min30 => self.kline_30.insert(stock_code.to_string(), kline_data),
                KLineType::Min60 => self.kline_60.insert(stock_code.to_string(), kline_data),
                KLineType::Day => self.kline_day.insert(stock_code.to_string(), kline_data),
            };
            info!("===================");
        } else {
            error!("fill_contract_data {} has no kline data", stock_code);
            return Err(anyhow!("fill_contract_data {} has no kline data", stock_code));
        }
        Ok(())
    }

    //交易结束保存交易日分时数据/日K数据,清理合约均价信息
    pub async fn save_trade_end_data(&self, stock_code: &String, exchange: &String) -> Result<()> {
        //特殊处理港股的时K
        if exchange == "XHKG" || exchange == "HK" || exchange == "XASE" || exchange == "XNYS" || exchange == "XNAS" || exchange == "US" {
            if let Some(val) = self.kline_60.get(stock_code) {
                let mut kline_data = val.value().clone();
                kline_data.prev_minutes -= 30;
                if let Err(err) = self.tx_kline.send(kline_data.to_owned()).await {
                    error!("send error: {:?}", err);
                    return Err(anyhow!("send error: {:?}", err));
                }
            } else {
                return Err(anyhow!("{} has no data in the 60 kline map", stock_code));
            }
        }

        //生成日K存入Cassandra
        if let Some(val) = self.kline_day.get(stock_code) {
            let kline_data = val.value();
            info!("Save day K Line {} => {:?}", stock_code, kline_data);
            if let Err(err) = self.tx_kline.send(kline_data.to_owned()).await {
                error!("send error: {:?}", err);
                return Err(anyhow!("send error: {:?}", err));
            }
        } else {
            return Err(anyhow!("{} has no data in the day kline map", stock_code));
        }

        //当天分时数据存入Cassandra
        Ok(())
    }

    pub async fn query_kline(&self, stock_code: &String, kline_type: i32) -> Option<KLineData> {
        let ret = match kline_type {
            1 => self.kline_1.get(stock_code),
            5 => self.kline_5.get(stock_code),
            10 => self.kline_10.get(stock_code),
            30 => self.kline_30.get(stock_code),
            60 => self.kline_60.get(stock_code),
            1440 => self.kline_day.get(stock_code),
            _ => None,
        };

        if ret.is_none() {
            error!("未找到数据: {}", stock_code);
            return None;
        }
        Some(ret.unwrap().value().to_owned())
    }
}
