[package]
name = "phoenix_hqcenter"
version = "0.2.0"
edition = "2021"
description = "行情查询中心 build time: 2025-07-03"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dependencies]
utility = { workspace = true }
common = { workspace = true }
protoes = { workspace = true }

tokio = { workspace = true }
tokio-stream = { workspace = true }
tonic = { workspace = true }
# async-stream = "0.3"
prost = { workspace = true }
futures = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
# log = { workspace = true }
# log4rs = { workspace = true }
tracing = { workspace = true }
config = { workspace = true }
lazy_static = { workspace = true }
chrono = { workspace = true }
time = { workspace = true }
scylla = { workspace = true }
rust_decimal = { workspace = true }

[build-dependencies]
tonic-build = { workspace = true }

# [dependencies]
# utility = { path = "../utility" }
# tokio = { version = "1.28.2", features = ["full"] }
# tokio-stream = { version = "0.1.14", features = ["net"] }
# tonic = { version = "0.12.3", features = ["tls"] }
# # async-stream = "0.3"
# prost = "0.13.3"
# futures = "0.3.28"

# serde = { version = "1.0", features = ["derive"] }
# serde_json = "1.0"
# # futures-lite = "1.12.0"
# # protobuf = { version = "2.25.1", features = ["with-bytes"] }
# # Required for wellknown types
# # lapin = "1.8.0"
# # lapin = { version = "2.1.1", default-features = false, features = ["rustls"] }
# # tokio-amqp = { version = "2.0.0", default-features = false }
# # deadpool = { version = "0.9.0", default-features = false, features = [
# #     "managed",
# # ] }
# # deadpool-lapin = "0.10.0"
# # tcp-stream = { version = "0.24.3", features = ["default"] }
# anyhow = "1.0"
# log = "0.4"
# log4rs = "1.2"
# config = "0.14.0"
# lazy_static = "1.4.0"
# # libc = { version = "0.2.69", optional = true }
# # Uuid support
# # uuid = { version = "1.0.0", features = ["v4"] }
# # redis = "0.21.4"
# #time
# chrono = "0.4.26"
# time = "0.3.21"
# # convert_case = "0.5.0"
# #[dev-dependencies]
# # env_logger = "0.11.3"
# # Apache Cassandra connector
# scylla = "0.14.0"
# # cdrs = { version = "2" }
# # cassandra_macro = "0.1.2"
# # cassandra_macro_derive = "0.1.2"
# # maplit = "1.0.0"
# # regex = "1.5.4"
# # cdrs_helpers_derive = "0.4.0"
# # cdrs-tokio = "6.2.0"
# # cdrs-tokio-helpers-derive = "4.0.0"
# # cassandra-protocol = "1.0.0"
# rust_decimal = "1.33.1"
# [build-dependencies]
# # protoc-rust = "2.28.0"
# tonic-build = { version = "0.12.3", features = ["prost"] }
# # cc = "1.0"

# # [[bin]]
# # name = "phoenix_hqcenter"
# # path = "src/main.rs"

# # [[bin]]
# # name = "c"
# # path = "src/client1.rs"
