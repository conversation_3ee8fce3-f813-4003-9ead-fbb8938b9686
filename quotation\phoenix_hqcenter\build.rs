// // extern crate protoc_rust;
// fn build_grpc() {
//     tonic_build::configure()
//         .out_dir("src/protofiles")
//         .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
//         .compile_protos(
//             &[
//                 "protos/HqMsg.proto",
//                 "protos/SubscribeHqMsg.proto",
//                 "protos/phoenixtickcenter.proto",
//                 "protos/phoenixklinecenter.proto",
//                 "protos/usserver.proto",
//             ],
//             &["protos"],
//         )
//         .unwrap();
// }

fn main() {
    //     build_grpc();
}
