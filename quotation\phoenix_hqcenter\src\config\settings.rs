use config::{Config, ConfigError, File};
use serde::Deserialize;

//连接信息
#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct Application {
    pub apphost: String, //地址
    pub appport: i32,    //端口
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct TickCenter {
    pub uri_hs: String,
    pub uri_hk: String,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct KlineCenter {
    pub uri_hs: String,
    pub uri_hk: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct UsMart {
    pub uri_us: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct CassandraConfig {
    pub addr: String,         //地址：data.metestsvr.com:11092
    pub username: String,     //用户名：cassdbuser
    pub password: String,     //密码：cassdbuser4321
    pub hs_namespace: String, //
    pub hk_namespace: String,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
#[allow(dead_code)]
pub struct Common {
    pub market: String,
    pub delay: u32,
    pub time_diff: u32,
    pub winter_time: u32,
    pub exchange_no: String,
}

#[derive(Debug, <PERSON>lone, Deserialize)]
pub struct Settings {
    pub application: Application, //连接信息
    pub common: Common,           //
    pub tickcenter: TickCenter,
    pub klinecenter: KlineCenter,
    pub usmartcentr: UsMart,
    pub cassandra: CassandraConfig, //Cassandra连接的配置信息
}

impl Settings {
    pub fn new() -> Result<Self, ConfigError> {
        let s = Config::builder()
            // Start off by merging in the "default" configuration file
            .add_source(File::with_name("config/hqcenter.toml"))
            // // Add in the current environment file
            // // Default to 'development' env
            // // Note that this file is _optional_
            // .add_source(File::with_name(&format!("examples/hierarchical-env/config/{}", run_mode)).required(false))
            // // Add in a local configuration file
            // // This file shouldn't be checked in to git
            // .add_source(File::with_name("examples/hierarchical-env/config/local").required(false))
            // // Add in settings from the environment (with a prefix of APP)
            // // Eg.. `APP_DEBUG=1 ./target/app` would set the `debug` key
            // .add_source(Environment::with_prefix("app"))
            // // You may also programmatically change settings
            // .set_override("database.url", "postgres://")?
            .build()
            .expect("build config file error");

        s.try_deserialize()
    }
}

// extern crate lazy_static;

// use std::fmt::Debug;
// use std::io;
// use std::fs::File;
// use std::io::{BufReader, BufRead};
// use std::collections::HashMap;
// use std::sync::Mutex;

// use lazy_static::lazy_static;

// lazy_static! {
// 	// 此处表示声明全局可变 HashMap
//     static ref MAP: Mutex<HashMap<String, String>> = Mutex::new(HashMap::new());
// }

// // 读取文件,path表示文件路径
// pub fn read_file(path: &str) -> Result<i32, io::Error> {
//     let mut map: HashMap<String, String> = HashMap::new();
// 	// 读取文件，失败直接返回Err
// 	let file: File = File::open(path)?;
// 	let buffered: BufReader<File> = BufReader::new(file);
//  	// 存放`[key]`
//     let mut key: String = "".to_string();
//     // 缓存 : 去掉空格
//     let mut new_line = "".to_string();

// 	for line in buffered.lines().map(|x| x.unwrap()) {
// 		new_line.clear();
//         new_line.push_str(line.trim());//字符串追加

//         println!("new_line: {}",new_line);
//  		// 定义注释为`#`, 遇到注释跳过
//  		 if line.contains("#") {
//             continue;
//         } else if line.contains("[") && line.contains("]") { // 解析`[key]`为`key::`
//         	key.clear();
//             new_line.pop();
//             new_line.remove(0);
//             key.push_str(new_line.as_str());
//             key.push_str("::");
//         } else if new_line.contains("=") { // 必须包含表达式, 才将值写入
//             let kvs: Vec<&str> = new_line.as_str().split("=").collect::<Vec<&str>>();
//             if kvs.len() == 2 { // 如果不满足则定义为异常数据，该数据暂不处理
//                 // 缓存
//                 let mut new_key: String = key.clone();
//                 new_key.push_str(kvs[0]);
//                 println!("new_key: {}",new_key);
//                 println!("kvs[0]: {}",kvs[0]);
//                 println!("kvs[1]: {}",kvs[1]);

//                 MAP.lock().unwrap().insert(new_key.trim().to_string(), kvs[1].trim().to_string());
//                 map.entry(new_key.trim().to_string()).or_insert(kvs[1].trim().to_string());
//             }
//         }
// 	}
//     println!("{:#?}",map);
// 	return Ok(0);
// }

// pub fn read_config() { //->Result<(), Box<dyn std::error::Error>> {
//     //let mut map: HashMap<String, String> = HashMap::new();
// 	 // 这里只需要处理错误
//     if let Err(e) = read_file("svrconfig.ini") {
//         println!("err = {:?}", e);
//     }

//  	// for /* (key, value)*/s in MAP.lock().unwrap().iter() {
//     //     // println!("k = {}\ty = {}", key, value);
//     //     println!("{:?}",s);

//     // }

//     // for (key, value) in MAP.lock().unwrap().iter() {
//     //     // println!("k = {}\ty = {}", key, value);
//     //     map.entry(key.to_string()).or_insert(value.to_string());
//     // }
//     //println!("{:#?}",map);
//     //Ok(())
// }

// #[derive(Debug, Default)]
// pub struct cassandra_config {
//     pub CassandraSwitch: String,
//     pub IP: String,
//     pub Port: String,
//     pub User: String,
//     pub Password: String,
//     pub Namespace: String
// }
// impl cassandra_config {
//     pub async fn new(path: &str) -> Result<Self, io::Error> {
//         let mut map: HashMap<String, String> = HashMap::new();
//         // 读取文件，失败直接返回Err
//         let file: File = File::open(path)?;
//         let buffered: BufReader<File> = BufReader::new(file);
//         // 存放`[key]`
//         let mut key: String = "".to_string();
//         // 缓存 : 去掉空格
//         let mut new_line = "".to_string();

//         for line in buffered.lines().map(|x| x.unwrap()) {
//             new_line.clear();
//             new_line.push_str(line.trim());//字符串追加
//             // 定义注释为`#`, 遇到注释跳过
//             if line.contains("#") {
//                 continue;
//             } else if line.contains("[") && line.contains("]") { // 解析`[key]`为`key::`
//             	key.clear();
//                 new_line.pop();
//                 new_line.remove(0);
//                 key.push_str(new_line.as_str());
//                 key.push_str("::");
//             } else if new_line.contains("=") { // 必须包含表达式, 才将值写入
//                 let kvs: Vec<&str> = new_line.as_str().split("=").collect::<Vec<&str>>();
//                 if kvs.len() == 2 { // 如果不满足则定义为异常数据，该数据暂不处理
//                     // 缓存
//                     let mut new_key: String = key.clone();
//                     new_key.push_str(kvs[0]);
//                     map.entry(new_key.to_string()).or_insert(kvs[1].to_string());
//                 }
//             }
// 	    }
//         //println!("{:#?}",map);
//         let config = cassandra_config {
//             CassandraSwitch: map.get("Cassandra::CassandraSwitch").unwrap().to_string(),
//             IP: map.get("Cassandra::IP").unwrap().to_string(),
//             Port: map.get("Cassandra::Port").unwrap().to_string(),
//             User: map.get("Cassandra::User").unwrap().to_string(),
//             Password: map.get("Cassandra::Password").unwrap().to_string(),
//             Namespace: map.get("Cassandra::Namespace").unwrap().to_string()

//             // CassandraSwitch: map.get("CassandraSwitch").unwrap().to_string(),
//             // IP: map.get("IP").unwrap().to_string(),
//             // Port: map.get("Port").unwrap().to_string(),
//             // User: map.get("User").unwrap().to_string(),
//             // Password: map.get("Password").unwrap().to_string(),
//             // Namespace: map.get("Namespace").unwrap().to_string()
//         };
//         Ok(config)
//     }
// }
