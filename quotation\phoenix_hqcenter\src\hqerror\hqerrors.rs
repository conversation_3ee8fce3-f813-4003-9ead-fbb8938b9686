/* 错误说明
错误代码总长度是6为，其中前两位分类
0 系统类
1 账号类
2 品种类
9 其它
*/
#[allow(unused)]
pub enum ErrorCode {
    CodeOk,                 /*0 正常*/
    CodeRedisNoData,        /*1 未查到相关信息*/
    CodeSystemErrRequest,   /*2 请求参数不完整*/
    CodeContractNotexist,   /*3 合约编号不存在*/
    CodeRedisFlagErr,       /*4 Redis配置flag错误*/
    CodeGoodsNotexist,      /*5 品种代码不存在!*/
    CodeExchangeNONotexist, /*6 市场代码不存在 */
    CodeNotFound,           /*7 NOT FOUND*/
    CodeUnknown,            /*未知*/
}

pub struct PhoenixHqError(pub String, pub i32);

pub fn get_error_code(code: ErrorCode) -> PhoenixHqError {
    match code {
        ErrorCode::CodeOk => PhoenixHqError(String::from("Ok"), 0),
        ErrorCode::CodeRedisNoData => PhoenixHqError(String::from("未查到相关信息"), 1),
        ErrorCode::CodeSystemErrRequest => PhoenixHqError(String::from("请求参数不完整"), 2),
        ErrorCode::CodeContractNotexist => PhoenixHqError(String::from("合约编号不存在"), 3),
        ErrorCode::CodeRedisFlagErr => PhoenixHqError(String::from("Redis配置flag错误"), 4),
        ErrorCode::CodeGoodsNotexist => PhoenixHqError(String::from("品种代码不存在"), 5),
        ErrorCode::CodeExchangeNONotexist => PhoenixHqError(String::from("市场代码不存在"), 6),
        ErrorCode::CodeNotFound => PhoenixHqError(String::from("没有找到相应的信息"), 7),
        _ => PhoenixHqError(String::from("其他错误"), 999),
    }
}
