#[macro_use]
extern crate anyhow;
extern crate chrono;
extern crate lazy_static;
// extern crate maplit;
// extern crate redis;

mod client;
mod config;
mod hqerror;
mod server;

use std::time::Duration;
use tracing::*;

use protoes::hqcenter::svr_post_subscribe_hq_msg_server::SvrPostSubscribeHqMsgServer;
// use utility::loggings;

use crate::config::settings::Settings;
use crate::server::server::SvrPostSubscribeHqMsgHandler;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/hqcenter.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_hqcenter";
    let dir = "./log";

    let settings = Settings::new().unwrap();
    let level = "INFO";
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:#?}", &settings);

    let server = prepare(&settings).await.expect("Init state error");
    run_server(server).await
}

async fn prepare(settings: &Settings) -> anyhow::Result<SvrPostSubscribeHqMsgHandler> {
    // let settings = Settings::new().unwrap();

    let grpc = SvrPostSubscribeHqMsgHandler::new(&settings).await;
    Ok(grpc)
}

async fn run_server(mut server: SvrPostSubscribeHqMsgHandler) -> Result<(), Box<dyn std::error::Error>> {
    let app_url = format!("{}:{}", server.settings.application.apphost, server.settings.application.appport); //地址和端口号
                                                                                                              // let app_url = format!(":{}", server.settings.application.appport);//地址和端口号
    let addr = app_url.as_str().parse().unwrap();
    info!("Starting hqcenter service on: {}", addr);
    info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));

    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = server.on_leave(); //调用方法返回ServerLeave

    tokio::spawn(async move {
        //创建异步任务
        tokio::signal::ctrl_c().await.ok(); //发送Ctrl-c信号,终止任务
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder()
        .timeout(Duration::from_secs(10)) //创建可以配置[server]的新服务器生成器。
        .tcp_keepalive(Some(Duration::from_secs(60)))
        .add_service(SvrPostSubscribeHqMsgServer::new(server))
        .serve_with_shutdown(addr, async {
            //使用该服务器创建一个未来，在tokio的服务器上执行该服务器,并在接收到所提供的信号时关闭。
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");

    Ok(())
}
