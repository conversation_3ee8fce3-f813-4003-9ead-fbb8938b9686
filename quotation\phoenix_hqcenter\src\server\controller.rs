extern crate chrono;
use anyhow::Result;
use chrono::prelude::*;
// use prost::Message;
use std::fmt::Write;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;
// use scylla::IntoTypedRows;

use protoes::{
    hqcenter::{KLineHqInfo, KLineHqReq, KLineHqResp, LastPriceInfo, LastPriceMsgReq, LastPriceMsgResp, PreDealAmount, PreDealNumReq, ResultMsg, SubscribeHqMsgReq, TickHqReq, TickHqResp},
    hqmsg::YsHqInfo,
};
use utility::timeutil::{build_naive_date_time, current_naive_time, from_timestamp_to_naive_date_time};

use super::service::common::{HqTick, KlineDate};
use crate::client::{cassandraclient::CassandraClient, klinecenterclient::KlineCenterClient, tickcenterclient::TickCenterClient, usmartclient::UsMartClient};
use crate::config::settings::Settings;
use crate::hqerror::hqerrors;
use crate::server::service::common::{anti_serialize_tick, get_current_period, get_table_name_by_kline_type, KLineType};

//行情查询控制中心
#[derive(Clone)]
pub struct PhoenixHqcenterController {
    pub settings: Settings,
    pub tick_client: Arc<RwLock<TickCenterClient>>,
    pub kline_client: Arc<RwLock<KlineCenterClient>>,
    pub us_client: Arc<RwLock<UsMartClient>>,
    pub cassandra_client: Arc<CassandraClient>,
}

impl PhoenixHqcenterController {
    //行情订阅
    pub async fn get_post_subscribe_hq_msg(&self, _req: &SubscribeHqMsgReq) -> Result<ResultMsg> {
        let res = ResultMsg {
            err_msg: hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).0,
            err_code: hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).1,
        };
        Ok(res)
    }

    //查询K线
    pub async fn get_hq_kline(&self, req: &KLineHqReq) -> Result<KLineHqResp> {
        info!("strcontractno: {}, realtime: {}, klinetype: {}", req.strcontractno, req.realtime, req.strklinetype);

        // 1. 美股直接返回
        if req.strcontractno.contains("_XASE") || req.strcontractno.contains("_XNYS") || req.strcontractno.contains("_XNAS") || req.strcontractno.contains("_US") {
            return self.us_client.write().await.query_us_kline(req).await.or_else(|_| Ok(KLineHqResp::default()));
        }

        let mut resp = KLineHqResp::default();
        let mut kline_info = KLineHqInfo::default();
        let mut klinedate = KlineDate::default();

        // if req.strklinetype == "168" || req.strklinetype == "720" {
        //     // 1.cassandra中没有当前周期的 内存+Cassandra
        //     // 2.Cassandra中有当前周期的 合并内存与Cassandra第一条+Cassandra其他条
        //     PhoenixHqcenterController::get_multiple_day_kline(&self, &req, &mut resp).await;
        //     resp.klineinfo.push(kline_info);
        //     return Ok(resp);
        // }

        // 2. 解析K线类型
        let mut kline_type: i32 = req.strklinetype.parse().unwrap_or_default();
        if kline_type == 24 {
            kline_type = KLineType::Hq24Kline as i32;
        }
        let table_name = get_table_name_by_kline_type(kline_type).await;
        if table_name.is_empty() {
            info!("K线请求信息有误: code: {}, type: {}", &req.strcontractno, &req.strklinetype);
            return Ok(resp);
        }
        info! {"获取表:{}", table_name};

        // 3. 时间参数处理
        let mut btime = 0i64;
        let mut etime = 0i64;
        let mut flag = true; //是否从内存中取最新K线
        let filter_num = 0i32;
        let start_time = 0i64;
        let end_time = 0i64;
        let tsecond = 0i64;
        let nextperiod_time = 0i64;
        let turnover: f64 = 0.0;

        info!("strtime:{}", req.strendtime);
        if !req.strendtime.is_empty() {
            if !req.strendtime.contains("-") {
                btime = req.strendtime.parse().unwrap_or_default();
                info!("btime:{}", btime);
            } else {
                let time: Vec<&str> = req.strendtime.split('-').collect();
                btime = time[0].parse().unwrap_or_default();
                etime = time[1].parse().unwrap_or_default();
                info!("btime:{} etime:{}", btime, etime);
                flag = false;
            }
        }

        // 4. 港股/美股延时行情特殊处理
        if !req.strcontractno.contains("XSHE") && !req.strcontractno.contains("XSHG") && req.realtime == 0 {
            // ...（此处可提取为函数，略）
            // 省略原有延时行情时间处理逻辑，保持原有功能
            // 设置 filter_num = 1, 并计算 start_time, end_time, tsecond, nextperiod_time
        }

        info!("filterNum: {}", filter_num);
        // 5. 查询内存K线
        if filter_num == 1 {
            PhoenixHqcenterController::query_1_kline(self, req, &mut resp.klineinfo, end_time, start_time, tsecond, nextperiod_time).await?;
        } else if flag {
            let last_kline_value = self.get_last_kline_data(&req.strcontractno, &req.strklinetype).await?;
            if !last_kline_value.is_empty() {
                info!("{}最新{}分钟K线: {}", req.strcontractno, kline_type, last_kline_value);
                kline_info.strkline = last_kline_value;
                resp.klineinfo.push(kline_info.clone());
            }
        }

        // 6. 构造CQL
        let cql = if btime == 0 && etime == 0 {
            format!(
                "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
            from {} where vc_code = '{}' order by l_update_time desc limit {} ",
                table_name, req.strcontractno, req.limit
            )
        } else if btime != 0 && etime == 0 {
            format!(
                "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
            from {} where vc_code = '{}' and l_update_time > {} order by l_update_time desc",
                table_name, req.strcontractno, btime
            )
        } else {
            format!(
                "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
            from {} where vc_code = '{}' and l_update_time > {} and l_update_time < {} order by l_update_time desc",
                table_name, req.strcontractno, btime, etime
            )
        };
        info!("cql: {}", cql);

        if let Some(rows_result) = self.cassandra_client.query_cass(&cql, &req.strcontractno).await {
            let mut rows_iter = rows_result.rows::<(i64, f32, f32, f32, f32, f32, i32)>()?;
            while let Some((l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume)) = rows_iter.next().transpose()? {
                klinedate.l_update_time = l_update_time;
                klinedate.en_close_price = en_close_price;
                klinedate.en_high_price = en_high_price;
                klinedate.en_last_price = en_last_price;
                klinedate.en_low_price = en_low_price;
                klinedate.en_open_price = en_open_price;
                klinedate.l_volume = l_volume;

                //延迟需要过滤
                if filter_num == 1 && klinedate.l_update_time > end_time {
                    continue;
                }

                klinedate.l_update_time -= klinedate.l_update_time % 100; //去掉秒
                if kline_type != KLineType::Hq24Kline as i32 {
                    klinedate.l_update_time = PhoenixHqcenterController::convert_to_local_time(&self, klinedate.l_update_time).await?;
                }
                //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
                //保留3位小数
                let kline_value = format!(
                    "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                    klinedate.en_close_price, klinedate.en_high_price, klinedate.en_last_price, klinedate.en_low_price, klinedate.en_open_price, klinedate.l_volume, klinedate.l_update_time, turnover
                );
                kline_info.strkline = kline_value;
                resp.klineinfo.push(kline_info.clone());
            }
        } else {
            info!("Query Cassandra None");
        }

        info!("[get_hq_kline] GetHqKline kline info size:{}", resp.klineinfo.len());

        // 8. 去重
        let mut seen = std::collections::HashSet::new();
        resp.klineinfo.retain(|item| {
            let str: Vec<&str> = item.strkline.rsplitn(3, "|").collect();
            seen.insert(str[1].to_owned())
        });

        info!("get_hq_kline End");

        Ok(resp)
    }

    // 1.cassandra中没有当前周期的 内存+Cassandra
    // 2.Cassandra中有当前周期的 合并内存与Cassandra第一条+Cassandra其他条
    #[allow(dead_code)]
    async fn get_multiple_day_kline(&self, req: &KLineHqReq, resp: &mut KLineHqResp) -> Result<()> {
        info!("[get_multiple_day_kline] code: {}, type: {}", req.strcontractno, req.strklinetype);

        let period: i32 = req.strklinetype.parse().unwrap_or_default();
        let _current_period = get_current_period(period, &req.strcontractno).await;

        // 从内存中获取最新日K线（如需用到可补充业务逻辑）
        let kline_data = {
            let mut kline_client = self.kline_client.write().await;
            let ret_kline = kline_client.get_last_kline_data(&req.strcontractno, &req.strklinetype).await;
            if let Err(e) = &ret_kline {
                error!("{:?}", e);
                return Err(anyhow!("{:?}", e));
            }
            ret_kline.unwrap()
        };
        if !kline_data.stock_code.is_empty() {}

        // 查询Cassandra
        let table_name = get_table_name_by_kline_type(period).await;
        if table_name.is_empty() {
            return Ok(());
        }
        let cql = format!(
            "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
        from {} where vc_code = '{}' order by l_update_time desc limit {};",
            table_name, req.strcontractno, req.limit
        );
        info!("get_multiple_day_kline query cql: {}", cql);

        if let Some(rows_result) = self.cassandra_client.query_cass(&cql, &req.strcontractno).await {
            let mut rows_iter = rows_result.rows::<(i64, f32, f32, f32, f32, f32, i32)>()?;
            while let Some((l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume)) = rows_iter.next().transpose()? {
                let kline_info = KLineHqInfo {
                    strkline: format!(
                        "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                        en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume, l_update_time, 0.00
                    ),
                };
                info!("get_multiple_day_kline {}({}) {}", req.strcontractno, period, kline_info.strkline);
                resp.klineinfo.push(kline_info);
            }
        }

        // let mut flag: bool; // = false;
        Ok(())
    }

    async fn query_1_kline(&self, req: &KLineHqReq, klineinfo: &mut Vec<KLineHqInfo>, et: i64, st: i64, tsecond: i64, nextperiod_time: i64) -> Result<()> {
        info!("start_time:{} end_time:{}", st, et);

        let mut close_price = 0.0f32;
        let mut high_price = 0.0f32;
        let mut last_price = 0.0f32;
        let mut low_price = 0.0f32;
        let mut open_price = 0.0f32;
        let mut update_time = 0i64;
        let mut volume = 0i32;
        let mut turnover = 0.0f64;
        let d_turnover = 0.0f64;
        let mut size = 0;

        // 查询K线主表
        let cql = format!(
            "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
        from tstockhq1kline where vc_code = '{}' and l_update_time <= {} and l_update_time > {} order by l_update_time desc;",
            req.strcontractno, et, st
        );
        info!("cql: {}", cql);

        if let Some(rows_result) = self.cassandra_client.query_cass(&cql, &req.strcontractno).await {
            let mut rows_iter = rows_result.rows::<(i64, f32, f32, f32, f32, f32, i32)>()?;
            while let Some((l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume)) = rows_iter.next().transpose()? {
                if size == 0 {
                    close_price = en_close_price;
                    high_price = en_high_price;
                    last_price = en_last_price;
                    low_price = en_low_price;
                    open_price = en_open_price;
                    turnover = d_turnover;
                    update_time = l_update_time;
                    volume = l_volume;
                } else {
                    volume += l_volume;
                    turnover += d_turnover;
                    if en_high_price > high_price {
                        high_price = en_high_price;
                    }
                    if en_low_price < low_price {
                        low_price = en_low_price;
                    }
                }
                size += 1;
            }

            update_time -= update_time % 100;
            info!("[query_1_kline] update_time:{}", update_time);

            if size != 0 {
                let kline_value = format!(
                    "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                    close_price, high_price, last_price, low_price, open_price, volume, nextperiod_time, turnover
                );
                info!("[query_1_kline] sklineValue:{}", kline_value);
                klineinfo.push(KLineHqInfo { strkline: kline_value });
            }
            info!("[query_1_kline] size:{}", klineinfo.len());
        } else {
            info!("Query Cassandra None");
        }

        // 查询tick表补充
        let cql = format!(
            "select vc_content from tstockhqtick where vc_contract_code = '{}' \
        and l_update_time <= '{}' and l_update_time > '{}' order by l_update_time desc;",
            req.strcontractno,
            tsecond * 1000,
            (tsecond + 60) * 1000
        );
        info!("cql: {}", cql);

        if let Some(rows_result) = self.cassandra_client.query_cass(&cql, &req.strcontractno).await {
            let mut rows_iter = rows_result.rows::<(String,)>()?;
            while let Some(vc_content) = rows_iter.next().transpose()? {
                let mut hqtick = YsHqInfo::default();
                anti_serialize_tick(&mut hqtick, &vc_content.0).await;
                info!("{:#?}", hqtick);

                volume += hqtick.q_last_qty as i32;
                // turnover += 0.0; // tick成交额暂未用
                if (hqtick.q_high_price as f32) > high_price {
                    high_price = hqtick.q_high_price as f32;
                }
                if (hqtick.q_low_price as f32) < low_price {
                    low_price = hqtick.q_low_price as f32;
                }
                size += 1;
            }

            if size != 0 {
                let kline_value = format!(
                    "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                    close_price, high_price, last_price, low_price, open_price, volume, nextperiod_time, turnover
                );
                info!("[query_1_kline] sklineValue:{}", kline_value);
                klineinfo.push(KLineHqInfo { strkline: kline_value });
            }
            info!("[query_1_kline] size:{}", klineinfo.len());
        } else {
            info!("Query Cassandra None");
        }

        info!("[query_1_kline] end");

        Ok(())
    }

    pub async fn get_current_fenshi_hq(&self, req: &KLineHqReq) -> Result<KLineHqResp> {
        let mut resp = KLineHqResp::default();
        let mut kline_info = KLineHqInfo::default();

        // 1. 美股直接返回
        if req.strcontractno.contains("_XASE") || req.strcontractno.contains("_XNYS") || req.strcontractno.contains("_XNAS") || req.strcontractno.contains("_US") {
            if let Ok(kline) = self.us_client.write().await.query_us_fen_shi(&req.strcontractno).await {
                kline_info.strkline = kline;
                resp.klineinfo.push(kline_info);
            }
            return Ok(resp);
        }

        // 2. 获取已生成的分时
        let mut kline_client = self.kline_client.write().await;
        let val_resp = match kline_client.get_generate_fenshi_hq(&req.strcontractno, &req.strklinetype).await {
            Ok(val) => val.fenshi_hq.clone(),
            Err(e) => {
                error!("{:?}", e);
                return Err(anyhow!("{:?}", e));
            }
        };
        info!("已经生成的分时: {}", &val_resp);

        // 3. 获取当前未生成的1分钟K线
        let kline_data = match kline_client.get_last_kline_data(&req.strcontractno, &"1".to_string()).await {
            Ok(data) => data,
            Err(e) => {
                error!("{:?}", e);
                return Err(anyhow!("{:?}", e));
            }
        };
        drop(kline_client);
        if kline_data.stock_code.is_empty() {
            info!("未找到最新K线数据: {:?}", &kline_data);
            if val_resp.is_empty() {
                // Cassandra兜底
                match self.get_history_time_share(&req.strcontractno).await {
                    Ok(r) => return Ok(r),
                    Err(e) => {
                        error!("{:?}", e);
                        return Err(anyhow!("{:?}", e));
                    }
                }
            } else {
                kline_info.strkline = val_resp.clone();
                resp.klineinfo.push(kline_info);
                return Ok(resp);
            }
        }

        // 4. 拼接分时数据
        let minutes = (kline_data.prev_minutes + kline_data.period) % 1440;
        let st = build_naive_date_time(&kline_data.tick_time);
        let time = format!("{:04}{:02}{:02}{:02}{:02}00", st.year(), st.month(), st.day(), minutes / 60, minutes % 60);

        let mut final_resp = val_resp.clone();
        if !val_resp.is_empty() {
            let ret: Vec<&str> = val_resp.rsplit('|').collect();
            info!("分时线日期: {}", &ret[0][0..8]);
            if time.contains(&ret[0][0..8]) {
                let delta_data = format!("{:<.03}|{:<.03}|{}|{}+", kline_data.average_price, kline_data.last_price, kline_data.current_period_volume, time);
                let _ = write!(final_resp, "{}", delta_data);
            }
        } else {
            final_resp = format!(
                "{:<.03}|{:<.03}|{:<.03}|{}|{}+",
                kline_data.pre_close_price, kline_data.average_price, kline_data.last_price, kline_data.current_period_volume, time
            );
        }
        info!("分时: {}", &final_resp);

        if final_resp.is_empty() {
            // Cassandra兜底
            match self.get_history_time_share(&req.strcontractno).await {
                Ok(r) => return Ok(r),
                Err(e) => {
                    error!("{:?}", e);
                    return Err(anyhow!("{:?}", e));
                }
            }
        } else {
            kline_info.strkline = final_resp;
            resp.klineinfo.push(kline_info);
        }

        Ok(resp)
    }

    pub async fn get_pre_deal_amount(&self, req: &PreDealNumReq) -> Result<PreDealAmount> {
        info!("get_pre_deal_amount StockCode:{} ExchangeId:{}", req.stock_code, req.exchange_id);
        let mut res = PreDealAmount::default();

        let contract_no1 = match req.exchange_id {
            101 => format!("{}_XSHG", req.stock_code),
            102 => format!("{}_XSHE", req.stock_code),
            103 => format!("{}_XHKG", req.stock_code), //港交所
            104 => format!("{}_XASE", req.stock_code), //美交所
            105 => format!("{}_XNYS", req.stock_code), //纽交所
            106 => format!("{}_XNAS", req.stock_code), //纳斯达克
            _ => String::new(),
        };
        info!("get_pre_deal_amount key:{}", contract_no1);

        if contract_no1.is_empty() {
            error!("市场代码不存在");
            return Ok(res);
        }

        let fenshi_val = match self.kline_client.write().await.get_generate_fenshi_hq(&contract_no1, "1").await {
            Ok(val) => val,
            Err(e) => {
                error!("{:?}", e);
                return Ok(res);
            }
        };
        info!("分时: {}", &fenshi_val.fenshi_hq);
        // let vec: Vec<&str> = fenshi_val.fenshi_hq.rsplitn(3, "|").collect();//当前周期的上一周期

        // 查找指定时间点的成交量
        if let Some(idx) = fenshi_val.fenshi_hq.find(&req.time) {
            // 找到时间点后，向前找两个'|'，取成交量字段
            let before = &fenshi_val.fenshi_hq[..idx];
            let mut parts = before.rsplitn(3, '|');
            let _ = parts.next(); // 跳过时间字段
            if let Some(amount_str) = parts.next() {
                info!("获取{}点成交量：{}", req.time, amount_str);
                res.prev_period_amount = amount_str.parse().unwrap_or_default();
            }
        }

        // let val: Vec<&str> = fenshi_val.fenshi_hq.rsplitn(2, req.time.as_str()).collect();
        // if val.is_empty() || val.len() != 2 {
        //     return Ok(res);
        // }
        // let vec: Vec<&str> = val[1].rsplitn(3, "|").collect();
        // info!("获取{}点成交量：{}", req.time, vec[1]);
        // res.prev_period_amount = vec[1].parse().unwrap_or_default();
        Ok(res)
    }

    async fn get_history_tick(&self, code: &str, time: i64, value: &mut String) -> Result<()> {
        let mut tick = HqTick::new();
        let strat_time = format!("{}{}", (time - 5), "000");
        let end_time = format!("{}{}", (time - 5), "000");
        info!("get_history_tick code {} starttime:{} endtime: {}", &code, &strat_time, &end_time);

        let st = from_timestamp_to_naive_date_time(time);
        info!("trade_day: {}", st.to_string());

        let cql = format!(
            "select vc_content from tstockhqtick \
            where vc_contract_code = '{}' and  l_update_time  > '{}'  and l_update_time < '{}' ALLOW FILTERING;",
            code, strat_time, end_time
        );
        info!("cql: {}", &cql);
        if let Some(rows_result) = self.cassandra_client.query_cass(&cql, code).await {
            let mut rows_iter = rows_result.rows::<(String,)>()?;
            while let Some(vc_content) = rows_iter.next().transpose()? {
                tick.vc_content = vc_content.0;
                if tick.vc_content.contains(&st.to_string()) {
                    continue;
                }
                let _ = write!(value, "{}", tick.vc_content);
            }
        } else {
            return Err(anyhow!("{:?}", "未查到tick数据"));
        }
        Ok(())
    }

    async fn get_delay_tick(&self, code: &str) -> Result<YsHqInfo> {
        // 计算延时tick的查询时间
        let mut st = current_naive_time();
        let mut tt = st.and_utc().timestamp() - 15 * 60;

        // 港股收盘后特殊处理，取16点tick
        if code.contains("XHKG") {
            st = from_timestamp_to_naive_date_time(tt);
            if st.hour() >= 16 {
                tt = tt - (st.hour() as i64 - 16) * 3600 - st.minute() as i64 * 60 - st.second() as i64;
            }
        }

        st = from_timestamp_to_naive_date_time(tt);
        info!("get_delay_tick time: {}", st);

        let llt = utility::timeutil::convert_datetime_to_timestamp(&st.to_string());
        let cql = format!(
            "select vc_content from tstockhqtick \
        where vc_contract_code = '{}' and l_update_time < '{}' ORDER BY l_update_time DESC limit 1 ALLOW FILTERING;",
            code, llt
        );
        info!("cql: {}", cql);

        let mut hqtick = YsHqInfo::default();
        if let Some(rows_result) = self.cassandra_client.query_cass(&cql, code).await {
            let mut rows_iter = rows_result.rows::<(String,)>()?;
            if let Some(vc_content) = rows_iter.next().transpose()? {
                anti_serialize_tick(&mut hqtick, &vc_content.0).await;
            }
        } else {
            info!("Query Cassandra None");
        }
        Ok(hqtick)
    }

    //获取历史分时数据
    pub async fn get_history_time_share(&self, code: &str) -> Result<KLineHqResp> {
        info!("获取 {} 历史分时", code);
        let mut resp = KLineHqResp::default();
        let mut kline_info = KLineHqInfo::default();
        let mut value = String::new();

        let ch = code.to_uppercase();
        let cql = format!(
            "select vc_content from tstockhqtimeshare \
        where vc_code = 'STOCK_FS:{}' ORDER BY l_update_time DESC limit 1 ALLOW FILTERING;",
            ch
        );
        info!("get_history_time_share query cql:{}", cql);

        if let Some(rows_result) = self.cassandra_client.query_cass(&cql, code).await {
            let mut rows_iter = rows_result.rows::<(String,)>()?;
            if let Some(vc_content) = rows_iter.next().transpose()? {
                value = vc_content.0;
            }
        } else {
            info!("Query Cassandra None");
        }

        info!("get_history_time_share klineValue:{}", value);

        if !value.is_empty() {
            let ret = PhoenixHqcenterController::convert_fs_to_local_time(self, &value).await;
            match ret {
                Ok(val) => kline_info.strkline = val,
                Err(e) => {
                    error!("{:?}", e);
                    return Err(anyhow!("{:?}", e));
                }
            }
        }

        resp.klineinfo.push(kline_info);

        Ok(resp)
    }

    //接口取最新价
    pub async fn get_tick_price(&self, req: &LastPriceMsgReq) -> Result<LastPriceMsgResp> {
        info!("get_tick_price StockCode:{} ExchangeId:{}", req.stock_code, req.exchange_id);
        let mut res = LastPriceMsgResp::default();
        res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).0;
        res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).1;

        let contract_no1 = match req.exchange_id {
            101 => format!("{}_XSHG", req.stock_code),
            102 => format!("{}_XSHE", req.stock_code),
            103 => format!("{}_XHKG", req.stock_code), //港交所
            104 => format!("{}_XASE", req.stock_code), //美交所
            105 => format!("{}_XNYS", req.stock_code), //纽交所
            106 => format!("{}_XNAS", req.stock_code), //纳斯达克
            _ => String::new(),
        };
        info!("stock_code: {}", contract_no1);

        if contract_no1.is_empty() {
            res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeExchangeNONotexist).0;
            res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeExchangeNONotexist).1;
            error!("市场代码不存在");
            return Ok(res);
        }

        let mut last_price_info = LastPriceInfo::default();

        if contract_no1.contains("_XASE") || contract_no1.contains("_XNYS") || contract_no1.contains("_XNAS") {
            match self.us_client.write().await.query_us_last_price(&contract_no1).await {
                Ok(data) => {
                    info!("query us last price: {:?}", data);
                    last_price_info.last_price = data.last_price; //最新价
                    last_price_info.change_value = data.change_value; //涨跌值
                    last_price_info.change_rate = data.change_rate; //涨跌幅
                    info!(
                        "us stock_code: {}, last_price: {}, change_value: {}, change_rate: {}",
                        contract_no1, last_price_info.last_price, last_price_info.change_value, last_price_info.change_rate
                    );
                }
                Err(err) => {
                    error!("query us last price err: {:?}", err);
                    res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).0;
                    res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).1;
                    return Ok(res);
                }
            }
        } else {
            let ret = self.tick_client.write().await.get_last_price(&contract_no1).await;
            let data = match ret {
                Ok(ref v) if v.data.is_some() => v.data.as_ref().unwrap(),
                _ => {
                    error!("get last price err: {:?}", ret);
                    res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).0;
                    res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).1;
                    return Ok(res);
                }
            };
            info!("query hs or hk last price: {:?}", data);
            last_price_info.last_price = data.q_last_price;
            last_price_info.change_value = data.q_change_value;
            last_price_info.change_rate = data.q_change_rate;
            info!(
                "stock_code: {}, last_price: {}, change_value: {}, change_rate: {}",
                contract_no1, last_price_info.last_price, last_price_info.change_value, last_price_info.change_rate
            );
        }
        res.data = Some(last_price_info);
        info!("ret value: {:?}", res);
        Ok(res)
    }

    pub async fn get_tick_hq(&self, req: &TickHqReq) -> Result<TickHqResp> {
        let mut resp = TickHqResp::default();

        if req.ticktime == 0 {
            // 实时tick
            let stock_codes: Vec<&str> = req.strcontractno.split(',').filter(|x| !x.is_empty()).collect();

            let mut strcontractno_hs = String::new();
            let mut strcontractno_hk = String::new();
            let mut strcontractno_us = Vec::new();

            for &stock_code in &stock_codes {
                if stock_code.contains("XSHE") || stock_code.contains("XSHG") || stock_code.contains("HS") {
                    strcontractno_hs.push_str(stock_code);
                    strcontractno_hs.push(',');
                } else if stock_code.contains("XHKG") || stock_code.contains("HK") {
                    strcontractno_hk.push_str(stock_code);
                    strcontractno_hk.push(',');
                } else {
                    strcontractno_us.push(stock_code.to_string());
                }
            }

            let mut ticks: Vec<YsHqInfo> = Vec::new();

            if !strcontractno_hs.is_empty() {
                if let Ok(ret) = self.tick_client.write().await.get_latest_tick(&strcontractno_hs).await {
                    ticks.extend(ret.tick_hq_info);
                }
            }
            if !strcontractno_hk.is_empty() {
                if let Ok(ret) = self.tick_client.write().await.get_latest_tick(&strcontractno_hk).await {
                    ticks.extend(ret.tick_hq_info);
                }
            }
            if !strcontractno_us.is_empty() {
                if let Ok(ret) = self.us_client.write().await.query_us_tick(&strcontractno_us).await {
                    ticks.extend(ret);
                }
            }

            info!("tick len {}", ticks.len());

            for mut hq_info in ticks {
                // 延时行情处理
                if req.realtime == 0 && !hq_info.contract_no1.contains("XSHE") && !hq_info.contract_no1.contains("XSHG") {
                    let st = from_timestamp_to_naive_date_time(current_naive_time().and_utc().timestamp() - 15 * 60);
                    info!("code: {}, {}, {}", hq_info.contract_no1, hq_info.tapidtstamp, st);

                    if hq_info.tapidtstamp >= st.to_string() {
                        match self.get_delay_tick(&hq_info.contract_no1).await {
                            Ok(mut delay_tick) => {
                                for i in 0..delay_tick.q_bid_price.len() {
                                    delay_tick.q_bid_price[i] = 0.0;
                                    delay_tick.q_ask_price[i] = 0.0;
                                    delay_tick.q_bid_qty[i] = 0;
                                    delay_tick.q_ask_qty[i] = 0;
                                }
                                delay_tick.contract_no1 = hq_info.contract_no1.clone();
                                hq_info = delay_tick;
                            }
                            Err(e) => {
                                error!("{:?}", e);
                                continue;
                            }
                        }
                    }
                }
                // 只要一档
                if req.iticktype != 1 {
                    for i in 0..hq_info.q_bid_price.len() {
                        hq_info.q_bid_price[i] = 0.0;
                        hq_info.q_ask_price[i] = 0.0;
                        hq_info.q_bid_qty[i] = 0;
                        hq_info.q_ask_qty[i] = 0;
                    }
                }

                resp.tickhqinfo.push(hq_info);
            }
        } else {
            // 历史TICK
            let mut value = String::new();
            if let Err(e) = self.get_history_tick(&req.strcontractno, req.ticktime, &mut value).await {
                error!("{:?}", e);
            }
            info!("历史tick数据");
        }

        info!("{:?}", resp);
        Ok(resp)
    }

    pub async fn get_last_kline_data(&self, contract_no: &str, kline_type: &str) -> Result<String> {
        let kline_data = self.kline_client.write().await.get_last_kline_data(contract_no, kline_type).await?;
        if kline_data.stock_code.is_empty() {
            info!("未找到最新K线数据: {:?}", kline_data);
            return Ok(String::new());
        }

        let minutes = (kline_data.prev_minutes + kline_data.period) % 1440;
        let st = build_naive_date_time(&kline_data.tick_time);
        let time = format!("{:04}{:02}{:02}{:02}{:02}00", st.year(), st.month(), st.day(), minutes / 60, minutes % 60);
        //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
        let kline_value = format!(
            "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
            kline_data.close_price, kline_data.high_price, kline_data.last_price, kline_data.low_price, kline_data.open_price, kline_data.current_period_volume, time, kline_data.current_period_turnover
        );

        Ok(kline_value)
    }

    //转换本地时间
    pub async fn convert_to_local_time(&self, ll: i64) -> Result<i64> {
        let mut tmp = ll;
        if self.settings.common.time_diff == 0 {
            return Ok(ll);
        }
        let second = (tmp % 100) as u32; //秒
        tmp /= 100;
        let minute = (tmp % 100) as u32; //分
        tmp /= 100;
        let hour = (tmp % 100) as u32; //小时
        tmp /= 100;
        let day = (tmp % 100) as u32; //天
        tmp /= 100;
        let month = (tmp % 100) as u32; //月
        tmp /= 100;
        let year = tmp as i32; //年

        //2021-11-24 22:02:08
        let date_time = format!("{:0>4}-{:0>2}-{:0>2} {:0>2}:{:0>2}:{:0>2}", year, month, day, hour, minute, second);
        //2021-11-24 22:02:08
        let local_time = build_naive_date_time(&date_time);

        let tt = local_time.and_utc().timestamp() + (self.settings.common.time_diff * 60 * 60) as i64;

        let local_time = from_timestamp_to_naive_date_time(tt);
        let time: i64 = (local_time.year() as i64) * 10000000000 + (local_time.month() as i64) * 100000000 + (local_time.day() as i64) * 1000000 + (local_time.hour() as i64) * 10000 + (local_time.minute() as i64) * 100;
        Ok(time)
    }

    async fn convert_fs_to_local_time(&self, val: &str) -> Result<String> {
        if self.settings.common.time_diff == 0 {
            return Ok(val.to_string());
        }
        let mut value: Vec<&str> = Vec::new();
        //4.160|4.150|4.150|0|20210323093000+4.147|4.150|284|20210323093100+
        //4.148|4.160|24|20210323093200+4.148|4.150|9|20210323093400+................
        if !val.is_empty() {
            if val.contains("+") {
                value = val.split("+").collect(); //按+分割为一周期
            }
        }
        if value.is_empty() {
            return Ok(val.to_string());
        }
        let mut val_tmp = String::default();
        for i in value.iter() {
            if let Some(index) = i.rfind('|') {
                //后面开始找日期
                let update_time: i64 = i[index + 1..].to_string().parse().unwrap_or_default(); //由|分割开的最后一个值
                let ret = PhoenixHqcenterController::convert_to_local_time(&self, update_time).await;
                if ret.as_ref().is_err() {
                    error!("{:?}", &ret);
                    return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
                }
                let local_time = ret.unwrap().to_string();
                let mut str_tmp = i.trim_end_matches(&update_time.to_string()).to_string(); //删除末尾的字符串片段:4.160|4.150|4.150|0|
                str_tmp.push_str(&local_time);
                val_tmp.push_str(&str_tmp);
                val_tmp.push('+');
            }
        }
        Ok(val_tmp)
    }
}
