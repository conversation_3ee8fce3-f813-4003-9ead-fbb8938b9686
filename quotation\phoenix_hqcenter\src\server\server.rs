use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot, RwLock};
use tonic::{self, Request, Response};
use tracing::*;

use protoes::hqcenter::{
    svr_post_subscribe_hq_msg_server::SvrPostSubscribeHqMsg, KLineHqReq, KLineHqResp, LastPriceMsgReq, LastPriceMsgResp, PreDealAmount, PreDealNumReq, ResultMsg, SubscribeHqMsgReq, TickHqReq, TickHqResp,
};

use super::controller::PhoenixHqcenterController;
use crate::client::{cassandraclient::CassandraClient, klinecenterclient::KlineCenterClient, tickcenterclient::TickCenterClient, usmartclient::UsMartClient};
use crate::config::settings::Settings;

type StubType = Arc<PhoenixHqcenterController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct SvrPostSubscribeHqMsgHandler {
    stub: StubType, //行情查询控制中心
    pub settings: Settings,
    task_dispacther: mpsc::Sender<ControllerAction>, //发送控制器
    set_close: Option<oneshot::Sender<()>>,          //关闭发送通道
}

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>); //元组结构体

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap(); //尝试在此通道上发送值，如果无法发送，则返回该值。
        self.0.closed().await; //接收器断开时完成。
    }
}

impl SvrPostSubscribeHqMsgHandler {
    pub async fn new(config: &Settings) -> SvrPostSubscribeHqMsgHandler {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(60 * 60 as u64));

        //创建通道
        let (tx, mut rx) = mpsc::channel(16);
        let (tx_close, mut rx_close) = oneshot::channel();

        let settings = config;

        //创建Cassandra连接
        let cassandra_connect = CassandraClient::new(&config.cassandra).await.expect("cassandra连接出错..");

        let tick_client = TickCenterClient::new(&config.tickcenter.uri_hs, &config.tickcenter.uri_hk).await;
        let kline_client = KlineCenterClient::new(&config.klinecenter.uri_hs, &config.klinecenter.uri_hk).await;
        let us_mart = UsMartClient::new(&config.usmartcentr.uri_us).await;
        let stub = PhoenixHqcenterController {
            cassandra_client: Arc::new(cassandra_connect),
            settings: config.clone(),
            tick_client: Arc::new(RwLock::new(tick_client)),
            kline_client: Arc::new(RwLock::new(kline_client)),
            us_client: Arc::new(RwLock::new(us_mart)),
        };

        let stub = Arc::new(stub);
        let stub_for_dispatch = stub.clone();

        let ret = SvrPostSubscribeHqMsgHandler {
            stub,
            settings: settings.clone(),
            task_dispacther: tx,
            set_close: Some(tx_close),
        };

        tokio::spawn(async move {
            persist_interval.tick().await; //skip first tick
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        info!("my task received...");
                        let task = may_task.expect("Server scheduler has unexpected exit");
                        // info!("启动 controller task。====================== thread:{:?}", std::thread::current());
                        task(stub_for_dispatch.clone()).await;
                    }
                    _ = persist_interval.tick() => {
                        // let stub_rd = stub_for_dispatch.read().await;
                        info!("Start a time interval task (persist, computing):{:?}",std::thread::current());
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            //drain unhandled task
            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
                info!("drain unhandled task received");
            }

            warn!("Server scheduler has exited");
        });

        ret
    }

    #[allow(dead_code)] //Rust编译器会对未使用的函数提出警告，可以使用属性#[allow(dead_code)]禁用无效代码检查。
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

#[tonic::async_trait]
impl SvrPostSubscribeHqMsg for SvrPostSubscribeHqMsgHandler {
    //rpc PostSubscribeHqMsg(SubscribeHqMsgReq) returns (ResultMsg) {}	//行情订阅接口
    async fn post_subscribe_hq_msg(&self, request: Request<SubscribeHqMsgReq>) -> Result<Response<ResultMsg>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("行情订阅: {:#?}", &request);
        let req = request.into_inner();
        match self.stub.get_post_subscribe_hq_msg(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_err) => Ok(Response::new(ResultMsg::default())),
        }
    }

    //rpc PostHistoryKLineHq(KLineHqReq) returns(KLineHqResp){};			//历史K线请求
    async fn post_history_k_line_hq(&self, request: Request<KLineHqReq>) -> Result<Response<KLineHqResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("历史K线请求: {:#?}", &request);
        let req = request.into_inner();
        match self.stub.get_hq_kline(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_) => Ok(Response::new(KLineHqResp::default())),
        }
    }

    //rpc PostCurrentKlineHq(KLineHqReq) returns(KLineHqResp){};			//当前K线
    async fn post_current_kline_hq(&self, request: Request<KLineHqReq>) -> Result<Response<KLineHqResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("当前K线请求: {:#?}", &request);
        let req = request.into_inner();
        match self.stub.get_hq_kline(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_) => Ok(Response::new(KLineHqResp::default())),
        }
    }

    //rpc PostHistoryFenShiHq (KLineHqReq) returns(KLineHqResp){};		//历史分时
    async fn post_history_fen_shi_hq(&self, request: Request<KLineHqReq>) -> Result<Response<KLineHqResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("历史分时请求: {:#?}", &request);
        let req = request.into_inner();
        match self.stub.get_history_time_share(&req.strcontractno).await {
            Ok(val) => Ok(Response::new(val)),
            Err(_) => Ok(Response::new(KLineHqResp::default())),
        }
    }

    //rpc PostCurrentFenShiHq (KLineHqReq) returns(KLineHqResp){};		//当前分时
    async fn post_current_fen_shi_hq(&self, request: Request<KLineHqReq>) -> Result<Response<KLineHqResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("当前分时请求: {:#?}", &request);
        let req = request.into_inner();
        match self.stub.get_current_fenshi_hq(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(_) => Ok(Response::new(KLineHqResp::default())),
        }
    }

    //rpc PostTickHq(TickHqReq) returns (TickHqResp) {};				//最新TICK数据
    async fn post_tick_hq(&self, request: Request<TickHqReq>) -> Result<Response<TickHqResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("最新Tick请求: {:#?}", &request);
        let req = request.into_inner();
        match self.stub.get_tick_hq(&req).await {
            Ok(val) => Ok(Response::new(val)),
            Err(_) => Ok(Response::new(TickHqResp::default())),
        }
    }

    // rpc GetLastPrice(LastPriceMsgReq) returns (LastPriceMsgResp) {} 	//获取最新价
    async fn get_last_price(&self, request: Request<LastPriceMsgReq>) -> Result<Response<LastPriceMsgResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("最新价请求: {:#?}", &request);
        let req = request.into_inner();
        // match self.stub.get_stock_last_price(&req).await {
        match self.stub.get_tick_price(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_err) => Ok(Response::new(LastPriceMsgResp::default())),
        }
    }

    async fn get_pre_deal_amount(&self, request: Request<PreDealNumReq>) -> Result<Response<PreDealAmount>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        info!("上一分钟成交量请求: {:#?}", &request);
        let req = request.into_inner();
        match self.stub.get_pre_deal_amount(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_err) => Ok(Response::new(PreDealAmount::default())),
        }
    }
}
