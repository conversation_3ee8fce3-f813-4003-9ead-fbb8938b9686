use anyhow::Result;
use chrono::prelude::*;
use rust_decimal::prelude::*;
use tracing::*;

use protoes::hqmsg::YsHqInfo;
use utility::timeutil::{current_naive_time, from_timestamp_to_naive_date_time};

#[allow(dead_code)]
#[derive(Debug)]
pub enum KLineType
//行情类型
{
    Hq1Kline = 1,
    Hq5Kline = 5,
    Hq10Kline = 10,
    Hq30Kline = 30,
    Hq60Kline = 60,
    Hq24Kline = 1440,
    HqWeekKline = 168,
    HqMonthKline = 720,
}

#[allow(dead_code)]
#[derive(Debug, Default)]
pub struct LastPrice {
    pub last_price: f64,   //最新价
    pub change_value: f64, //涨跌值
    pub change_rate: f64,  //涨跌幅
}

#[derive(Debug, Default)]
#[allow(dead_code)]
pub struct KlineDate {
    pub vc_code: String,
    pub l_update_time: i64,
    pub en_close_price: f32,
    pub en_last_price: f32,
    pub en_high_price: f32,
    pub en_low_price: f32,
    pub en_open_price: f32,
    pub l_volume: i32,
}

// impl KlineDate {
//     pub async fn new() -> Self {
//         KlineDate { ..Default::default() }
//     }
// }

#[derive(Debug, Default)]
#[allow(dead_code)]
pub struct HqTick {
    pub vc_contract_code: String,
    pub l_update_time: String,
    pub vc_content: String,
}

impl HqTick {
    pub fn new() -> Self {
        HqTick { ..Default::default() }
    }
}

pub async fn rounding(val: f64, point: u32) -> f64 {
    Decimal::from_f64(val)
        .unwrap_or_default()
        .round_dp_with_strategy(point, RoundingStrategy::MidpointAwayFromZero)
        .to_f64()
        .unwrap_or_default()
}

//20211111150000
#[allow(dead_code)]
pub async fn convert_from_naivdate_i64(timestamp: i64) -> Result<i64> {
    let st = from_timestamp_to_naive_date_time(timestamp);
    info!("st:{}", st);
    let str = st.format("%Y%m%d%H%M").to_string(); //202111111500
    let date = str.parse::<i64>();
    if date.as_ref().is_err() {
        info!("error: {:?}", &date);
        return Err(anyhow!("{}", &date.as_ref().err().unwrap().to_string()));
    }
    Ok(date.unwrap())
}

//根据K线类型获取表名
pub async fn get_table_name_by_kline_type(table_type: i32) -> &'static str {
    match table_type {
        1 => "tstockhq1kline",
        5 => "tstockhq5kline",
        10 => "tstockhq10kline",
        30 => "tstockhq30kline",
        60 => "tstockhq60kline",
        1440 => "tstockhqdaykline",
        168 => "tstockhqweekkline",
        720 => "tstockhqmonthkline",
        _ => "", //_ => None,
    }
}

//获取当前期间
#[allow(dead_code)]
pub async fn get_current_period(period: i32, code: &str) -> i32 {
    let mut res: i32 = Default::default();
    match period {
        168 => {
            res = get_last_trade_day_of_week(code).await;
            res
        }
        720 => {
            res = get_last_trade_day_of_month(code).await;
            res
        }
        _ => res,
    }
}

//获取每周最后交易日
#[allow(dead_code)]
pub async fn get_last_trade_day_of_week(_code: &str) -> i32 {
    let mut st = current_naive_time();
    let mut tt = st.and_utc().timestamp();
    let seconds = 24 * 60 * 60;

    let diff: i64 = if st.weekday().num_days_from_sunday() == 0 { -2 } else { (5 - st.weekday().num_days_from_sunday()).into() };
    tt += diff * seconds;

    st = from_timestamp_to_naive_date_time(tt);
    let sdate = format!("{:04}{:02}{:02}", st.year(), st.month(), st.day());
    let date = sdate.parse().unwrap_or_default();
    info!("[get_last_trade_day_of_month] date:{}", date);

    // while ((CHqTick::GetInstance()->IsHoliday(date, code)) || (st.tm_wday == 0) || (st.tm_wday == 6))
    // {
    //     tt -= seconds;
    //     localtime_r(&tt, &st);
    //     sprintf(ch, "%04d%02d%02d", st.tm_year + 1900, st.tm_mon + 1, st.tm_mday);
    //     date = atoi(ch);
    // }

    date
}

//获取每月最后交易日
#[allow(dead_code)]
pub async fn get_last_trade_day_of_month(_code: &str) -> i32 {
    let mut st = current_naive_time();
    let mut tt = st.and_utc().timestamp();
    let seconds = 24 * 60 * 60;
    // let date: i32; // = Default::default();
    let month = st.month0();

    while st.month0() == month {
        tt += seconds; //add a day
        st = from_timestamp_to_naive_date_time(tt);
    }
    tt -= seconds; //last day of the month
    st = from_timestamp_to_naive_date_time(tt);
    let sdate = format!("{:04}{:02}{:02}", st.year(), st.month(), st.day());
    let date = sdate.parse().unwrap_or_default();
    info!("[get_last_trade_day_of_month] date:{}", date);
    // // while ((CHqTick::GetInstance()->IsHoliday(date, code)) || (st.tm_wday == 0) || (st.tm_wday == 6))
    // // {
    // //     tt -= seconds;
    // //     localtime_r(&tt, &st);
    // //     sprintf(ch, "%04d%02d%02d", st.tm_year + 1900, st.tm_mon + 1, st.tm_mday);
    // //     date = atoi(ch);
    // // }
    date
}

pub async fn anti_serialize_tick(ptick: &mut YsHqInfo, content: &str) {
    info!("[AntiSerialize_tick] strat content:{}", content);
    if !content.is_empty() {
        //返回true时为空
        if content.contains("|") {
            //查找字符串，找到返回true
            //按字符|分割，|1|2|  ----> ["", 1, 2, ""],共四个元素，前后两个""
            let mut value: Vec<&str> = content.split("|").collect(); //分割字符串，collect() 方法将迭代器转换为 向量 Vector
            info!("{:#?}", value);
            value.remove(0); //第一个元素为"",所以先移除

            ptick.tapidtstamp = value[0].to_string(); //时间戳
            value.remove(0);

            ptick.q_pre_closing_price = value[0].parse().unwrap_or_default(); //昨收价
            value.remove(0);

            ptick.q_average_price = value[0].parse().unwrap_or_default(); //均价
            value.remove(0);

            ptick.q_last_price = value[0].parse().unwrap_or_default(); //最新价
            value.remove(0);

            ptick.q_last_qty = value[0].parse().unwrap_or_default(); //最新成交量
            value.remove(0);

            ptick.q_total_qty = value[0].parse().unwrap_or_default(); //当日总成交量
            value.remove(0);

            ptick.q_total_turnover = value[0].parse().unwrap_or_default(); //当日成交金额
            value.remove(0);

            ptick.q_change_value = value[0].parse().unwrap_or_default(); //涨跌值
            value.remove(0);

            ptick.q_change_rate = value[0].parse().unwrap_or_default(); //涨幅
            value.remove(0);

            ptick.q_entrust_rate = value[0].parse().unwrap_or_default(); //委比
            value.remove(0);

            ptick.q_vol_ratio = value[0].parse().unwrap_or_default(); //量比
            value.remove(0);

            ptick.q_turnover_ratio = value[0].parse().unwrap_or_default(); //振幅
            value.remove(0);

            ptick.q_opening_price = value[0].parse().unwrap_or_default(); //开盘价
            value.remove(0);

            ptick.q_closing_price = value[0].parse().unwrap_or_default(); //收盘价
            value.remove(0);

            ptick.q_high_price = value[0].parse().unwrap_or_default(); //最高价
            value.remove(0);

            ptick.q_low_price = value[0].parse().unwrap_or_default(); //最低价
            value.remove(0);
            //|时间|昨收|均价|最新|现手|交易量|交易额|涨跌值|涨跌幅|委比|量比|换手率|开盘|收盘|最高|最低|
            info!(
                "|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|{}|",
                ptick.tapidtstamp,
                ptick.q_pre_closing_price,
                ptick.q_average_price,
                ptick.q_last_price,
                ptick.q_last_qty,
                ptick.q_total_qty,
                ptick.q_total_turnover,
                ptick.q_change_value,
                ptick.q_change_rate,
                ptick.q_entrust_rate,
                ptick.q_vol_ratio,
                ptick.q_turnover_ratio,
                ptick.q_opening_price,
                ptick.q_closing_price,
                ptick.q_high_price,
                ptick.q_low_price
            );

            ptick.q_circulation_amount = value[0].parse().unwrap_or_default(); //流通股
            value.remove(0);

            ptick.q_total_shares = value[0].parse().unwrap_or_default(); //总股本
            value.remove(0);

            ptick.q_market_value = value[0].parse().unwrap_or_default(); //总市值
            value.remove(0);

            ptick.q_pe_rate = value[0].parse().unwrap_or_default(); //市盈率
            value.remove(0);

            ptick.q_dyn_pb_rate = value[0].parse().unwrap_or_default(); //市净率
            value.remove(0);

            ptick.q_money_type = value[0].parse().unwrap_or_default(); //币种
            value.remove(0);
            info!(
                "|{}|{}|{}|{}|{}|{}|",
                ptick.q_circulation_amount, ptick.q_total_shares, ptick.q_market_value, ptick.q_pe_rate, ptick.q_dyn_pb_rate, ptick.q_money_type,
            );

            //买价bidprice
            value.remove(0); //移除"bidprice"
            let mut i = Default::default();
            while i < 20 {
                if value[0] == "bidqty" {
                    //下一组数据开始处
                    value.remove(0); //移除"bidqty"
                    break;
                }
                ptick.q_bid_price.push(value[0].parse().unwrap_or_default()); //Vec类型
                value.remove(0);
                i = i + 1;
            }
            info!("买价bidprice:{:?}", ptick.q_bid_price);

            //买量bidqty
            i = 0;
            while i < 20 {
                if value[0] == "askprice" {
                    //下一组数据开始处
                    value.remove(0); //移除"askprice"
                    break;
                }
                ptick.q_bid_qty.push(value[0].parse().unwrap_or_default());
                value.remove(0);
                i = i + 1;
            }
            info!("买量bidqty:{:?}", ptick.q_bid_qty);

            //卖价askprice
            i = 0;
            while i < 20 {
                if value[0] == "askqty" {
                    //下一组数据开始处
                    value.remove(0); //移除"askqty"
                    break;
                }
                ptick.q_ask_price.push(value[0].parse().unwrap_or_default());
                value.remove(0);
                i = i + 1;
            }
            info!("卖价askprice:{:?}", ptick.q_ask_price);

            //卖量askqty
            i = 0;
            while i < 20 {
                if value[0] == "" {
                    //最后一个元素为"",故作为终止点
                    value.remove(0); //移除""
                    break;
                }
                ptick.q_ask_qty.push(value[0].parse().unwrap_or_default());
                value.remove(0);
                i = i + 1;
            }
            info!("卖量askqty:{:?}", ptick.q_ask_qty);
        }
    }
}
