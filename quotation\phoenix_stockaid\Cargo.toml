[package]
name = "phoenix_stockaid"
version = "0.2.0"
edition = "2021"
description = "首页数据展示模块 build time: 2025-06-26"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
utility = { workspace = true }
common = { workspace = true }
messagecenter = { workspace = true }
protoes = { workspace = true }

config = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
lapin = { workspace = true }
tonic = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
tracing = { workspace = true }
notify = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }

anyhow = { workspace = true }
thiserror = { workspace = true }

# log = { workspace = true }

dashmap = { workspace = true }
sea-orm = { workspace = true }
futures-util = { workspace = true }
async-channel = { workspace = true }
chrono = { workspace = true }
lazy_static = { workspace = true }
tokio-stream = { workspace = true }
base64 = { workspace = true }
tokio-tungstenite = { workspace = true }
byteorder = { workspace = true }
# console-subscriber = { workspace = true }
# [build-dependencies]
# tonic-build = { workspace = true }
