// fn build_grpc() {
//     tonic_build::configure()
//         .out_dir("src/protofiles")
//         .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
//         .compile_protos(
//             &["../../protoes/StockAid.proto", "../../protoes/akacenter.proto", "../../protoes/HqMsg.proto", "../../protoes/SubscribeHqMsg.proto"],
//             &["../../protoes"],
//         )
//         .unwrap();
// }

fn main() {
    // build_grpc();
}
