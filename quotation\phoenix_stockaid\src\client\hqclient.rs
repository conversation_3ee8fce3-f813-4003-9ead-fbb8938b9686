use anyhow::{anyhow, Result};
use common::logclient::LogClient;
use protoes::hqcenter::svr_post_subscribe_hq_msg_client::SvrPostSubscribeHqMsgClient;
use protoes::hqcenter::{TickHqReq, TickHqResp};
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone)]
pub struct HqCenterClient {
    pub client: SvrPostSubscribeHqMsgClient<Channel>,
}

impl HqCenterClient {
    pub async fn new(url: &String) -> Self {
        loop {
            match SvrPostSubscribeHqMsgClient::connect(url.clone()).await {
                Ok(hqclient) => return Self { client: hqclient },
                Err(err) => {
                    error!("connect to HqCenterClient failed: {:?}", err);
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("HqCenterClient connect faild :{}", err.to_string())).await;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn post_tick_hq(&mut self, req: &TickHqReq) -> Result<TickHqResp> {
        let ret = self.client.post_tick_hq(req.to_owned()).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            if let Ok(c) = LogClient::get() {
                c.push_error(&format!("post_tick_hq error")).await;
            }
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }

        let last_price = ret.unwrap();
        Ok(last_price.into_inner())
    }
}
