use anyhow::Result;
use protoes::hqcenter::TickHqReq;
use protoes::hqmsg::YsHqInfo;
use protoes::phoenixstockaid::{DetailCodeInfo, DetailPlateInfo, ReqDetailMsg, ReqIndexOrRankMsg, ReqTrsMsg, ResultMainIndexMsg, ResultMainRankMsg, ResultPlateInfoMsg};
use std::collections::{BTreeMap, HashMap};
use std::fmt::Write;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info};

use crate::client::hqclient::HqCenterClient;
use crate::config::settings::Settings;
use crate::dataservice::dbsetup::DbConnection;
use crate::dataservice::entities::prelude::{SysCommodity, SysStockIndex};
use crate::service::stockdb;

//板块信息
#[derive(Debug, Clone)]
pub struct MainPlateInfo {
    pub plate_id: i64,
    pub plate_code: String,
    pub plate_name: String,
    //1：港股，2：美股，3：沪深
    pub market_type: i32,
    /// 1：自定义板块，2：固定板块, 3:行业板块
    pub cate: i32,
}
// enum MarketType
// {
// 	MT_HK = 1,//香港
// 	MT_US = 2,//美股
// 	MT_CN = 3,//沪深
// }
// #[derive(Clone)]
#[allow(dead_code)]
pub struct ServerController {
    pub settings: Arc<RwLock<Settings>>,
    pub finances_db: Arc<DbConnection>, //数据库连接
    pub hq_center_client: HqCenterClient,

    /// 板块信息
    pub plate_info: Arc<RwLock<HashMap<String, MainPlateInfo>>>,
    /// 指数信息
    pub index_info: Arc<RwLock<HashMap<String, SysStockIndex>>>,
    /// 股票信息
    pub stock_info: Arc<RwLock<HashMap<String, SysCommodity>>>,
    /// 板块内所有股票
    pub stock_plate: Arc<RwLock<HashMap<i64, Vec<String>>>>,
    /// 股票所属板块
    pub plate_stock: Arc<RwLock<HashMap<String, Vec<i64>>>>,
    /// 所有排序  涨跌幅（涨跌幅可能相同） key:涨跌幅
    pub stock_extent_info: Arc<RwLock<BTreeMap<i32, Vec<String>>>>,
    /// 单个板块里面排序 涨跌幅  key:板块
    pub stock_extent_info_belong_plate: Arc<RwLock<HashMap<i64, BTreeMap<i32, Vec<String>>>>>,

    pub stock_tick: Arc<RwLock<HashMap<String, YsHqInfo>>>, //所有股票行情
    pub all_stock_info: Arc<RwLock<Vec<String>>>,           //所有股票信息副本,轮询用
}

//处理业务逻辑
impl ServerController {
    pub async fn init_controller(&mut self) -> Result<()> {
        //查询所有品种组分类表
        // let trade_list = stockdb::query_sys_trade_config_commodity(&self.finances_db).await;
        // for val in trade_list.iter() {
        //     let ret = stockdb::query_inner_code(&self.finances_db, val.commodity_id).await;
        //     if let Ok(stock_info) = ret {
        //         self.stock_info.write().await.insert(stock_info.inner_code.clone(), stock_info.clone());
        //         self.stock_tick.write().await.insert(stock_info.inner_code.clone(), YsHqInfo::default());
        //         let mut all_stock_info = self.all_stock_info.write().await;

        //         if !all_stock_info.contains(&stock_info.inner_code) {
        //             all_stock_info.push(stock_info.inner_code.clone());
        //         }
        //     }
        // }

        let trade_list = stockdb::query_inner_code_and_type(&self.finances_db).await;
        for stock_info in trade_list.iter() {
            self.stock_info.write().await.insert(stock_info.inner_code.clone(), stock_info.clone());
            self.stock_tick.write().await.insert(stock_info.inner_code.clone(), YsHqInfo::default());
            let mut all_stock_info = self.all_stock_info.write().await;

            if !all_stock_info.contains(&stock_info.inner_code) {
                all_stock_info.push(stock_info.inner_code.clone());
            }
        }

        //查询指数
        let index_list = stockdb::query_index(&self.finances_db).await;
        for val in index_list.iter() {
            self.stock_tick.write().await.insert(val.inner_code.clone(), YsHqInfo::default());
            self.index_info.write().await.insert(val.inner_code.clone(), val.clone());
            let mut all_stock_info = self.all_stock_info.write().await;
            if !all_stock_info.contains(&val.inner_code) {
                all_stock_info.push(val.inner_code.clone());
            }
        }

        //多表联查 确定板块内有哪些股票代码
        let plate_list = stockdb::query_plate(&self.finances_db).await;
        {
            let mut plate_info = self.plate_info.write().await;
            let mut stock_plate = self.stock_plate.write().await;
            let mut plate_stock = self.plate_stock.write().await;
            let mut all_stock_info = self.all_stock_info.write().await;
            let mut stock_tick = self.stock_tick.write().await;
            info!("plate_list: {:#?}", plate_list);

            for val in plate_list.iter() {
                let mp = MainPlateInfo {
                    plate_id: val.id,
                    plate_code: val.plate_code.clone(),
                    plate_name: val.name.clone(),
                    market_type: val.market_type,
                    cate: val.cate,
                };

                plate_info.insert(val.plate_code.clone(), mp.clone());
                match stock_plate.get_mut(&val.id) {
                    Some(v) => {
                        if !v.contains(&val.stock_code) {
                            v.push(val.stock_code.clone());
                        }
                    }
                    None => {
                        stock_plate.insert(val.id, vec![val.stock_code.clone()]);
                    }
                }

                match plate_stock.get_mut(&val.stock_code) {
                    Some(v) => {
                        if !v.contains(&val.id) {
                            v.push(val.id);
                        }
                    }
                    None => {
                        plate_stock.insert(val.stock_code.clone(), vec![val.id]);
                    }
                }

                //不存在则写入
                if !all_stock_info.contains(&val.plate_code) {
                    all_stock_info.push(val.plate_code.clone());
                }
                if !all_stock_info.contains(&val.stock_code) {
                    all_stock_info.push(val.stock_code.clone());
                }
                stock_tick.insert(val.plate_code.clone(), YsHqInfo::default());
                stock_tick.insert(val.stock_code.clone(), YsHqInfo::default());
            }
        }
        info!("all_stock_info: {:#?}", self.all_stock_info.read().await);

        let mut stock_extent_info_belong_plate = self.stock_extent_info_belong_plate.write().await;
        let stock_plate = self.stock_plate.write().await;

        for (plate_id, stock_codes) in stock_plate.iter() {
            match stock_extent_info_belong_plate.get_mut(plate_id) {
                Some(sp) => {
                    let em = 0;
                    match sp.get_mut(&em) {
                        None => {
                            sp.insert(0, stock_codes.to_vec());
                        }
                        Some(btree_list) => {
                            for stock_code in stock_codes.iter() {
                                if !btree_list.contains(&stock_code) {
                                    btree_list.push(stock_code.clone());
                                }
                            }
                        }
                    }
                }
                None => {
                    let mut bt_map = BTreeMap::new();
                    bt_map.insert(0, stock_codes.to_vec());
                    stock_extent_info_belong_plate.insert(*plate_id, bt_map.clone());
                }
            }
        }
        drop(stock_plate);
        drop(stock_extent_info_belong_plate);

        let all_stock_info = self.all_stock_info.read().await;
        let mut req = TickHqReq {
            strcontractno: "".to_string(),
            iticktype: 0,
            ticktime: 0,
            realtime: 1,
        };

        let dst: Vec<Vec<String>> = all_stock_info.chunks(32).map(|s| s.into()).collect();
        let mut c = self.hq_center_client.clone();

        for val in dst.iter() {
            for v in val.iter() {
                let _ = write!(req.strcontractno, "{},", v);
            }
            let _ = self.get_hq(&req, &mut c).await;
            req.strcontractno = "".to_string();
        }

        info!("{:#?}", self.stock_extent_info.read().await);
        info!("{:#?}", self.stock_extent_info_belong_plate.read().await);
        Ok(())
    }

    pub async fn process_hq_info(&self, stock_tick: &mut HashMap<String, YsHqInfo>, hq_info: &YsHqInfo) -> Result<()> {
        let change_rate = (hq_info.q_change_rate * 100.0) as i32;
        let mut pre_change_rate = 0;

        match stock_tick.get_mut(&hq_info.contract_no1) {
            None => {
                stock_tick.insert(hq_info.contract_no1.clone(), hq_info.clone());
            }
            Some(hq_val) => {
                pre_change_rate = (hq_val.q_change_rate * 100.0) as i32;
                *hq_val = hq_info.clone();
            }
        }

        let _ = self.deal_plate_sort(&hq_info.contract_no1, change_rate, pre_change_rate).await;
        let _ = self.deal_all_stock_sort(&hq_info.contract_no1, change_rate, pre_change_rate).await;
        // let _ = self.deal_cache(&hq_info).await;
        // stock_tick.insert(hq_info.contract_no1.clone(), hq_info.clone());

        Ok(())
    }

    pub async fn get_hq(&self, req: &TickHqReq, hq_client: &mut HqCenterClient) -> Result<()> {
        info!("{:?}", req);
        let mut stock_tick = self.stock_tick.write().await;

        if let Ok(ret_val) = hq_client.post_tick_hq(&req).await {
            if ret_val.tickhqinfo.len() != 0 {
                for hq_info in ret_val.tickhqinfo.iter() {
                    self.process_hq_info(&mut stock_tick, &hq_info).await?;
                }
            } else {
                let str_list: Vec<&str> = req.strcontractno.split(",").collect();
                for val in str_list.iter() {
                    let req = TickHqReq {
                        strcontractno: val.to_string().clone(),
                        iticktype: 0,
                        ticktime: 0,
                        realtime: 1,
                    };
                    let ret = hq_client.post_tick_hq(&req).await;
                    if let Ok(ret_val) = ret {
                        if ret_val.tickhqinfo.len() != 0 {
                            let hq_info = ret_val.tickhqinfo[0].clone();
                            self.process_hq_info(&mut stock_tick, &hq_info).await?;
                        }
                    }
                }
            }
        } else {
            info!("没有查询到hq信息 stock: {:?}", req.strcontractno);
        }

        Ok(())
    }

    pub async fn update_us(&self) -> Result<Vec<String>> {
        let mut list = Vec::new();
        let plate_info = self.plate_info.read().await;
        for (_plate_code, info) in plate_info.iter() {
            if info.market_type == 2 {
                let pl_info = DetailPlateInfo {
                    id: info.plate_id as i32,
                    code: info.plate_code.clone(),
                    name: info.plate_name.clone(),
                    count: 5,
                    stock_infos: vec![],
                };
                if info.plate_code == "ZG0001_US".to_string() {
                    continue;
                }
                match self.stock_plate.read().await.get(&(pl_info.id as i64)) {
                    None => {
                        info!("信息不完整");
                    }
                    Some(stock_list) => {
                        for value in stock_list.iter() {
                            list.push(value.to_string());
                        }
                    }
                }
            }
        }

        Ok(list)
    }

    pub async fn deal_plate_sort(&self, stock_code: &String, change_rate: i32, pre_change_rate: i32) -> Result<()> {
        let mut stock_extent_info_belong_plate = self.stock_extent_info_belong_plate.write().await;
        //股票所属板块
        let plate_stock = self.plate_stock.read().await;

        match plate_stock.get(stock_code) {
            Some(plate_codes) => {
                //获取板块id
                for plate_code in plate_codes.iter() {
                    match stock_extent_info_belong_plate.get_mut(plate_code) {
                        Some(sp) => {
                            //取板块涨跌幅排序
                            //将前一次涨跌幅清掉
                            match sp.get_mut(&pre_change_rate) {
                                None => {}
                                Some(btree_list) => {
                                    if let Some(index) = btree_list.iter().position(|x| x == stock_code) {
                                        btree_list.remove(index);
                                    }
                                }
                            }

                            //使用新的涨跌幅
                            match sp.get_mut(&change_rate) {
                                None => {
                                    let mut new_list = Vec::new();
                                    new_list.push(stock_code.clone());
                                    sp.insert(change_rate, new_list);
                                }
                                Some(btree_list) => {
                                    btree_list.push(stock_code.clone());
                                }
                            }
                        }
                        None => {
                            let mut bt_map = BTreeMap::new();
                            let mut bt_list = Vec::new();
                            bt_list.push(stock_code.clone());
                            bt_map.insert(change_rate, bt_list);
                            stock_extent_info_belong_plate.insert(plate_code.clone(), bt_map.clone());
                        }
                    }
                }
            }
            None => {}
        }
        Ok(())
    }

    pub async fn deal_all_stock_sort(&self, stock_code: &String, change_rate: i32, pre_change_rate: i32) -> Result<()> {
        let mut stock_extent_info = self.stock_extent_info.write().await;

        match stock_extent_info.get_mut(&pre_change_rate) {
            None => {}
            Some(btree_list) => {
                if let Some(index) = btree_list.iter().position(|x| x == stock_code) {
                    btree_list.remove(index);
                }
            }
        }
        match stock_extent_info.get_mut(&change_rate) {
            None => {
                let mut new_list = Vec::new();
                new_list.push(stock_code.clone());
                stock_extent_info.insert(change_rate, new_list);
            }
            Some(btree_list) => {
                btree_list.push(stock_code.clone());
            }
        }
        Ok(())
    }

    #[allow(dead_code)]
    pub async fn deal_cache(&self, tick: &YsHqInfo) -> Result<()> {
        self.stock_tick.write().await.insert(tick.contract_no1.clone(), tick.clone());

        Ok(())
    }

    /// 获取首页指数
    pub async fn post_main_index_msg(&self, in_stream: ReqIndexOrRankMsg) -> Result<ResultMainIndexMsg> {
        let mut res = ResultMainIndexMsg { ..Default::default() };
        let index_info = self.index_info.read().await;
        let stock_tick = self.stock_tick.read().await;

        for (index_code, index_info) in index_info.iter() {
            if index_info.r#type == in_stream.market_type {
                match stock_tick.get(index_code) {
                    None => {
                        info!("not found index info ")
                    }
                    Some(val) => {
                        // info!("{:?}", val);
                        let mut ci = DetailCodeInfo {
                            code: index_code.clone(),
                            last_price: val.q_last_price,
                            change_value: val.q_change_value,
                            change_rate: val.q_change_rate,
                            top_stock: "".to_string(),
                        };
                        if val.contract_no1.is_empty() {
                            let req = TickHqReq {
                                strcontractno: index_code.clone(),
                                iticktype: 0,
                                ticktime: 0,
                                realtime: 1,
                            };
                            let mut client = self.hq_center_client.clone();
                            match client.post_tick_hq(&req).await {
                                Ok(ret_val) if !ret_val.tickhqinfo.is_empty() => {
                                    let hq_info = &ret_val.tickhqinfo[0];
                                    ci.last_price = hq_info.q_last_price;
                                    ci.change_value = hq_info.q_change_value;
                                    ci.change_rate = hq_info.q_change_rate;
                                }
                                Ok(_) => {
                                    info!("没有查询到hq信息 stock: {:?}", index_code);
                                }
                                Err(e) => {
                                    error!("查询hq信息失败 stock: {:?}, 错误: {:?}", index_code, e);
                                }
                            }
                        }

                        res.index_infos.push(ci);
                    }
                }
            }
        }
        Ok(res)
    }

    /// 获取首页热门排行榜
    pub async fn post_main_rank_msg(&self, in_stream: ReqIndexOrRankMsg) -> Result<ResultMainRankMsg> {
        let mut res = ResultMainRankMsg { ..Default::default() };
        let num = if in_stream.count == 0 { 3 } else { in_stream.count };
        let mut p_count = num;
        let plate_info = self.plate_info.read().await;
        let stock_extent_info = self.stock_extent_info.read().await;
        let index_info = self.index_info.read().await;
        let stock_info = self.stock_info.read().await;
        let stock_tick = self.stock_tick.read().await;
        // 首页板块排行（取3个热门板块）
        // 板块涨跌幅排序
        for (_change_rate, stock_list) in stock_extent_info.iter().rev() {
            if p_count == 0 {
                break;
            }
            for stock_code in stock_list.iter() {
                if p_count == 0 {
                    break;
                }
                match plate_info.get(stock_code) {
                    Some(plate) => {
                        if plate.market_type == in_stream.market_type && plate.cate == 3 {
                            let mut pl_info = DetailCodeInfo {
                                code: plate.plate_code.to_string(),
                                last_price: 0.0,
                                change_value: 0.0,
                                change_rate: 0.0,
                                top_stock: "".to_string(),
                            };
                            if let Some(s_info_copy) = self.stock_tick.read().await.get(&plate.plate_code) {
                                pl_info.change_rate = s_info_copy.q_change_rate;
                                pl_info.last_price = s_info_copy.q_last_price;
                                pl_info.change_value = s_info_copy.q_change_value;
                            }
                            // 领涨股
                            match self.stock_extent_info_belong_plate.read().await.get(&plate.plate_id) {
                                None => {
                                    info!("信息不完整");
                                }
                                Some(bt_map) => {
                                    for value in bt_map.iter() {
                                        if !value.1.is_empty() {
                                            pl_info.top_stock = value.1[0].clone();
                                        }
                                    }
                                }
                            }
                            res.main_plate.push(pl_info.clone());
                            p_count -= 1;
                        }
                    }
                    None => continue,
                }
            }
        }

        if in_stream.market_type != 2 {
            //涨幅榜
            let mut pl_info = DetailPlateInfo {
                id: 0,
                code: "19999".to_string(),
                name: "涨幅榜".to_string(),
                count: 0,
                stock_infos: vec![],
            };
            let mut count = 5;
            for stock in stock_extent_info.iter().rev().map(|x| x.1.to_owned()) {
                if count == 0 {
                    break;
                }
                for stock_code in stock.iter() {
                    if count == 0 {
                        break;
                    }
                    info!("{:#?}", stock_code);
                    let mut s_info = DetailCodeInfo {
                        code: "".to_string(),
                        last_price: 0.0,
                        change_value: 0.0,
                        change_rate: 0.0,
                        top_stock: "".to_string(),
                    };
                    if plate_info.contains_key(stock_code) || index_info.contains_key(stock_code) {
                        continue;
                    }
                    match stock_info.get(stock_code) {
                        None => {
                            continue;
                        }
                        Some(info) => {
                            if info.r#type != in_stream.market_type {
                                continue;
                            }
                        }
                    }
                    match stock_tick.get(stock_code.clone().as_str()) {
                        None => {
                            continue;
                        }
                        Some(hq_val) => {
                            s_info.code = stock_code.clone();
                            s_info.last_price = hq_val.q_last_price;
                            s_info.change_rate = hq_val.q_change_rate;
                            s_info.change_value = hq_val.q_change_value;
                        }
                    }
                    pl_info.stock_infos.push(s_info);
                    count -= 1;
                }
            }

            res.hot_plate.push(pl_info);

            //跌幅榜
            let mut pl_info = DetailPlateInfo {
                id: 0,
                code: "29999".to_string(),
                name: "跌幅榜".to_string(),
                count: 0,
                stock_infos: vec![],
            };
            count = 5;
            for stock in stock_extent_info.iter().map(|x| x.1.to_owned()) {
                if count == 0 {
                    break;
                }
                for stock_code in stock.iter() {
                    if count == 0 {
                        break;
                    }
                    let mut s_info = DetailCodeInfo {
                        code: "".to_string(),
                        last_price: 0.0,
                        change_value: 0.0,
                        change_rate: 0.0,
                        top_stock: "".to_string(),
                    };
                    if plate_info.contains_key(stock_code) || index_info.contains_key(stock_code) {
                        continue;
                    }
                    match stock_info.get(stock_code) {
                        None => {
                            continue;
                        }
                        Some(info) => {
                            if info.r#type != in_stream.market_type {
                                continue;
                            }
                        }
                    }
                    match stock_tick.get(stock_code.clone().as_str()) {
                        None => {
                            continue;
                        }
                        Some(hq_val) => {
                            s_info.code = stock_code.clone();
                            s_info.last_price = hq_val.q_last_price;
                            s_info.change_rate = hq_val.q_change_rate;
                            s_info.change_value = hq_val.q_change_value;
                        }
                    }
                    pl_info.stock_infos.push(s_info);
                    count -= 1;
                }
            }

            res.hot_plate.push(pl_info);
        }
        //     let mut count = 5;
        //     for (plate_code, info) in plate_info.iter() {
        //         if info.market_type == in_stream.market_type {
        //             let mut pl_info = DetailPlateInfo {
        //                 id: info.plate_id as i32,
        //                 code: info.plate_code.clone(),
        //                 name: info.plate_name.clone(),
        //                 count: 5,
        //                 stock_infos: vec![],
        //             };
        //             match self.stock_extent_info_belong_plate.read().await.get(&(pl_info.id as i64)) {
        //                 None => {
        //                     info!("信息不完整");
        //                 }
        //                 Some(bt_map) => {
        //                     for value in bt_map.iter().rev() {
        //                         for vec_val in value.1 {
        //                             if count == 0 {
        //                                 break;
        //                             }
        //                             let mut s_info = DetailCodeInfo {
        //                                 code: vec_val.clone(),
        //                                 ..Default::default()
        //                             };
        //                             if let Some(s_info_copy) = self.stock_tick.read().await.get(vec_val) {
        //                                 s_info.last_price = s_info_copy.q_last_price;
        //                                 s_info.change_value = s_info_copy.q_change_value;
        //                                 s_info.change_rate = s_info_copy.q_change_rate;
        //                             }
        //                             pl_info.stock_infos.push(s_info);
        //                             count -= 1;
        //                         }
        //                     }
        //                 }
        //             }
        //             res.hot_plate.push(pl_info);
        //         }
        //     }
        // } else if in_stream.market_type == 2 {
        //     for (plate_code, info) in plate_info.iter() {
        //         if info.market_type == in_stream.market_type {
        //             let mut pl_info = DetailPlateInfo {
        //                 id: info.plate_id as i32,
        //                 code: info.plate_code.clone(),
        //                 name: info.plate_name.clone(),
        //                 count: 5,
        //                 stock_infos: vec![],
        //             };
        //             if info.plate_code == "ZG0001_US".to_string() {
        //                 continue;
        //             }
        //             let mut count = 5;
        //             match self.stock_plate.read().await.get(&info.plate_id) {
        //                 None => {
        //                     info!("信息不完整");
        //                 }
        //                 Some(stock_list) => {
        //                     match stock_list.iter().find(|x| **x == "LGHL_XNAS".to_string()) {
        //                         None => {}
        //                         Some(val) => {
        //                             if let Some(s_info_copy) = self.stock_tick.read().await.get(val) {
        //                                 let s_info = DetailCodeInfo {
        //                                     code: val.clone(),
        //                                     last_price: s_info_copy.q_last_price,
        //                                     change_value: s_info_copy.q_change_value,
        //                                     change_rate: s_info_copy.q_change_rate,
        //                                     top_stock: "".to_string(),
        //                                 };
        //                                 pl_info.stock_infos.push(s_info);
        //                                 count -= 1;
        //                             }
        //                         }
        //                     }

        //                     for value in stock_list.iter() {
        //                         if count == 0 {
        //                             break;
        //                         }
        //                         if value == "LGHL_XNAS".to_string().as_str() {
        //                             continue;
        //                         }
        //                         let mut s_info = DetailCodeInfo {
        //                             code: value.clone(),
        //                             last_price: 0.0,
        //                             change_value: 0.0,
        //                             change_rate: 0.0,
        //                             top_stock: "".to_string(),
        //                         };
        //                         if let Some(s_info_copy) = self.stock_tick.read().await.get(value) {
        //                             s_info.last_price = s_info_copy.q_last_price;
        //                             s_info.change_value = s_info_copy.q_change_value;
        //                             s_info.change_rate = s_info_copy.q_change_rate;
        //                         }
        //                         pl_info.stock_infos.push(s_info);
        //                         count -= 1;
        //                     }
        //                 }
        //             }
        //             res.hot_plate.push(pl_info);
        //         }
        //     }
        // }

        Ok(res)
    }

    /// 获取板块数据
    pub async fn post_plate_msg(&self, in_stream: ReqDetailMsg) -> Result<ResultPlateInfoMsg> {
        let mut res = ResultPlateInfoMsg { ..Default::default() };
        let plate_infos = self.plate_info.read().await;
        let mut count = in_stream.count;
        let stock_extent_info = self.stock_extent_info.read().await;
        let index_info = self.index_info.read().await;
        let stock_info = self.stock_info.read().await;
        let stock_tick = self.stock_tick.read().await;
        //查询热门板块
        if in_stream.info_type == 2 {
            for stock in stock_extent_info.iter().rev().map(|x| x.1.to_owned()) {
                if count == 0 {
                    break;
                }
                for stock_code in stock.iter() {
                    if count == 0 {
                        break;
                    }
                    match plate_infos.get(stock_code) {
                        Some(plate) => {
                            if plate.market_type == in_stream.market_type && plate.cate == 3 {
                                let mut pl_info = DetailCodeInfo {
                                    code: plate.plate_code.to_string(),
                                    last_price: 0.0,
                                    change_value: 0.0,
                                    change_rate: 0.0,
                                    top_stock: "".to_string(),
                                };
                                if let Some(s_info_copy) = stock_tick.get(&plate.plate_code) {
                                    pl_info.change_rate = s_info_copy.q_change_rate;
                                    pl_info.last_price = s_info_copy.q_last_price;
                                    pl_info.change_value = s_info_copy.q_change_value;
                                }
                                // 领涨股
                                match self.stock_extent_info_belong_plate.read().await.get(&plate.plate_id) {
                                    None => {
                                        info!("信息不完整");
                                    }
                                    Some(bt_map) => {
                                        for value in bt_map.iter() {
                                            if !value.1.is_empty() {
                                                pl_info.top_stock = value.1[0].clone();
                                            }
                                        }
                                    }
                                }
                                res.plate_infos.push(pl_info.clone());
                                count -= 1;
                            }
                        }
                        None => continue,
                    }
                }
            }
        } else if in_stream.info_type == 3 && in_stream.plate_code != 0 {
            match in_stream.plate_code {
                19999 => {
                    for stock in stock_extent_info.iter().rev().map(|x| x.1.to_owned()) {
                        if count == 0 {
                            break;
                        }
                        for stock_code in stock.iter() {
                            if count == 0 {
                                break;
                            }
                            let mut pl_info = DetailCodeInfo {
                                code: "".to_string(),
                                last_price: 0.0,
                                change_value: 0.0,
                                change_rate: 0.0,
                                top_stock: "".to_string(),
                            };
                            if plate_infos.contains_key(stock_code) || index_info.contains_key(stock_code) {
                                continue;
                            }
                            match stock_info.get(stock_code) {
                                None => {
                                    continue;
                                }
                                Some(info) => {
                                    if info.r#type != in_stream.market_type {
                                        continue;
                                    }
                                }
                            }
                            match stock_tick.get(stock_code.clone().as_str()) {
                                None => {
                                    continue;
                                }
                                Some(hq_val) => {
                                    pl_info.code = stock_code.clone();
                                    pl_info.last_price = hq_val.q_last_price;
                                    pl_info.change_rate = hq_val.q_change_rate;
                                    pl_info.change_value = hq_val.q_change_value;
                                }
                            }
                            res.plate_infos.push(pl_info);
                            count -= 1;
                        }
                    }
                }
                29999 => {
                    for stock in stock_extent_info.iter().map(|x| x.1.to_owned()) {
                        if count == 0 {
                            break;
                        }
                        for stock_code in stock.iter() {
                            if count == 0 {
                                break;
                            }
                            let mut pl_info = DetailCodeInfo {
                                code: "".to_string(),
                                last_price: 0.0,
                                change_value: 0.0,
                                change_rate: 0.0,
                                top_stock: "".to_string(),
                            };
                            if plate_infos.contains_key(stock_code) || index_info.contains_key(stock_code) {
                                continue;
                            }
                            match stock_info.get(stock_code) {
                                None => {
                                    continue;
                                }
                                Some(info) => {
                                    if info.r#type != in_stream.market_type {
                                        continue;
                                    }
                                }
                            }
                            match stock_tick.get(stock_code.clone().as_str()) {
                                None => {
                                    continue;
                                }
                                Some(hq_val) => {
                                    pl_info.code = stock_code.clone();
                                    pl_info.last_price = hq_val.q_last_price;
                                    pl_info.change_rate = hq_val.q_change_rate;
                                    pl_info.change_value = hq_val.q_change_value;
                                }
                            }
                            res.plate_infos.push(pl_info);
                            count -= 1;
                        }
                    }
                }
                _ => {
                    if let Some(stock_info) = self.stock_extent_info_belong_plate.read().await.get(&(in_stream.plate_code as i64)) {
                        for si in stock_info.iter().rev() {
                            if count == 0 {
                                break;
                            }
                            for s_c in si.1.iter() {
                                if count == 0 {
                                    break;
                                }
                                if let Some(hq_val) = stock_tick.get(s_c.clone().as_str()) {
                                    let pl_info = DetailCodeInfo {
                                        code: s_c.clone(),
                                        last_price: hq_val.q_last_price,
                                        change_rate: hq_val.q_change_rate,
                                        change_value: hq_val.q_change_value,
                                        top_stock: "".to_string(),
                                    };
                                    res.plate_infos.push(pl_info);
                                    count -= 1;
                                } else {
                                    info!("not found stock hq info for stock_code: {}", s_c);
                                    continue;
                                }
                            }
                        }
                    } else {
                        info!("not found in stock_extent_info_belong_plate for plate_code: {}", in_stream.plate_code);
                    }
                }
            }
        }
        Ok(res)
    }

    pub async fn post_trs_info_msg(&self, in_stream: ReqTrsMsg) -> Result<ResultPlateInfoMsg> {
        let mut res = ResultPlateInfoMsg { ..Default::default() };
        let count = in_stream.count;
        if in_stream.order_type == 0 {
            let list = self.stock_tick.read().await;
            let mut bt_map: BTreeMap<i32, Vec<String>> = BTreeMap::new();
            for (_k, val) in list.iter() {
                match bt_map.get_mut(&((val.q_last_price * 100.0) as i32)) {
                    None => {
                        let mut new_list = Vec::new();
                        new_list.push(val.contract_no1.clone());
                        bt_map.insert((val.q_last_price * 100.0) as i32, new_list);
                    }
                    Some(btree_list) => {
                        btree_list.push(val.contract_no1.clone());
                    }
                }
            }
            let ret_list = self.get_price(bt_map, count, in_stream.order).await;
            let mut ret = ret_list.clone();
            info!("{:?}", ret);
            res.plate_infos.append(&mut ret);
        } else {
            //涨跌幅
            let list = self.stock_extent_info.clone();
            if in_stream.order {
                let ret_list = self.get_rev_list(list, count).await;
                let mut ret = ret_list.clone();
                info!("{:?}", ret);
                res.plate_infos.append(&mut ret);
            } else {
                let ret_list = self.get_list(list, count).await;
                let mut ret = ret_list.clone();
                info!("{:?}", ret);
                res.plate_infos.append(&mut ret);
            }
        }
        Ok(res)
    }

    pub async fn get_list(&self, list: Arc<RwLock<BTreeMap<i32, Vec<String>>>>, mut count: i32) -> Vec<DetailCodeInfo> {
        let mut pl_list = Vec::new();
        let list_s: Vec<_> = list.read().await.iter().map(|x| x.1.to_owned()).collect();
        let index_info = self.index_info.read().await;
        for val in list_s.iter() {
            if count == 0 {
                break;
            }
            for vec_val in val {
                if count == 0 {
                    break;
                }
                let mut pl_info = DetailCodeInfo {
                    code: "".to_string(),
                    last_price: 0.0,
                    change_value: 0.0,
                    change_rate: 0.0,
                    top_stock: "".to_string(),
                };
                if self.all_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                    continue;
                }

                for (_k, _val) in index_info.iter() {
                    if _k == vec_val {
                        continue;
                    }
                }

                match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                    None => {
                        info!("not found stock hq info");
                    }
                    Some(hq_val) => {
                        pl_info.code = vec_val.clone();
                        pl_info.last_price = hq_val.q_last_price;
                        pl_info.change_rate = hq_val.q_change_rate;
                        pl_info.change_value = hq_val.q_change_value;
                        pl_list.push(pl_info.clone());
                        count -= 1;
                    }
                }
            }
        }
        return pl_list;
    }

    pub async fn get_rev_list(&self, list: Arc<RwLock<BTreeMap<i32, Vec<String>>>>, mut count: i32) -> Vec<DetailCodeInfo> {
        let mut pl_list = Vec::new();
        let index_info = self.index_info.read().await;
        let list_s: Vec<_> = list.read().await.iter().rev().map(|x| x.1.to_owned()).collect();
        for val in list_s {
            if count == 0 {
                break;
            }
            for vec_val in val {
                if count == 0 {
                    break;
                }
                let mut pl_info = DetailCodeInfo {
                    code: "".to_string(),
                    last_price: 0.0,
                    change_value: 0.0,
                    change_rate: 0.0,
                    top_stock: "".to_string(),
                };
                if self.all_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                    continue;
                }
                let mut flag = false;
                for (_k, _) in index_info.iter() {
                    if *_k == vec_val {
                        flag = true;
                        break;
                    }
                }
                if flag {
                    continue;
                }
                match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                    None => {}
                    Some(hq_val) => {
                        pl_info.code = vec_val.clone();
                        pl_info.last_price = hq_val.q_last_price;
                        pl_info.change_rate = hq_val.q_change_rate;
                        pl_info.change_value = hq_val.q_change_value;
                        pl_list.push(pl_info.clone());
                        count -= 1;
                    }
                }
            }
        }
        return pl_list;
    }

    pub async fn get_price(&self, list: BTreeMap<i32, Vec<String>>, mut count: i32, x: bool) -> Vec<DetailCodeInfo> {
        let mut pl_list = Vec::new();
        let index_info = self.index_info.read().await;
        if x {
            for val in list.iter().rev() {
                if count == 0 {
                    break;
                }
                for vec_val in val.1 {
                    if count == 0 {
                        break;
                    }
                    let mut pl_info = DetailCodeInfo {
                        code: "".to_string(),
                        last_price: 0.0,
                        change_value: 0.0,
                        change_rate: 0.0,
                        top_stock: "".to_string(),
                    };
                    if self.all_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                        continue;
                    }
                    let mut flag = false;
                    for (_k, _) in index_info.iter() {
                        if _k == vec_val {
                            flag = true;
                            break;
                        }
                    }
                    if flag {
                        continue;
                    }
                    match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                        None => {
                            info!("not found stock hq info");
                        }
                        Some(hq_val) => {
                            pl_info.code = vec_val.clone();
                            pl_info.last_price = hq_val.q_last_price;
                            pl_info.change_rate = hq_val.q_change_rate;
                            pl_info.change_value = hq_val.q_change_value;
                            pl_list.push(pl_info.clone());
                            count -= 1;
                        }
                    }
                }
            }
        } else {
            for val in list.iter() {
                if count == 0 {
                    break;
                }
                for vec_val in val.1 {
                    if count == 0 {
                        break;
                    }
                    let mut pl_info = DetailCodeInfo {
                        code: "".to_string(),
                        last_price: 0.0,
                        change_value: 0.0,
                        change_rate: 0.0,
                        top_stock: "".to_string(),
                    };
                    if self.all_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                        continue;
                    }
                    let mut flag = false;
                    for (_k, _val) in index_info.iter() {
                        if _k == vec_val {
                            flag = true;
                            break;
                        }
                    }
                    if flag {
                        continue;
                    }
                    match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                        None => {
                            info!("not found stock hq info");
                        }
                        Some(hq_val) => {
                            pl_info.code = vec_val.clone();
                            pl_info.last_price = hq_val.q_last_price;
                            pl_info.change_rate = hq_val.q_change_rate;
                            pl_info.change_value = hq_val.q_change_value;
                            pl_list.push(pl_info.clone());
                            count -= 1;
                        }
                    }
                }
            }
        }

        return pl_list;
    }
}
