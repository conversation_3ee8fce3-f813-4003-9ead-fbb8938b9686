use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{many_table_query, prelude::*},
};
use anyhow::{anyhow, Result};
use common::constant;
use sea_orm::{ActiveModelTrait, ActiveValue::NotSet, ColumnTrait, EntityTrait, QueryFilter};
use serde_json::json;

impl ManyTableQuery {
    pub async fn query(db: &DbConnection) -> Result<Vec<ManyTableQuery>> {
        let query = Query::select()
            .from(SysCommodityGroupListEntity)
            .from(SysCommodityEntity)
            .from(SysCommodityGroupEntity)
            .expr_as(Expr::tbl(SysCommodityEntity, sys_commodity::Column::InnerCode), Alias::new("stock_code"))
            .expr_as(Expr::tbl(SysCommodityGroupEntity, sys_commodity_group::Column::Id), Alias::new("id"))
            .expr_as(Expr::tbl(SysCommodityGroupEntity, sys_commodity_group::Column::Type), Alias::new("market_type"))
            .expr_as(Expr::tbl(SysCommodityEntity, sys_commodity::Column::MarketId), Alias::new("market_id"))
            .expr_as(Expr::tbl(SysCommodityGroupEntity, sys_commodity_group::Column::InnerCode), Alias::new("inner_code"))
            .expr_as(Expr::tbl(SysCommodityGroupEntity, sys_commodity_group::Column::Cate), Alias::new("cate"))
            .expr_as(Expr::tbl(SysCommodityGroupEntity, sys_commodity_group::Column::Name), Alias::new("name"))
            .and_where(Expr::tbl(SysCommodityGroupListEntity, sys_commodity_group_list::Column::CommodityId).equals(SysCommodityEntity, sys_commodity::Column::Id))
            .and_where(Expr::tbl(SysCommodityGroupListEntity, sys_commodity_group_list::Column::GroupId).equals(SysCommodityGroupEntity, sys_commodity_group::Column::Id))
            .and_where(Expr::tbl(SysCommodityGroupEntity, sys_commodity_group::Column::Status).eq(1))
            .and_where(Expr::tbl(SysCommodityEntity, sys_commodity::Column::Status).eq(1))
            .and_where(Expr::tbl(SysCommodityEntity, sys_commodity::Column::ShowState).eq(1))
            .to_owned();
        let attributes = AttributeMeta::find_by_statement(Statement::from_string(DatabaseBackend::MySql, query.to_string(MysqlQueryBuilder))).all(&dbc).await;
        let ret: Vec<AttributeMeta> = attributes.unwrap();
        for val in ret {
            print!("{:?}", val);
            break;
        }
    }
}
