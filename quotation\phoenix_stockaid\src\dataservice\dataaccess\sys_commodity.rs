use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{prelude::*, sys_commodity},
};
use anyhow::{anyhow, Result};
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
use tracing::*;
#[allow(dead_code)]
impl SysCommodity {
    pub async fn query_inner_code(db: &DbConnection, id: i64) -> Result<SysCommodity> {
        let ret = SysCommodityEntity::find()
            .filter(sys_commodity::Column::Id.eq(id))
            .filter(sys_commodity::Column::BusinessType.eq(3))
            .filter(sys_commodity::Column::ShowState.eq(1))
            .filter(sys_commodity::Column::Status.eq(1))
            .one(db.get_connection())
            .await;
        if let Err(err) = ret {
            error!("查询失败query,{:?}", err);
            return Err(anyhow!("查询失败query,{:?}", err));
        }
        let d = ret.unwrap();
        if d.is_none() {
            return Err(anyhow!("can't find SysCommodity"));
        }
        return Ok(d.unwrap());
    }

    pub async fn query_inner_code_and_type(db: &DbConnection) -> Result<Vec<SysCommodity>> {
        let ret = SysCommodityEntity::find()
            .filter(sys_commodity::Column::BusinessType.eq(3))
            .filter(sys_commodity::Column::ShowState.eq(1))
            .filter(sys_commodity::Column::Status.eq(1))
            .filter(sys_commodity::Column::MarketId.is_in(vec![101, 102, 103]))
            .all(db.get_connection())
            .await;
        if let Err(err) = ret {
            error!("查询失败query,{:?}", err);
            return Err(anyhow!("查询失败query,{:?}", err));
        }
        let d = ret.unwrap();
        if d.len() == 0 {
            error!("没有查询到SysCommodity");
            return Err(anyhow!("can't find SysCommodity"));
        }
        return Ok(d);
    }
}
