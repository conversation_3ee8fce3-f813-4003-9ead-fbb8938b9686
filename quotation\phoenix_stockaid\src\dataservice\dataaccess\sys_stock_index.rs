use crate::dataservice::{dbsetup::DbConnection, entities::prelude::*};
use anyhow::{anyhow, Result};
use sea_orm::EntityTrait;
use tracing::*;
impl SysStockIndex {
    pub async fn query(db: &DbConnection) -> Result<Vec<SysStockIndex>> {
        let ret = SysStockIndexEntity::find().all(db.get_connection()).await;
        if let Err(err) = ret {
            error!("查询失败query,{:?}", err);
            return Err(anyhow!("查询失败query,{:?}", err));
        }
        let d = ret.unwrap();
        if d.len() == 0 {
            return Err(anyhow!("can't find SysStockIndex"));
        }
        return Ok(d);
    }
}
