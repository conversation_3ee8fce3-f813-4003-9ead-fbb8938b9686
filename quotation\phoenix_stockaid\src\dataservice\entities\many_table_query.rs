// use sea_orm::entity::prelude::*;
use sea_orm::FromQueryResult;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, FromQueryResult)]
#[allow(dead_code)]
pub struct PlateMeta {
    pub id: i64,
    /// 板块名称
    pub name: String,
    /// 1：自定义板块，2：固定板块, 3:行业板块
    pub cate: i32,
    //1：港股，2：美股，3：沪深
    pub market_type: i32,
    pub market_id: i32,
    /// 品种代码
    pub stock_code: String,
    /// 板块代码
    pub plate_code: String,
}
