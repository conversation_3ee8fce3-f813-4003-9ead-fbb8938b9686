//! `SeaORM` Entity. Generated by sea-orm-codegen 0.10.3

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "sys_commodity_group")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub business_type: i8,
    pub market_id: i64,
    pub name: String,
    pub counts: i32,
    pub status: i8,
    pub create_date: i64,
    pub cate: i8,
    pub sort: i32,
    pub code: String,
    pub edit_state: i8,
    pub r#type: i32,
    pub inner_code: String,
}

#[derive(Co<PERSON>, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
