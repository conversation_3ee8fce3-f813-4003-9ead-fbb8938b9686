mod client;
mod config;
mod controller;
// mod controller_1;
mod dataservice;
// mod protofiles;
mod server;
mod service;

use anyhow::Result;
use tracing::*;

use common::{init_tracing, logclient::*};
use protoes::phoenixstockaid::phoenix_stock_aid_server::PhoenixStockAidServer;

use crate::config::settings::Settings;
use crate::server::ServerHandler;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let prefix = "phoenix_stockaid";
    let dir = "./log";

    let settings = Settings::new().expect("read config error");
    let level = "INFO";
    let _guard = init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:#?}", &settings);

    init_logclient(&settings.servers.logcenterserver, &format!("phoenix_stockaid")).await;

    let server = prepare(&settings).await.expect("Init server error......");

    // info!("开始启动系统...");
    server_run(server, &settings).await
}

async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
    // let grpc_stub = create_controller(settings).await;

    let grpc = ServerHandler::new(&settings).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    // let addr = "0.0.0.0:60000".parse().unwrap();
    let addr = format!("{}:{}", settings.application.apphost, settings.application.appport).parse().unwrap();
    info!("Starting  service on {}", addr);
    info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    // log_debug(&format!(
    //     "server started, name: {} version: {} description: {}",
    //     env!("CARGO_PKG_NAME"),
    //     env!("CARGO_PKG_VERSION"),
    //     env!("CARGO_PKG_DESCRIPTION")
    // ))
    // .await;
    // 2. 获取客户端, 实际业务中不要使用unwrap
    // LogClient::get()?.push(LogLevel::Info, "server is runnig".into()).await;
    //lc.push(LogLevel::Error, "server had halted".into()).await;

    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder()
        .add_service(PhoenixStockAidServer::new(svr))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await
        .expect("build server error");

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
