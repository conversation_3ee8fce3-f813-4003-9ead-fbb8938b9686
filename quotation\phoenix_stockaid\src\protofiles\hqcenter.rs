// This file is @generated by prost-build.
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeHqMsgReq {
    /// 动作类型 1：订阅，0：取消订阅。
    #[prost(int32, tag = "1")]
    pub action: i32,
    /// 品种代码
    #[prost(string, tag = "2")]
    pub goods: ::prost::alloc::string::String,
    /// 市场代码
    #[prost(string, tag = "3")]
    pub exchange_no: ::prost::alloc::string::String,
    /// 合约编号
    #[prost(string, tag = "4")]
    pub contract: ::prost::alloc::string::String,
    /// Redis中对应ID
    #[prost(string, tag = "5")]
    pub id: ::prost::alloc::string::String,
    /// 商品类型，期货为F
    #[prost(string, tag = "6")]
    pub commodity_type: ::prost::alloc::string::String,
}
/// 回复的数据包
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ResultMsg {
    #[prost(string, tag = "1")]
    pub err_msg: ::prost::alloc::string::String,
    /// 0 代表成功，其他错误码自行定义
    #[prost(int32, tag = "2")]
    pub err_code: i32,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TickHqReq {
    /// 合约编号可选
    #[prost(string, tag = "1")]
    pub strcontractno: ::prost::alloc::string::String,
    /// 请求行情类型 0:1档行情(默认) 1:5档行情
    #[prost(int32, tag = "2")]
    pub iticktype: i32,
    /// 请求tick时间 unix时间
    #[prost(int64, tag = "3")]
    pub ticktime: i64,
    /// 0 延迟行情  1实时行情
    #[prost(int32, tag = "4")]
    pub realtime: i32,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TickHqResp {
    #[prost(message, repeated, tag = "1")]
    pub tick_hq_info: ::prost::alloc::vec::Vec<YsHqInfo>,
}
/// 备注:历史K线请求必传：合约编号,结束时间, 条数。当前K线请求：传条数 ,合约
/// 历史分时请求：只传合约编号拼接日期 合约编号值为：合约|日期。 当前分时请求:合约+日期   例如:CL1810|20180910
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct KLineHqReq {
    /// 合约编号
    #[prost(string, tag = "1")]
    pub strcontractno: ::prost::alloc::string::String,
    /// 1:一分钟 5:5分钟, 10:10分钟,30: 30分钟, 60：60分钟,24:日线
    #[prost(string, tag = "2")]
    pub strklinetype: ::prost::alloc::string::String,
    /// 结束时间
    #[prost(string, tag = "3")]
    pub strendtime: ::prost::alloc::string::String,
    /// 条数
    #[prost(int32, tag = "4")]
    pub limit: i32,
    /// 0 延迟行情  1实时行情
    #[prost(int32, tag = "5")]
    pub realtime: i32,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct KLineHqResp {
    #[prost(message, repeated, tag = "1")]
    pub klineinfo: ::prost::alloc::vec::Vec<KLineHqInfo>,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct KLineHqInfo {
    #[prost(string, tag = "1")]
    pub strkline: ::prost::alloc::string::String,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct LastPriceMsgReq {
    /// 证券代码 60001
    #[prost(string, tag = "1")]
    pub stock_code: ::prost::alloc::string::String,
    /// 市场 沪深:101 102 港:103 ...
    #[prost(int32, tag = "2")]
    pub exchange_id: i32,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct LastPriceMsgResp {
    #[prost(int32, tag = "1")]
    pub err_code: i32,
    #[prost(string, tag = "2")]
    pub err_msg: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "3")]
    pub data: ::core::option::Option<LastPriceInfo>,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct LastPriceInfo {
    /// 最新价
    #[prost(double, tag = "1")]
    pub last_price: f64,
    /// 涨幅
    #[prost(double, tag = "2")]
    pub change_rate: f64,
    /// 涨跌
    #[prost(double, tag = "3")]
    pub change_value: f64,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct HqMsgReq {
    /// ctp行情
    #[prost(message, optional, tag = "1")]
    pub ctphqinfo: ::core::option::Option<CtpHqInfo>,
    /// 易盛行情
    #[prost(message, optional, tag = "2")]
    pub yshqinfo: ::core::option::Option<YsHqInfo>,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CtpHqInfo {
    /// 交易日
    #[prost(string, tag = "1")]
    pub trading_day: ::prost::alloc::string::String,
    /// 合约代码
    #[prost(string, tag = "2")]
    pub instrument_id: ::prost::alloc::string::String,
    /// 市场代码
    #[prost(string, tag = "3")]
    pub exchange_id: ::prost::alloc::string::String,
    /// 合约在交易所的代码
    #[prost(string, tag = "4")]
    pub exchange_inst_id: ::prost::alloc::string::String,
    /// 最新价
    #[prost(double, tag = "5")]
    pub last_price: f64,
    /// 上次结算价
    #[prost(double, tag = "6")]
    pub pre_settlement_price: f64,
    /// 昨收盘
    #[prost(double, tag = "7")]
    pub pre_close_price: f64,
    /// 昨持仓量
    #[prost(double, tag = "8")]
    pub pre_open_interest: f64,
    /// 今开盘
    #[prost(double, tag = "9")]
    pub open_price: f64,
    /// 最高价
    #[prost(double, tag = "10")]
    pub highest_price: f64,
    /// 最低价
    #[prost(double, tag = "11")]
    pub lowest_price: f64,
    /// 数量
    #[prost(int32, tag = "12")]
    pub volume: i32,
    /// 成交金额
    #[prost(double, tag = "13")]
    pub turnover: f64,
    /// 持仓量
    #[prost(double, tag = "14")]
    pub open_interest: f64,
    /// 今收盘
    #[prost(double, tag = "15")]
    pub close_price: f64,
    /// 本次结算价
    #[prost(double, tag = "16")]
    pub settlement_price: f64,
    /// 涨停板价
    #[prost(double, tag = "17")]
    pub upper_limit_price: f64,
    /// 跌停板价
    #[prost(double, tag = "18")]
    pub lower_limit_price: f64,
    /// 昨虚实度
    #[prost(double, tag = "19")]
    pub pre_delta: f64,
    /// 今虚实度
    #[prost(double, tag = "20")]
    pub curr_delta: f64,
    /// 最后修改时间
    #[prost(string, tag = "21")]
    pub update_time: ::prost::alloc::string::String,
    /// 最后修改毫秒
    #[prost(int32, tag = "22")]
    pub update_millisec: i32,
    /// 申买价一
    #[prost(double, tag = "23")]
    pub bid_price1: f64,
    /// 申买量一
    #[prost(int32, tag = "24")]
    pub bid_volume1: i32,
    /// 申卖价一
    #[prost(double, tag = "25")]
    pub ask_price1: f64,
    /// 申卖量一
    #[prost(int32, tag = "26")]
    pub ask_volume1: i32,
    /// 申买价二
    #[prost(double, tag = "27")]
    pub bid_price2: f64,
    /// 申买量二
    #[prost(int32, tag = "28")]
    pub bid_volume2: i32,
    /// 申卖价二
    #[prost(double, tag = "29")]
    pub ask_price2: f64,
    /// 申卖量二
    #[prost(int32, tag = "30")]
    pub ask_volume2: i32,
    /// 申买价三
    #[prost(double, tag = "31")]
    pub bid_price3: f64,
    /// 申买量三
    #[prost(int32, tag = "32")]
    pub bid_volume3: i32,
    /// 申卖价三
    #[prost(double, tag = "33")]
    pub ask_price3: f64,
    /// 申卖量三
    #[prost(int32, tag = "34")]
    pub ask_volume3: i32,
    /// 申买价四
    #[prost(double, tag = "35")]
    pub bid_price4: f64,
    /// 申买量四
    #[prost(int32, tag = "36")]
    pub bid_volume4: i32,
    /// 申卖价四
    #[prost(double, tag = "37")]
    pub ask_price4: f64,
    /// 申卖量四
    #[prost(int32, tag = "38")]
    pub ask_volume4: i32,
    /// 申买价五
    #[prost(double, tag = "39")]
    pub bid_price5: f64,
    /// 申买量五
    #[prost(int32, tag = "40")]
    pub bid_volume5: i32,
    /// 申卖价五
    #[prost(double, tag = "41")]
    pub ask_price5: f64,
    /// 申卖量五
    #[prost(int32, tag = "42")]
    pub ask_volume5: i32,
    /// 当日均价
    #[prost(double, tag = "43")]
    pub average_price: f64,
    /// 业务日期
    #[prost(string, tag = "44")]
    pub action_day: ::prost::alloc::string::String,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct YsHqInfo {
    /// 市场代码
    #[prost(string, tag = "1")]
    pub exchange_id: ::prost::alloc::string::String,
    /// 品种编号
    #[prost(string, tag = "2")]
    pub commodity_no: ::prost::alloc::string::String,
    /// 合约代码
    #[prost(string, tag = "3")]
    pub contract_no1: ::prost::alloc::string::String,
    /// 币种编号
    #[prost(string, tag = "4")]
    pub currency_no: ::prost::alloc::string::String,
    /// 时间戳
    #[prost(string, tag = "5")]
    pub tapidtstamp: ::prost::alloc::string::String,
    /// 昨结算价
    #[prost(double, tag = "6")]
    pub q_pre_settle_price: f64,
    /// 昨持仓量
    #[prost(int64, tag = "7")]
    pub q_pre_position_qty: i64,
    /// 开盘价
    #[prost(double, tag = "8")]
    pub q_opening_price: f64,
    /// 最新价
    #[prost(double, tag = "9")]
    pub q_last_price: f64,
    /// 最高价
    #[prost(double, tag = "10")]
    pub q_high_price: f64,
    /// 最低价
    #[prost(double, tag = "11")]
    pub q_low_price: f64,
    /// 涨停价
    #[prost(double, tag = "12")]
    pub q_limit_up_price: f64,
    /// 跌停价
    #[prost(double, tag = "13")]
    pub q_limit_down_price: f64,
    /// 当日总成交量
    #[prost(int64, tag = "14")]
    pub q_total_qty: i64,
    /// 当日成交金额
    #[prost(double, tag = "15")]
    pub q_total_turnover: f64,
    /// 持仓量
    #[prost(int64, tag = "16")]
    pub q_position_qty: i64,
    /// 均价
    #[prost(double, tag = "17")]
    pub q_average_price: f64,
    /// 收盘价
    #[prost(double, tag = "18")]
    pub q_closing_price: f64,
    /// 最新成交量
    #[prost(double, tag = "19")]
    pub q_last_qty: f64,
    /// 买价1-5档
    #[prost(double, repeated, tag = "20")]
    pub q_bid_price: ::prost::alloc::vec::Vec<f64>,
    /// 买量1-5档
    #[prost(int64, repeated, tag = "21")]
    pub q_bid_qty: ::prost::alloc::vec::Vec<i64>,
    /// 卖价1-5档
    #[prost(double, repeated, tag = "22")]
    pub q_ask_price: ::prost::alloc::vec::Vec<f64>,
    /// 卖量1-5档
    #[prost(int64, repeated, tag = "23")]
    pub q_ask_qty: ::prost::alloc::vec::Vec<i64>,
    /// 涨幅
    #[prost(double, tag = "24")]
    pub q_change_rate: f64,
    /// 涨跌值
    #[prost(double, tag = "25")]
    pub q_change_value: f64,
    /// 昨收价
    #[prost(double, tag = "26")]
    pub q_pre_closing_price: f64,
    /// 委买总量
    #[prost(int64, tag = "27")]
    pub q_total_bid_qty: i64,
    /// 委卖总量
    #[prost(int64, tag = "28")]
    pub q_total_ask_qty: i64,
    /// 新加
    ///
    /// 换手率
    #[prost(double, tag = "29")]
    pub q_turnover_ratio: f64,
    /// 振幅
    #[prost(double, tag = "30")]
    pub q_amplitude: f64,
    /// 市盈率
    #[prost(double, tag = "31")]
    pub q_pe_rate: f64,
    /// 市净率
    #[prost(double, tag = "32")]
    pub q_dyn_pb_rate: f64,
    /// 量比
    #[prost(double, tag = "33")]
    pub q_vol_ratio: f64,
    /// 流通股
    #[prost(int64, tag = "34")]
    pub q_circulation_amount: i64,
    /// 总股本
    #[prost(int64, tag = "35")]
    pub q_total_shares: i64,
    /// 总市值
    #[prost(double, tag = "36")]
    pub q_market_value: f64,
    /// 货币
    #[prost(string, tag = "37")]
    pub q_money_type: ::prost::alloc::string::String,
    /// 行业信息
    #[prost(string, tag = "38")]
    pub q_industry_info: ::prost::alloc::string::String,
    /// 最新成交额
    #[prost(double, tag = "39")]
    pub q_last_turnover: f64,
    /// 委比
    #[prost(double, tag = "40")]
    pub q_entrust_rate: f64,
    /// 买量1-5档
    #[prost(double, repeated, tag = "41")]
    pub q_bid_qty2: ::prost::alloc::vec::Vec<f64>,
    /// 卖量1-5档
    #[prost(double, repeated, tag = "42")]
    pub q_ask_qty2: ::prost::alloc::vec::Vec<f64>,
    /// 当日总成交量
    #[prost(double, tag = "43")]
    pub q_total_qty2: f64,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PreDealNumReq {
    /// 证券代码 60001
    #[prost(string, tag = "1")]
    pub stock_code: ::prost::alloc::string::String,
    /// 市场 沪深:101 102 港:103 ...
    #[prost(int32, tag = "2")]
    pub exchange_id: i32,
    /// 20230830142500
    #[prost(string, tag = "3")]
    pub time: ::prost::alloc::string::String,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct PreDealAmount {
    #[prost(int32, tag = "1")]
    pub prev_period_amount: i32,
}
/// Generated client implementations.
pub mod svr_post_subscribe_hq_msg_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct SvrPostSubscribeHqMsgClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl SvrPostSubscribeHqMsgClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> SvrPostSubscribeHqMsgClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::Body>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> SvrPostSubscribeHqMsgClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::Body>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::Body>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::Body>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            SvrPostSubscribeHqMsgClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn post_subscribe_hq_msg(
            &mut self,
            request: impl tonic::IntoRequest<super::SubscribeHqMsgReq>,
        ) -> std::result::Result<tonic::Response<super::ResultMsg>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/PostSubscribeHqMsg",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "hqcenter.SvrPostSubscribeHqMsg",
                        "PostSubscribeHqMsg",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_history_k_line_hq(
            &mut self,
            request: impl tonic::IntoRequest<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/PostHistoryKLineHq",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "hqcenter.SvrPostSubscribeHqMsg",
                        "PostHistoryKLineHq",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_current_kline_hq(
            &mut self,
            request: impl tonic::IntoRequest<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/PostCurrentKlineHq",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "hqcenter.SvrPostSubscribeHqMsg",
                        "PostCurrentKlineHq",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_history_fen_shi_hq(
            &mut self,
            request: impl tonic::IntoRequest<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/PostHistoryFenShiHq",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "hqcenter.SvrPostSubscribeHqMsg",
                        "PostHistoryFenShiHq",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_current_fen_shi_hq(
            &mut self,
            request: impl tonic::IntoRequest<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/PostCurrentFenShiHq",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "hqcenter.SvrPostSubscribeHqMsg",
                        "PostCurrentFenShiHq",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_tick_hq(
            &mut self,
            request: impl tonic::IntoRequest<super::TickHqReq>,
        ) -> std::result::Result<tonic::Response<super::TickHqResp>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/PostTickHq",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("hqcenter.SvrPostSubscribeHqMsg", "PostTickHq"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_last_price(
            &mut self,
            request: impl tonic::IntoRequest<super::LastPriceMsgReq>,
        ) -> std::result::Result<
            tonic::Response<super::LastPriceMsgResp>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/get_last_price",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("hqcenter.SvrPostSubscribeHqMsg", "get_last_price"),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_pre_deal_amount(
            &mut self,
            request: impl tonic::IntoRequest<super::PreDealNumReq>,
        ) -> std::result::Result<tonic::Response<super::PreDealAmount>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/hqcenter.SvrPostSubscribeHqMsg/get_pre_deal_amount",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "hqcenter.SvrPostSubscribeHqMsg",
                        "get_pre_deal_amount",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod svr_post_subscribe_hq_msg_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with SvrPostSubscribeHqMsgServer.
    #[async_trait]
    pub trait SvrPostSubscribeHqMsg: std::marker::Send + std::marker::Sync + 'static {
        async fn post_subscribe_hq_msg(
            &self,
            request: tonic::Request<super::SubscribeHqMsgReq>,
        ) -> std::result::Result<tonic::Response<super::ResultMsg>, tonic::Status>;
        async fn post_history_k_line_hq(
            &self,
            request: tonic::Request<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status>;
        async fn post_current_kline_hq(
            &self,
            request: tonic::Request<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status>;
        async fn post_history_fen_shi_hq(
            &self,
            request: tonic::Request<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status>;
        async fn post_current_fen_shi_hq(
            &self,
            request: tonic::Request<super::KLineHqReq>,
        ) -> std::result::Result<tonic::Response<super::KLineHqResp>, tonic::Status>;
        async fn post_tick_hq(
            &self,
            request: tonic::Request<super::TickHqReq>,
        ) -> std::result::Result<tonic::Response<super::TickHqResp>, tonic::Status>;
        async fn get_last_price(
            &self,
            request: tonic::Request<super::LastPriceMsgReq>,
        ) -> std::result::Result<
            tonic::Response<super::LastPriceMsgResp>,
            tonic::Status,
        >;
        async fn get_pre_deal_amount(
            &self,
            request: tonic::Request<super::PreDealNumReq>,
        ) -> std::result::Result<tonic::Response<super::PreDealAmount>, tonic::Status>;
    }
    #[derive(Debug)]
    pub struct SvrPostSubscribeHqMsgServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> SvrPostSubscribeHqMsgServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>>
    for SvrPostSubscribeHqMsgServer<T>
    where
        T: SvrPostSubscribeHqMsg,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::Body>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/hqcenter.SvrPostSubscribeHqMsg/PostSubscribeHqMsg" => {
                    #[allow(non_camel_case_types)]
                    struct PostSubscribeHqMsgSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::SubscribeHqMsgReq>
                    for PostSubscribeHqMsgSvc<T> {
                        type Response = super::ResultMsg;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::SubscribeHqMsgReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::post_subscribe_hq_msg(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostSubscribeHqMsgSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/hqcenter.SvrPostSubscribeHqMsg/PostHistoryKLineHq" => {
                    #[allow(non_camel_case_types)]
                    struct PostHistoryKLineHqSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::KLineHqReq>
                    for PostHistoryKLineHqSvc<T> {
                        type Response = super::KLineHqResp;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::KLineHqReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::post_history_k_line_hq(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostHistoryKLineHqSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/hqcenter.SvrPostSubscribeHqMsg/PostCurrentKlineHq" => {
                    #[allow(non_camel_case_types)]
                    struct PostCurrentKlineHqSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::KLineHqReq>
                    for PostCurrentKlineHqSvc<T> {
                        type Response = super::KLineHqResp;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::KLineHqReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::post_current_kline_hq(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostCurrentKlineHqSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/hqcenter.SvrPostSubscribeHqMsg/PostHistoryFenShiHq" => {
                    #[allow(non_camel_case_types)]
                    struct PostHistoryFenShiHqSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::KLineHqReq>
                    for PostHistoryFenShiHqSvc<T> {
                        type Response = super::KLineHqResp;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::KLineHqReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::post_history_fen_shi_hq(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostHistoryFenShiHqSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/hqcenter.SvrPostSubscribeHqMsg/PostCurrentFenShiHq" => {
                    #[allow(non_camel_case_types)]
                    struct PostCurrentFenShiHqSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::KLineHqReq>
                    for PostCurrentFenShiHqSvc<T> {
                        type Response = super::KLineHqResp;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::KLineHqReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::post_current_fen_shi_hq(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostCurrentFenShiHqSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/hqcenter.SvrPostSubscribeHqMsg/PostTickHq" => {
                    #[allow(non_camel_case_types)]
                    struct PostTickHqSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::TickHqReq>
                    for PostTickHqSvc<T> {
                        type Response = super::TickHqResp;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::TickHqReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::post_tick_hq(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostTickHqSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/hqcenter.SvrPostSubscribeHqMsg/get_last_price" => {
                    #[allow(non_camel_case_types)]
                    struct get_last_priceSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::LastPriceMsgReq>
                    for get_last_priceSvc<T> {
                        type Response = super::LastPriceMsgResp;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::LastPriceMsgReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::get_last_price(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = get_last_priceSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/hqcenter.SvrPostSubscribeHqMsg/get_pre_deal_amount" => {
                    #[allow(non_camel_case_types)]
                    struct get_pre_deal_amountSvc<T: SvrPostSubscribeHqMsg>(pub Arc<T>);
                    impl<
                        T: SvrPostSubscribeHqMsg,
                    > tonic::server::UnaryService<super::PreDealNumReq>
                    for get_pre_deal_amountSvc<T> {
                        type Response = super::PreDealAmount;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PreDealNumReq>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as SvrPostSubscribeHqMsg>::get_pre_deal_amount(
                                        &inner,
                                        request,
                                    )
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = get_pre_deal_amountSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(
                            tonic::body::Body::default(),
                        );
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for SvrPostSubscribeHqMsgServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "hqcenter.SvrPostSubscribeHqMsg";
    impl<T> tonic::server::NamedService for SvrPostSubscribeHqMsgServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
