// This file is @generated by prost-build.
/// 请求数据包
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ReqIndexOrRankMsg {
    /// 市场类型,港股/美股/沪深
    #[prost(int32, tag = "1")]
    pub market_type: i32,
    /// 	int32 rankCode = 2; //获取各个市场排行榜下的具体各股数据
    /// 根据排行榜code去获取 涨幅榜:19999 跌幅榜: 29999
    ///
    /// 板块下所需个数
    #[prost(int32, tag = "3")]
    pub count: i32,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ReqDetailMsg {
    /// 市场类型
    #[prost(int32, tag = "1")]
    pub market_type: i32,
    /// 行情类型  1指数2板块3股票    当
    #[prost(int32, tag = "2")]
    pub info_type: i32,
    /// infoType的值为2的时候,相当于打开板块排行榜
    /// 当infoType的值为3,plateCode不能为0, 例:
    /// infoType:3,plateCode:19999 就去查询 板块code19999的详情
    ///
    /// 板块代码
    #[prost(int32, tag = "3")]
    pub plate_code: i32,
    /// 所需个数
    #[prost(int32, tag = "4")]
    pub count: i32,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ReqTrsMsg {
    /// 所需个数
    #[prost(int32, tag = "1")]
    pub count: i32,
    /// true:正序;false:倒序
    #[prost(bool, tag = "2")]
    pub order: bool,
    /// 0 根据最新价排序  1 根据涨跌幅排序
    #[prost(int32, tag = "3")]
    pub order_type: i32,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DetailCodeInfo {
    /// 证券代码
    #[prost(string, tag = "1")]
    pub code: ::prost::alloc::string::String,
    /// 最新价
    #[prost(double, tag = "2")]
    pub last_price: f64,
    /// 涨跌值
    #[prost(double, tag = "3")]
    pub change_value: f64,
    /// 涨跌幅
    #[prost(double, tag = "4")]
    pub change_rate: f64,
    /// 板块下的领涨股的code  获取板块下所有个股的时候这个字段为空
    #[prost(string, tag = "5")]
    pub top_stock: ::prost::alloc::string::String,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DetailPlateInfo {
    /// 板块ID
    #[prost(int32, tag = "1")]
    pub id: i32,
    /// 板块代码
    #[prost(string, tag = "2")]
    pub code: ::prost::alloc::string::String,
    /// 名称
    #[prost(string, tag = "3")]
    pub name: ::prost::alloc::string::String,
    /// 显示板块前几数量
    #[prost(int32, tag = "4")]
    pub count: i32,
    /// 前几板块列表
    #[prost(message, repeated, tag = "5")]
    pub stock_infos: ::prost::alloc::vec::Vec<DetailCodeInfo>,
}
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ResultPlateInfoMsg {
    /// 板块详情
    #[prost(message, repeated, tag = "1")]
    pub plate_infos: ::prost::alloc::vec::Vec<DetailCodeInfo>,
}
/// 指数
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ResultMainIndexMsg {
    /// 指数行情
    #[prost(message, repeated, tag = "1")]
    pub index_infos: ::prost::alloc::vec::Vec<DetailCodeInfo>,
}
/// 排行榜
#[derive(serde::Serialize, serde::Deserialize)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ResultMainRankMsg {
    /// 	  int32  id = 1;          //板块ID
    /// 		string code = 2;		//板块代码
    /// 	  string name = 3;        //名称
    /// 		int32  count = 4;       //显示板块前几数量
    /// 		repeated CodeInfo stockInfos = 5;
    /// //前几板块列表
    #[prost(message, repeated, tag = "1")]
    pub main_plate: ::prost::alloc::vec::Vec<DetailCodeInfo>,
    #[prost(message, repeated, tag = "2")]
    pub hot_plate: ::prost::alloc::vec::Vec<DetailPlateInfo>,
}
/// Generated client implementations.
pub mod phoenix_stock_aid_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct PhoenixStockAidClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl PhoenixStockAidClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> PhoenixStockAidClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::Body>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> PhoenixStockAidClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::Body>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::Body>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::Body>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            PhoenixStockAidClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn post_main_index_msg(
            &mut self,
            request: impl tonic::IntoRequest<super::ReqIndexOrRankMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultMainIndexMsg>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/phoenixstockaid.PhoenixStockAid/PostMainIndexMsg",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "phoenixstockaid.PhoenixStockAid",
                        "PostMainIndexMsg",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_main_rank_msg(
            &mut self,
            request: impl tonic::IntoRequest<super::ReqIndexOrRankMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultMainRankMsg>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/phoenixstockaid.PhoenixStockAid/PostMainRankMsg",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("phoenixstockaid.PhoenixStockAid", "PostMainRankMsg"),
                );
            self.inner.unary(req, path, codec).await
        }
        pub async fn post_plate_info_msg(
            &mut self,
            request: impl tonic::IntoRequest<super::ReqDetailMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultPlateInfoMsg>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/phoenixstockaid.PhoenixStockAid/PostPlateInfoMsg",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new(
                        "phoenixstockaid.PhoenixStockAid",
                        "PostPlateInfoMsg",
                    ),
                );
            self.inner.unary(req, path, codec).await
        }
        /// 根据获取数量进行区分
        pub async fn post_trs_info_msg(
            &mut self,
            request: impl tonic::IntoRequest<super::ReqTrsMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultPlateInfoMsg>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/phoenixstockaid.PhoenixStockAid/PostTrsInfoMsg",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("phoenixstockaid.PhoenixStockAid", "PostTrsInfoMsg"),
                );
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod phoenix_stock_aid_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with PhoenixStockAidServer.
    #[async_trait]
    pub trait PhoenixStockAid: std::marker::Send + std::marker::Sync + 'static {
        async fn post_main_index_msg(
            &self,
            request: tonic::Request<super::ReqIndexOrRankMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultMainIndexMsg>,
            tonic::Status,
        >;
        async fn post_main_rank_msg(
            &self,
            request: tonic::Request<super::ReqIndexOrRankMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultMainRankMsg>,
            tonic::Status,
        >;
        async fn post_plate_info_msg(
            &self,
            request: tonic::Request<super::ReqDetailMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultPlateInfoMsg>,
            tonic::Status,
        >;
        /// 根据获取数量进行区分
        async fn post_trs_info_msg(
            &self,
            request: tonic::Request<super::ReqTrsMsg>,
        ) -> std::result::Result<
            tonic::Response<super::ResultPlateInfoMsg>,
            tonic::Status,
        >;
    }
    #[derive(Debug)]
    pub struct PhoenixStockAidServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> PhoenixStockAidServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for PhoenixStockAidServer<T>
    where
        T: PhoenixStockAid,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::Body>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/phoenixstockaid.PhoenixStockAid/PostMainIndexMsg" => {
                    #[allow(non_camel_case_types)]
                    struct PostMainIndexMsgSvc<T: PhoenixStockAid>(pub Arc<T>);
                    impl<
                        T: PhoenixStockAid,
                    > tonic::server::UnaryService<super::ReqIndexOrRankMsg>
                    for PostMainIndexMsgSvc<T> {
                        type Response = super::ResultMainIndexMsg;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::ReqIndexOrRankMsg>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as PhoenixStockAid>::post_main_index_msg(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostMainIndexMsgSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/phoenixstockaid.PhoenixStockAid/PostMainRankMsg" => {
                    #[allow(non_camel_case_types)]
                    struct PostMainRankMsgSvc<T: PhoenixStockAid>(pub Arc<T>);
                    impl<
                        T: PhoenixStockAid,
                    > tonic::server::UnaryService<super::ReqIndexOrRankMsg>
                    for PostMainRankMsgSvc<T> {
                        type Response = super::ResultMainRankMsg;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::ReqIndexOrRankMsg>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as PhoenixStockAid>::post_main_rank_msg(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostMainRankMsgSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/phoenixstockaid.PhoenixStockAid/PostPlateInfoMsg" => {
                    #[allow(non_camel_case_types)]
                    struct PostPlateInfoMsgSvc<T: PhoenixStockAid>(pub Arc<T>);
                    impl<
                        T: PhoenixStockAid,
                    > tonic::server::UnaryService<super::ReqDetailMsg>
                    for PostPlateInfoMsgSvc<T> {
                        type Response = super::ResultPlateInfoMsg;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::ReqDetailMsg>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as PhoenixStockAid>::post_plate_info_msg(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostPlateInfoMsgSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/phoenixstockaid.PhoenixStockAid/PostTrsInfoMsg" => {
                    #[allow(non_camel_case_types)]
                    struct PostTrsInfoMsgSvc<T: PhoenixStockAid>(pub Arc<T>);
                    impl<
                        T: PhoenixStockAid,
                    > tonic::server::UnaryService<super::ReqTrsMsg>
                    for PostTrsInfoMsgSvc<T> {
                        type Response = super::ResultPlateInfoMsg;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::ReqTrsMsg>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as PhoenixStockAid>::post_trs_info_msg(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PostTrsInfoMsgSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(
                            tonic::body::Body::default(),
                        );
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for PhoenixStockAidServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "phoenixstockaid.PhoenixStockAid";
    impl<T> tonic::server::NamedService for PhoenixStockAidServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
