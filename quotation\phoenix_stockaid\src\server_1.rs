use std::collections::{BTreeMap, HashMap};
use std::fs::File;
use std::io::Write;
use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot, RwLock};
use tonic::{self, Request, Response, Status};
use tracing::{error, info};

use messagecenter::quotationclient::QuotationClient;
use protoes::hqmsg::YsHqInfo;
use protoes::phoenixstockaid::{phoenix_stock_aid_server::PhoenixStockAid, ReqDetailMsg, ReqIndexOrRankMsg, ReqTrsMsg, ResultMainIndexMsg, ResultMainRankMsg, ResultPlateInfoMsg};

pub use super::controller::*;
use crate::client::hqclient::HqCenterClient;
use crate::config::settings::Settings;
use crate::dataservice::dbsetup::DbConnection;
use crate::service::wsservice;

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        // 定时器
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(settings.system.cachelong));
        let hq_center_client = HqCenterClient::new(&settings.servers.hqcenterserver).await;

        let finances_dbconn = DbConnection::new(&settings.database.finances_uri).await;
        let fdb_copy = finances_dbconn.clone();

        let mut stub = ServerController {
            settings: Arc::new(RwLock::new(settings.clone())),
            // market_info: Arc::new(DashMap::new()),
            plate_type: Arc::new(RwLock::new(HashMap::new())),
            market_plate: Arc::new(RwLock::new(HashMap::new())),
            market_index: Arc::new(RwLock::new(HashMap::new())),
            plate_stock: Arc::new(RwLock::new(HashMap::new())),
            market_stock: Arc::new(RwLock::new(HashMap::new())),
            market_plate_info: Arc::new(Default::default()),
            stock_belong_plate: Arc::new(RwLock::new(HashMap::new())),
            stock_belong_market: Arc::new(RwLock::new(HashMap::new())),
            stock_tick: Arc::new(RwLock::new(HashMap::new())),
            all_stock_info: Arc::new(RwLock::new(Vec::new())),
            stock_extent_info: Arc::new(RwLock::new(BTreeMap::<i32, Vec<String>>::new())),
            stock_extent_info_belong_plate: Arc::new(RwLock::new(HashMap::new())),
            plate_name_info: Arc::new(Default::default()),
            finances_db: Arc::new(fdb_copy.to_owned()),
            hq_center_client,
            trs_stock_info: Arc::new(RwLock::new(Vec::new())),

            plate_cache: Arc::new(RwLock::new(HashMap::new())),
        };
        info!("init controller");
        let _ = stub.init_controller().await;
        info!("init controller end");

        let (tx_quotation, mut rx_quotation) = tokio::sync::broadcast::channel::<YsHqInfo>(102400);
        let (tx_distribution, mut rx_distribution) = tokio::sync::mpsc::channel::<YsHqInfo>(102400);
        let (tx_all_stock, mut rx_all_stock) = tokio::sync::mpsc::channel::<(String, i32, i32)>(102400);
        let (tx_hq, mut rx_hq) = tokio::sync::mpsc::channel::<(String, i32, i32)>(102400);

        let (tx_cache, _rx_cache) = tokio::sync::mpsc::channel::<YsHqInfo>(102400);
        let mut routing_key: HashMap<String, i32> = HashMap::new();
        routing_key.insert("stock.#.#".to_string().clone(), 1);
        let queue_name = format!("phoenix_stockaid_quotation_{}", utility::timeutil::current_timestamp());
        let quotation_client = QuotationClient::new(
            &settings.quotation.stocklive_exchanger.as_str(),
            &queue_name,
            routing_key,
            &format!("{}{}", settings.mq.amqpaddr.as_str(), &settings.quotation.vhost),
            tx_quotation,
        )
        .await;
        // 监听行情
        tokio::spawn(async move {
            messagecenter::init::init_quotation_listen(quotation_client).await;
        });
        let stub = Arc::new(stub);

        let (tx, _rx) = mpsc::channel(16);
        let (tx_close, _rx_close) = oneshot::channel();

        let stub_clone = stub.clone();
        let stub_copy = stub.clone();
        let stub_cache_json = stub.clone();
        let stub_all = stub.clone();
        let stub_us = stub.clone();
        let all_c = tx_all_stock.clone();
        let hq_c = tx_hq.clone();
        let sub_c = stub_all.clone();
        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
        };

        tokio::spawn(async move {
            let sender_tx_distribution = tx_distribution.clone();
            //   let mut i = 0;
            loop {
                if let Ok(quotation) = rx_quotation.recv().await {
                    let _ = sender_tx_distribution.send(quotation).await;
                }
            }
        });
        tokio::spawn(async move {
            let sender_c = tx_all_stock.clone();
            let sender_cache = tx_cache.clone();
            let sender_hq = tx_hq.clone();
            let all = stub_all.clone();
            loop {
                if let Some(quotation) = rx_distribution.recv().await {
                    let stock_code = quotation.contract_no1.clone();
                    let hq_copy: YsHqInfo;
                    let mut flag_temp = false;
                    let mut stock_tick = all.stock_tick.write().await;
                    match stock_tick.get_mut(&*stock_code.clone()) {
                        None => {
                            stock_tick.insert(stock_code.clone(), quotation.clone());
                            hq_copy = quotation.clone();
                            flag_temp = true;
                        }
                        Some(hq_val) => {
                            hq_copy = hq_val.clone();
                            *hq_val = quotation.clone();
                        }
                    }

                    let hq_info = (quotation.q_change_rate * 100.0) as i32;
                    let hq_copy = (hq_copy.q_change_rate * 100.0) as i32;
                    let hq_cache = quotation.clone();
                    let _ = sender_cache.send(hq_cache).await;
                    if flag_temp {
                        let _ = sender_hq.send((stock_code.clone(), hq_info, hq_copy)).await;
                        let _ = sender_c.send((stock_code.clone(), hq_info, hq_copy)).await;
                    } else {
                        let _ = sender_hq.send((stock_code.clone(), hq_info, hq_copy)).await;
                        let _ = sender_c.send((stock_code.clone(), hq_info, hq_copy)).await;
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                if let Some(quotation) = rx_all_stock.recv().await {
                    // info!("{:?}",quotation);
                    let _ = stub_copy.deal_all_stock(&quotation.0, quotation.1, quotation.2).await;
                }
            }
        });
        // tokio::spawn(async move {
        //     loop {
        //         if let Some(quotation) = rx_cache.recv().await {
        //             let _ = stub_cache.deal_cache(&quotation).await;
        //         }
        //     }
        // });
        tokio::spawn(async move {
            loop {
                if let Some(quotation) = rx_hq.recv().await {
                    let _ = stub_clone.deal_hq(&quotation.0, quotation.1, quotation.2).await;
                }
            }
        });

        tokio::spawn(async move {
            loop {
                //一个小时序列化一次到文本里
                persist_interval.tick().await;
                info!("开始序列化");
                let cache = stub_cache_json.plate_cache.read().await.clone();
                let json_str = serde_json::to_string(&cache).expect("Failed to serialize Map to JSON");
                let mut file = File::create("./cache.json").expect("Failed to create file");
                file.write_all(json_str.as_bytes()).expect("Failed to write to file");
            }
        });
        let (tx_ws, mut rx_ws) = tokio::sync::mpsc::channel::<YsHqInfo>(102400);
        let mut flag = false;
        if settings.system.wsflag == 1 {
            flag = true;
        }
        if flag {
            let list = stub_us.update_us().await;
            let ss = settings.clone();
            if let Ok(val) = list {
                if val.len() != 0 {
                    tokio::spawn(async move {
                        let val_c = val.clone();
                        while let Err(e) = wsservice::wss_init(&ss, tx_ws.clone(), val_c.clone()).await {
                            error!("{:?}", e);
                        }
                    });
                }
            }
        }
        tokio::spawn(async move {
            let sender_c = all_c.clone();
            let sender_hq = hq_c.clone();
            let all = sub_c.clone();
            loop {
                if let Some(quotation) = rx_ws.recv().await {
                    let stock_code = quotation.contract_no1.clone();
                    let hq_copy: YsHqInfo;
                    let mut flag_temp = false;
                    let mut stock_tick = all.stock_tick.write().await;
                    match stock_tick.get_mut(&*stock_code.clone()) {
                        None => {
                            stock_tick.insert(stock_code.clone(), quotation.clone());
                            hq_copy = quotation.clone();
                            flag_temp = true;
                        }
                        Some(hq_val) => {
                            hq_copy = hq_val.clone();
                            *hq_val = quotation.clone();
                        }
                    }
                    let hq_info = (quotation.q_change_rate * 100.0) as i32;
                    let hq_copy = (hq_copy.q_change_rate * 100.0) as i32;
                    if flag_temp {
                        let _ = sender_hq.send((stock_code.clone(), hq_info, hq_copy)).await;
                        let _ = sender_c.send((stock_code.clone(), hq_info, hq_copy)).await;
                    } else {
                        let _ = sender_hq.send((stock_code.clone(), hq_info, hq_copy)).await;
                        let _ = sender_c.send((stock_code.clone(), hq_info, hq_copy)).await;
                    }
                }
            }
        });
        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl PhoenixStockAid for ServerHandler {
    async fn post_main_index_msg(&self, request: Request<ReqIndexOrRankMsg>) -> Result<Response<ResultMainIndexMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("获取首页指数 request:{:?}", in_stream);
        let res = self.stub.post_main_index_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }

    async fn post_main_rank_msg(&self, request: Request<ReqIndexOrRankMsg>) -> Result<Response<ResultMainRankMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("获取首页热门排行榜 request:{:?}", in_stream);
        let res = self.stub.post_main_rank_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }

    async fn post_plate_info_msg(&self, request: Request<ReqDetailMsg>) -> Result<Response<ResultPlateInfoMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("获取板块数据 request:{:?}", in_stream);
        let res = self.stub.post_plate_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }

    async fn post_trs_info_msg(&self, request: Request<ReqTrsMsg>) -> Result<Response<ResultPlateInfoMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("post_trs_info_msg request:{:?}", in_stream);
        let res = self.stub.post_trs_info_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }
}
