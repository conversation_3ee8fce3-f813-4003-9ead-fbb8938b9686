use crate::dataservice::dbsetup::DbConnection;
use anyhow::{anyhow, Result};
use sea_orm::sea_query::{<PERSON><PERSON>, Expr, MysqlQueryBuilder, Query};
use sea_orm::{DatabaseBackend, FromQueryResult, Statement};

use crate::dataservice::entities::prelude::*;
use crate::dataservice::entities::{many_table_query::PlateMeta, sys_commodity, sys_commodity_group, sys_commodity_group_list};
use tracing::*;
#[allow(dead_code)]
pub async fn query_sys_trade_config_commodity(db: &DbConnection) -> Vec<SysTradeConfigCommodity> {
    match SysTradeConfigCommodity::query(db).await {
        Ok(data) => data,
        Err(e) => {
            error!("查询失败:{:?}", e.to_string());
            Vec::new()
        }
    }
}
#[allow(dead_code)]
pub async fn query_inner_code(db: &DbConnection, id: i64) -> Result<SysCommodity> {
    match SysCommodity::query_inner_code(db, id).await {
        Ok(data) => Ok(data),
        Err(e) => {
            // error!("查询失败:{} {:?}", id, e.to_string());
            Err(anyhow!("查询失败: {}", e))
        }
    }
}

#[allow(dead_code)]
pub async fn query_inner_code_and_type(db: &DbConnection) -> Vec<SysCommodity> {
    match SysCommodity::query_inner_code_and_type(db).await {
        Ok(data) => data,
        Err(e) => {
            error!("查询失败:{:?}", e.to_string());
            Vec::new()
        }
    }
}

pub async fn query_index(db: &DbConnection) -> Vec<SysStockIndex> {
    match SysStockIndex::query(db).await {
        Ok(data) => data,
        Err(e) => {
            error!("查询失败:{:?}", e.to_string());
            // Err(anyhow!("查询失败: {}", e))
            Vec::new()
        }
    }
}

pub async fn query_plate(db: &DbConnection) -> Vec<PlateMeta> {
    let query = Query::select()
        .from(SysCommodityGroupListEntity)
        .from(SysCommodityEntity)
        .from(SysCommodityGroupEntity)
        .expr_as(Expr::col((SysCommodityGroupEntity, sys_commodity_group::Column::Id)), Alias::new("id"))
        .expr_as(Expr::col((SysCommodityGroupEntity, sys_commodity_group::Column::Name)), Alias::new("name"))
        .expr_as(Expr::col((SysCommodityGroupEntity, sys_commodity_group::Column::Cate)), Alias::new("cate"))
        .expr_as(Expr::col((SysCommodityGroupEntity, sys_commodity_group::Column::Type)), Alias::new("market_type"))
        .expr_as(Expr::col((SysCommodityEntity, sys_commodity::Column::MarketId)), Alias::new("market_id"))
        .expr_as(Expr::col((SysCommodityEntity, sys_commodity::Column::InnerCode)), Alias::new("stock_code"))
        .expr_as(Expr::col((SysCommodityGroupEntity, sys_commodity_group::Column::InnerCode)), Alias::new("plate_code"))
        .and_where(Expr::col((SysCommodityGroupListEntity, sys_commodity_group_list::Column::CommodityId)).equals((SysCommodityEntity, sys_commodity::Column::Id)))
        .and_where(Expr::col((SysCommodityGroupListEntity, sys_commodity_group_list::Column::GroupId)).equals((SysCommodityGroupEntity, sys_commodity_group::Column::Id)))
        .and_where(Expr::col((SysCommodityGroupEntity, sys_commodity_group::Column::Status)).eq(1))
        .and_where(Expr::col((SysCommodityEntity, sys_commodity::Column::Status)).eq(1))
        .and_where(Expr::col((SysCommodityEntity, sys_commodity::Column::MarketId)).is_in([101, 102, 103]))
        .and_where(Expr::col((SysCommodityEntity, sys_commodity::Column::ShowState)).eq(1))
        .to_owned();

    match PlateMeta::find_by_statement(Statement::from_string(DatabaseBackend::MySql, query.to_string(MysqlQueryBuilder)))
        .all(db.get_connection())
        .await
    {
        Ok(plate_list) => plate_list,
        Err(e) => {
            error!("查询失败:{:?}", e.to_string());
            Vec::new()
        }
    }
}
