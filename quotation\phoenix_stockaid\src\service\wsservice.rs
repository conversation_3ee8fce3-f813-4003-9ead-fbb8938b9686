use anyhow::{anyhow, Result};
use byteorder::{BigEndian, ByteOrder, NetworkEndian};
use futures_util::stream::SplitSink;
use futures_util::{SinkExt, StreamExt};
use prost::bytes::BytesMut;
use prost::Message as ProtoMessage;
use std::ops::ControlFlow;
use std::sync::Arc;
use tokio::net::TcpStream;
use tokio::sync::mpsc::Sender;
use tokio::sync::Mutex;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message as WsMessage, MaybeTlsStream, WebSocketStream};
use tracing::{error, info};

use common::logclient::LogClient;
use protoes::hqmsg::{HqMsgReq, SubscribeHqReq, YsHqInfo};

use crate::config::Settings;

pub async fn wss_init(ss: &Settings, sender1: Sender<YsHqInfo>, val: Vec<String>) -> Result<()> {
    let ws_stream = match connect_async(ss.system.hqwsserver.clone().as_str()).await {
        Ok((stream, response)) => {
            // This will be the HTTP response, same as with server this is the last moment we
            // can still access HTTP stuff.
            info!("Server response was {:?}", response);
            stream
        }
        Err(e) => {
            error!("WebSocket handshake failed with {:?}!", e);
            //return Err(Status::new(Code::Ok, ("error")));
            if let Ok(c) = LogClient::get() {
                c.push_error(&format!("wss_init error{}", e.to_string())).await;
            }
            return Err(anyhow!(e));
        }
    };
    let (sender, mut receiver) = ws_stream.split();
    let arc_tx = Arc::new(Mutex::new(sender));
    let tx_c = arc_tx.clone();
    let tx_c_pong = arc_tx.clone();
    let hq = SubscribeHqReq {
        stock_code: val.clone(),
        control: 1,
        realtime: 1,
    };
    let bin = package_data(&hq);
    let bin = prost::bytes::Bytes::from_iter(bin);
    tx_c.lock().await.send(WsMessage::Binary(bin)).await.expect("send error");

    let mut recv_task = tokio::spawn(async move {
        while let Some(Ok(msg)) = tokio_stream::StreamExt::next(&mut receiver).await {
            let msg_copy = sender1.clone();
            let tx_c = tx_c_pong.clone();
            if process_message(msg, msg_copy, tx_c).await.is_break() {
                break;
            }
        }
    });
    tokio::select! {
         // rv_a = (&mut send_task) => {
         //       match rv_a {
         //           Ok(a) => info!("messages sent to"),
         //           Err(a) => info!("Error sending messages {:?}",a)
         //       }
         //       recv_task.abort();
         //       // return Err(anyhow!(format!("wss error")));
         //    },
        rv_b = (&mut recv_task) => {
            match rv_b {
            Ok(_b) => info!("Received messages"),
            Err(b) => info!("Error receiving messages {:?}",b)
            }
            // send_task.abort();
             // return Err(anyhow!(format!("wss error")));
        }
    }
    return Err(anyhow!(format!("wss error")));
}

pub async fn process_message(msg: WsMessage, nt: Sender<YsHqInfo>, tx_c: Arc<Mutex<SplitSink<WebSocketStream<MaybeTlsStream<TcpStream>>, WsMessage>>>) -> ControlFlow<(), ()> {
    match msg {
        WsMessage::Text(t) => {
            info!(">>> got {} text: {:?}", t.len(), t);
        }
        WsMessage::Binary(d) => {
            //  info!(">>> got {} bytes: {:?}", d.len(), d);
            let mut data_buf = prost::bytes::BytesMut::from_iter(d);
            match decode(&mut data_buf) {
                Ok(Some(req)) => {
                    if let Some(val) = req.yshqinfo {
                        //   info!("{:?}",val);
                        let _ = nt.send(val).await;
                    }
                }
                Ok(None) => {
                    info!("数据不全");
                }
                Err(err) => {
                    error!("{:?}", err);
                }
            }
        }
        WsMessage::Close(c) => {
            if let Some(cf) = c {
                info!(">>> got close with code {} and reason `{}`", cf.code, cf.reason);
            } else {
                info!(">>> somehow got close message without CloseFrame",);
            }
            return ControlFlow::Break(());
        }

        WsMessage::Pong(v) => {
            info!(">>> got pong with {:?}", v);
        }

        WsMessage::Ping(_v) => {
            //            info!(">>>  got ping with {:?}",v);
            tx_c.lock().await.send(WsMessage::Pong(prost::bytes::Bytes::new())).await.expect("send error");
        }

        WsMessage::Frame(_) => {
            unreachable!("This is never supposed to happen")
        }
    }
    ControlFlow::Continue(())
}

// 编码
// async fn encode(msg: &HqMsgReq, dst: &mut BytesMut) {
//     let mut payload = prost::bytes::BytesMut::new();
//     let _ = msg.encode(&mut payload).unwrap();
//
//     dst.reserve(payload.len() + 8);//重置len
//     dst.put_u64(payload.len() as u64);//按大端字节顺序将一个无符号的64位整数写入self。
//     dst.put(payload);//追加消息
// }
// 解码
fn decode(src: &mut BytesMut) -> Result<Option<HqMsgReq>> {
    let size = {
        //获取消息长度
        if src.len() < 8 {
            return Ok(None);
        }
        BigEndian::read_u64(src.as_ref()) as usize //八个字节
    };

    if src.len() >= size + 8 {
        let _ = src.split_to(8); //在给定索引处将缓冲区一分为二。然后self包含元素[at，len），返回的BytesMut包含元素[0，at）。
        let buf = src.split_to(size); //截取消息
        Ok(Some(HqMsgReq::decode(buf).unwrap())) //解析
    } else {
        Ok(None)
    }
}
fn package_data(msg: &SubscribeHqReq) -> Vec<u8> {
    //let mut data_body: Vec<u8> = vec![];
    let mut payload = prost::bytes::BytesMut::new();
    let _ = msg.encode(&mut payload).unwrap();
    let mut header = vec![0; 8];
    NetworkEndian::write_i32(&mut header[4..], payload.len() as i32);

    // let mut resp = prost::bytes::Bytes::new();
    // resp.extend_from_slice(header);

    let result = [&header[..], &payload[..]].concat();
    result
}
