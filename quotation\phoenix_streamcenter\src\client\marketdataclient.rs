use crate::protofiles::{hqmsg::YsHqInfo, market_data_servers_client::MarketDataServersClient, ContractMsg};
use crate::server::controller::push_log;
use anyhow::Result;
use tokio::sync::broadcast::Sender;
use tonic::transport::Channel;
use tracing::{error, info};
// #[derive(Clone)]
pub struct MarketDataclient {
    exchangeno: Vec<String>,
    pub client: Option<MarketDataServersClient<Channel>>,
    tx_tick: Sender<YsHqInfo>,
    uri: String,
}

impl MarketDataclient {
    pub async fn new(uri: &String, exchangenos: &String, tx_tick: Sender<YsHqInfo>) -> Self {
        let mut market_uri = uri.clone();
        if !uri.contains("http://") {
            market_uri = format!("http://{}", uri);
        }
        let exchangeno: Vec<String> = exchangenos.split(',').map(|x| x.to_string()).collect();
        let mut market_dataclient = MarketDataclient {
            client: None,
            exchangeno,
            tx_tick,
            uri: market_uri,
        };
        let ret = MarketDataServersClient::connect(market_dataclient.uri.to_owned()).await;
        if ret.as_ref().is_err() {
            push_log(format!("connect to MarketDataServer failed: {:?}", market_dataclient.uri).as_str()).await;
        } else {
            info!("行情数据服务连接成功....");
            market_dataclient.client = Some(ret.expect("connect to MarketDataServer failed"))
        }
        market_dataclient
    }

    pub async fn init_client(&mut self) -> Result<MarketDataServersClient<Channel>> {
        if self.client.is_some() {
            return Ok(self.client.clone().unwrap());
        } else {
            let ret = MarketDataServersClient::connect(self.uri.to_owned()).await;
            if ret.as_ref().is_err() {
                // push_log(format!("connect to MarketDataServer failed: {:?}", self.uri).as_str()).await;
                return Err(anyhow!(format!("connect to MarketDataServer failed")));
            }
            let client = ret.expect("connect to MarketDataServer failed");
            info!("行情数据服务连接成功....");
            push_log(format!("行情数据服务连接成功").as_str()).await;
            self.client = Some(client);
            return Ok(self.client.clone().unwrap());
        }
    }

    pub async fn do_subscribe_market_data(&mut self) -> Result<()> {
        let ret = self.init_client().await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();

        let response = match client
            .subscribe_market_data(ContractMsg {
                exchange_no: "ALL".to_owned(),
                commodity_no: "ALL".to_string(),
                contract_no: "ALL".to_string(),
                channel_no: 0,
                subscribe: true,
            })
            .await
        {
            Ok(val) => val,
            Err(status) => {
                self.client = None;
                error!("{:?}", status);
                push_log(format!("MarketDataServer err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!(format!("MarketDataServer err")));
            }
        };

        let mut inbound = response.into_inner();
        while let Ok(inbound_data) = inbound.message().await {
            if inbound_data.is_some() {
                let hqinfo = inbound_data.unwrap();
                // info!("received hqinfo: {:?}", &hqinfo);
                if self.exchangeno.iter().find(|&x| x == &hqinfo.exchange_id).is_some() {
                    if let Err(e) = self.tx_tick.send(hqinfo.to_owned()) {
                        error!("send to channel error...........:{:?}", &e);
                        push_log("tx_tick.send error").await;
                    }
                }
            } else {
                error!("inbound data empty");
                break;
            }
        }
        self.client = None;
        error!("gRPC error");
        push_log("GRPC error error").await;
        // Ok(())
        Err(anyhow!("means a gRPC error was sent by the sender instead of a valid response message"))
    }

    // pub async fn do_subscribe_market_data(&mut self) -> Result<()> {
    //     let ret = self.init_client().await;
    //     if ret.as_ref().is_err() {
    //         return Err(anyhow!(ret.as_ref().err().unwrap().to_string()))
    //     }
    //     let mut client = ret.unwrap();

    //     let data = tokio_stream::iter(1..usize::MAX).map(move |_i| ContractMsg {
    //         exchange_no: "ALL".to_owned(),
    //         commodity_no: "ALL".to_string(),
    //         contract_no: "ALL".to_string(),
    //         channel_no: 0,
    //         subscribe: true,
    //     });
    //     let in_stream = data.take(1);

    //     let response = match client.subscribe_market_data(in_stream).await {
    //         Ok(val) => val,
    //         Err(status) => {
    //             self.client = None;
    //             error!("{:?}", status);
    //             push_log(format!("MarketDataServer err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
    //             return Err(anyhow!(format!("MarketDataServer err")));
    //         },
    //     };

    //     let mut inbound = response.into_inner();
    //     while let Some(inbound_data) = inbound.next().await {// 消耗并返回流中的下一个值，如果流已结束，则返回“无”。
    //         if inbound_data.is_ok() {
    //             let hqinfo = inbound_data.unwrap();
    //             // info!("received hqinfo: {:?}", &hqinfo);
    //             if self.exchangeno.iter().find(|&x| x == &hqinfo.exchange_id).is_some() {
    //                 if let Err(e) = self.tx_tick.send(hqinfo.to_owned()) {
    //                     error!("send to channel error...........:{:?}", &e);
    //                 }
    //             }
    //         } else {
    //             error!("inbound data empty")
    //         }
    //     }
    //     Ok(())
    // }

    // pub async fn do_subscribe_market_data_v2(&mut self) -> Result<()> {
    //     let ret = self.init_client().await;
    //     if ret.as_ref().is_err() {
    //         return Err(anyhow!(ret.as_ref().err().unwrap().to_string()))
    //     }
    //     let mut client = ret.unwrap();

    //     let data = tokio_stream::iter(1..usize::MAX).map(move |_i| ContractMsg {
    //         exchange_no: "ALL".to_owned(),
    //         commodity_no: "ALL".to_string(),
    //         contract_no: "ALL".to_string(),
    //         channel_no: 0,
    //         subscribe: true,
    //     });
    //     let in_stream = data.take(usize::MAX);

    //     let response = match client.subscribe_market_data(in_stream).await {
    //         Ok(val) => val,
    //         Err(status) => {
    //             self.client = None;
    //             error!("{:?}", status);
    //             push_log(format!("MarketDataServer err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
    //             return Err(anyhow!(format!("MarketDataServer err")));
    //         },
    //     };

    //     let mut inbound = response.into_inner();
    //     while let Ok(inbound_data) = inbound.message().await {//从该流中获取下一条消息。
    //         // err 表示发送方发送了gRPC错误, 而不是有效的响应消息
    //         // Ok（无）表示发件人已关闭流，将不再传递任何消息
    //         // Ok（Some（val））表示发送方流式传输了有效的响应消息val
    //         if inbound_data.is_some() {
    //             let hqinfo = inbound_data.unwrap();
    //             info!("received hqinfo: {:?}", &hqinfo);
    //             if self.exchangeno.iter().find(|&x| x == &hqinfo.exchange_id).is_some() {
    //                 if let Err(e) = self.tx_tick.send(hqinfo.to_owned()) {
    //                     error!("send to channel error...........:{:?}", &e);
    //                 }
    //             }
    //         } else {
    //             error!("inbound data empty")
    //         }
    //     }
    //     Ok(())
    // }
}
