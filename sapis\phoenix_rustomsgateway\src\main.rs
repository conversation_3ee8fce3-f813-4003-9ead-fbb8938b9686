mod client;
mod config;
mod controller;
mod handler;
mod model;
// mod protofiles;
mod router;
mod server;
mod service;
use crate::config::settings::Settings;
use crate::server::ServerHandler;
use anyhow::Result;
use common::logclient::*;
use tracing::*;
// use utility::loggings;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/rustomsgateway.yaml";
    // loggings::log_init(cfg);

    let prefix = "phoenix_rustomsgateway";
    let dir = "./log";

    let settings = Settings::new().expect("init configurtion error");
    let level = "INFO";
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:#?}", &settings);

    // 1. 日志中心客户端初始化
    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", &settings.notification.vhost)).await;

    let server = prepare(&settings).await.expect("Init server error......");
    info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    log_debug(&format!(
        "server started, name: {} version: {} description: {}",
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;
    // info!("开始启动系统...");
    server_run(server).await
}
async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
    // let grpc_stub = create_controller(settings).await;

    let grpc = ServerHandler::new(&settings).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler) -> Result<(), Box<dyn std::error::Error>> {
    //let addr = "0.0.0.0:60000".parse().unwrap();
    //receive ctrl-c exit signal
    let (tx, _rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();
    // tokio::spawn(async move {
    tokio::signal::ctrl_c().await.ok();
    info!("Ctrl-c received, shutting down");
    tx.send(()).ok();
    // });

    // tonic::transport::Server::builder()
    //   .add_service(PhoenixExchangerServer::new(svr))
    //   .serve_with_shutdown(addr, async {
    //     rx.await.ok();
    //   })
    //   .await
    //   .expect("build server error");

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
// async fn prepare<AppEntry>(settings: &Settings) -> anyhow::Result<App<AppEntry> > {
//   let app = move || App::new().configure(router_config);
// }

// async fn server_run(settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
//   LogClient::get()?.push(LogLevel::Info, "server is runnig".into()).await;
//
//   let mut  tcp_enabled = true; // 根据你的需求设置HTTP开关
//   let mut ws_enabled = true; // 根据你的需求设置WebSocket开关
//   let mut gw = wscontroller::ServerController::new(&settings).await;
//
//   if ws_enabled {
//     let app = Router::new()
//         .route("/apigateway", get(websocket_handler))
//         //.layer(Extension(shared_data));
//         .layer(Extension(Arc::new(gw)));
//     let listener = tokio::net::TcpListener::bind(format!("{}:{}",settings.application.apphost,settings.application.appport))
//         .await
//         .unwrap();
//     info!("listening on {}", listener.local_addr().unwrap());
//
//     axum::serve(
//       listener,
//       app.into_make_service_with_connect_info::<SocketAddr>(),
//     ).await.unwrap();
//   }
//
//   if tcp_enabled{
//     let listener = tokio::net::TcpListener::bind(format!("{}:{}",settings.application.apphost,settings.application.appport))
//         .await
//         .unwrap();
//
//   }
//   Ok(())
// }
