[package]
name = "akaclient"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
messagecenter = { workspace = true }
utility = { workspace = true }
common = { workspace = true }
protoes = { workspace = true }

strum = { workspace = true }
strum_macros = { workspace = true }

async-global-executor = { workspace = true }

once_cell = { workspace = true }
local-ip-address = { workspace = true }

redis = { workspace = true }

anyhow = { workspace = true }
# log = { workspace = true }
# log4rs = { workspace = true }
tracing = { workspace = true }

rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }

rs-snowflake = { workspace = true }
dashmap = { workspace = true }
chrono = { workspace = true }
prost = { workspace = true }
rand = { workspace = true }
tonic = { workspace = true }
tokio = { workspace = true }

serde = { workspace = true }
serde_json = { workspace = true }
cached = { workspace = true }

[build-dependencies]
# tonic-build = { workspace = true, features = ["prost"] }
tonic-build.workspace = true
