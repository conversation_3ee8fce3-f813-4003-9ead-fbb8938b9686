use anyhow::{Result, anyhow};
use common::logclient::log_error;

use protoes::assetscenter::{
    PhoenixassetscenterQueryRequest, PhoenixassetscenterRequest, PhoenixassetscenterResponse, PositionMarginRateReq, PositionMarginRateResp, PositionPriceChangeReq, PositionPriceChangeResp,
    phoenixassetscenter_client::PhoenixassetscenterClient,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone)]
pub struct AssetsCenterClient {
    client: Arc<RwLock<Option<PhoenixassetscenterClient<Channel>>>>,
    uri: String,
}

impl AssetsCenterClient {
    pub async fn init(url: String) -> Self {
        let assetsclient = Arc::new(RwLock::new(match PhoenixassetscenterClient::connect(url.clone()).await {
            Ok(client) => Some(client),
            Err(err) => {
                error!("connect to AssetsCenter failed: {:?}", &err);
                log_error(&format!("Connected to AssetsCenter failed:{:?}", &err)).await;
                None
            }
        }));

        let client = AssetsCenterClient { client: assetsclient, uri: url };
        client.start_reconnect_listen().await;
        client
    }

    pub async fn start_reconnect_listen(&self) {
        let assetsclient_clone = Arc::clone(&self.client);
        let url = self.uri.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(3)) => {
                        if assetsclient_clone.read().await.is_none() {
                            info!("资产中心重新连接中......");
                            log_error(&format!("资产中心重新连接中......")).await;
                            let client = PhoenixassetscenterClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到资产中心,3秒后重连");
                                // std::thread::sleep(std::time::Duration::from_secs(3));
                                // continue;
                            }else{
                                info!("资产中心连接成功......");
                                let mut wr = assetsclient_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
        });
    }

    pub async fn phoenix_assets_change(&self, request: &PhoenixassetscenterRequest) -> Result<PhoenixassetscenterResponse> {
        if self.client.read().await.is_none() {
            error!("资产中心未连接");
            return Err(anyhow!("资产中心未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().phoenix_assets_change(request.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("资产中心 phoenix_assets_change error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let assets_query = ret.unwrap();
        Ok(assets_query.into_inner())
    }

    pub async fn phoenix_assets_query(&self, request: &PhoenixassetscenterQueryRequest) -> Result<PhoenixassetscenterResponse> {
        if self.client.read().await.is_none() {
            error!("资产中心未连接");
            return Err(anyhow!("资产中心未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().phoenix_assets_query(request.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("资产中心 phoenix_assets_query error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let assets_query = ret.unwrap();
        Ok(assets_query.into_inner())
    }

    pub async fn phoenix_positions_marginrate_change(&self, request: &PositionMarginRateReq) -> Result<PositionMarginRateResp> {
        if self.client.read().await.is_none() {
            error!("资产中心未连接");
            return Err(anyhow!("资产中心未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().phoenix_positions_marginrate_change(request.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("资产中心 phoenix_positions_marginrate_change error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let assets_query = ret.unwrap();
        Ok(assets_query.into_inner())
    }

    pub async fn phoenix_positions_price_change(&self, request: &PositionPriceChangeReq) -> Result<PositionPriceChangeResp> {
        if self.client.read().await.is_none() {
            error!("资产中心未连接");
            return Err(anyhow!("资产中心未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().phoenix_positions_price_change(request.to_owned()).await;
        if ret.as_ref().is_err() {
            log_error(&format!("资产中心 phoenix_positions_price_change error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let assets_query = ret.unwrap();
        Ok(assets_query.into_inner())
    }
}
