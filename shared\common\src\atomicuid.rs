use std::{
    sync::atomic::{AtomicU64, Ordering},
    time::{SystemTime, UNIX_EPOCH},
};

pub struct AtomicUidGenerator {
    machine_id: u64,
    node_id: u64,
    sequence: AtomicU64,
}

impl AtomicUidGenerator {
    pub fn new(machine_id: i32, node_id: i32) -> Self {
        Self {
            machine_id: machine_id as u64,
            node_id: node_id as u64,
            sequence: AtomicU64::new(0),
        }
    }

    pub fn get_uid(&self) -> i64 {
        let timestamp = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
        let seq = self.sequence.fetch_add(1, Ordering::Relaxed) & 0xFFF;

        let uid = (timestamp << 22) | (self.machine_id << 17) | (self.node_id << 12) | seq;
        uid as i64
    }
}
