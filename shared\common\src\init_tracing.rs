use chrono::FixedOffset;
// use std::io;
use file_rotate::{
    suffix::{AppendTimestamp, FileLimit},
    ContentLimit, FileRotate, TimeFrequency,
};
use tracing_appender::non_blocking::WorkerGuard;
use tracing_subscriber::{
    fmt::{self, format, time::FormatTime},
    layer::SubscriberExt as _,
    util::SubscriberInitExt as _,
    Layer,
};
struct CustomLocalTime;

impl FormatTime for CustomLocalTime {
    fn format_time(&self, w: &mut tracing_subscriber::fmt::format::Writer<'_>) -> std::fmt::Result {
        let offset = FixedOffset::east_opt(8 * 3600).unwrap(); // Example: UTC+8
        let local_time = chrono::Utc::now().with_timezone(&offset);
        write!(w, "{}", local_time.format("%Y-%m-%d %H:%M:%S%.3f"))
    }
}

// struct CustomFormatter;

// impl<S, N> FormatEvent<S, N> for CustomFormatter
// where
//     S: tracing::Subscriber + for<'a> tracing_subscriber::registry::LookupSpan<'a>,
//     N: for<'a> FormatFields<'a> + 'static,
// {
//     fn format_event(&self, ctx: &fmt::FmtContext<'_, S, N>, mut writer: tracing_subscriber::fmt::format::Writer<'_>, event: &tracing::Event<'_>) -> std::fmt::Result {
//         // Format values from the event's's metadata:
//         let metadata = event.metadata();
//         write!(&mut writer, "{} {}: ", metadata.level(), metadata.target())?;
//         // Format all the spans in the event's span context.
//         if let Some(scope) = ctx.event_scope() {
//             for span in scope.from_root() {
//                 write!(writer, "{}", span.name())?;
//                 // `FormattedFields` is a formatted representation of the span's
//                 // fields, which is stored in its extensions by the `fmt` layer's
//                 // `new_span` method. The fields will have been formatted
//                 // by the same field formatter that's provided to the event
//                 // formatter in the `FmtContext`.
//                 let ext = span.extensions();
//                 let fields = &ext.get::<FormattedFields<N>>().expect("will never be `None`");
//                 // Skip formatting the fields if the span had no fields.
//                 if !fields.is_empty() {
//                     write!(writer, "{{{}}}", fields)?;
//                 }
//                 write!(writer, ": ")?;
//             }
//         }
//         // Format the fields of the event itself.
//         ctx.field_format().format_fields(writer.by_ref(), event)?;
//         writeln!(writer)
//     }
// }

pub fn init_tracing(prefix: &str, dir: &str, level: &str) -> WorkerGuard {
    // let prefix = "nodipexam";
    // let dir = "./log";
    // let local_time = time::LocalTime::new(time::macros::format_description!("%Y-%m-%d %H:%M:%S%.3f"));
    let console_layer = fmt::layer()
        .event_format(format().compact())
        .with_ansi(false)
        .with_timer(CustomLocalTime)
        .with_writer(std::io::stdout)
        .with_target(true)
        .with_line_number(true);

    // 设置每日滚动，保留 7 天
    // 使用 AppendTimestamp，設置時間戳格式為本地時間
    // let suffix = AppendTimestamp::default(FileLimit::MaxFiles(7)).with_format("%Y%m%d"); // 格式：YYYYMMDD
    let suffix = AppendTimestamp::with_format("%Y%m%d", FileLimit::MaxFiles(7), file_rotate::suffix::DateFrom::Now);

    let log_writer = FileRotate::new(
        &format!("{}/{}.log", dir, prefix),
        // AppendTimestamp::default(FileLimit::Age(chrono::Duration::days(7))),
        suffix,
        ContentLimit::Time(TimeFrequency::Daily),
        file_rotate::compression::Compression::None,
        None,
    );
    // let time_format = format_description!("[year]-[month]-[day] [hour]:[minute]:[second]");
    let (non_blocking_appender, guard) = tracing_appender::non_blocking(log_writer); // 输出非阻塞
    let file_layer = fmt::layer()
        .event_format(format().compact())
        .with_ansi(false)
        .with_writer(non_blocking_appender)
        .with_line_number(true)
        .with_target(true)
        // .with_timer(tracing_subscriber::fmt::time::UtcTime::rfc_3339())
        .with_timer(CustomLocalTime)
        .boxed();

    // add customer format
    tracing_subscriber::registry().with(file_layer).with(console_layer).with(tracing_subscriber::EnvFilter::new(level)).init();

    guard
}
