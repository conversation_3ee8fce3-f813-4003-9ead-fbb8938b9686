use anyhow::{Result, anyhow};
use common::logclient::log_error;

use protoes::{
    hqcenter::{KLineHqInfo, KLineHqReq, LastPriceMsgReq, PreDealNumReq, TickHqReq, svr_post_subscribe_hq_msg_client::SvrPostSubscribeHqMsgClient},
    hqmsg::YsHqInfo,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone)]
pub struct HqCenterClient {
    client: Arc<RwLock<Option<SvrPostSubscribeHqMsgClient<Channel>>>>,
    uri: String,
}

impl HqCenterClient {
    pub async fn init(url: String) -> Self {
        let hqcenterclient = Arc::new(RwLock::new(match SvrPostSubscribeHqMsgClient::connect(url.clone()).await {
            Ok(client) => Some(client),
            Err(err) => {
                error!("connect to HqCenterClient failed: {:?}", &err);
                log_error(&format!("Connected to HqCenterClient failed:{:?}", &err)).await;
                None
            }
        }));

        let client = HqCenterClient { client: hqcenterclient, uri: url };
        client.start_reconnect_listen().await;
        client
    }

    pub async fn start_reconnect_listen(&self) {
        let hqcenterclient_clone = Arc::clone(&self.client);
        let url = self.uri.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(3)) => {
                        if hqcenterclient_clone.read().await.is_none() {
                            info!("hqcenter重新连接中......");
                            log_error(&format!("hqcenter重新连接中......")).await;
                            let client = SvrPostSubscribeHqMsgClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到hqcenter,3秒后重连");
                                // std::thread::sleep(std::time::Duration::from_secs(3));
                                // continue;
                            }else{
                                info!("hqcenter连接成功......");
                                let mut wr = hqcenterclient_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
        });
    }

    pub async fn get_pre_deal_amount(&self, stock_code: &String, exchange_id: i32, time: &String) -> Result<i32> {
        if self.client.read().await.is_none() {
            error!("hqcenter未连接");
            return Err(anyhow!("hqcenter未连接"));
        }
        let mut wr = self.client.write().await;
        match wr
            .as_mut()
            .unwrap()
            .get_pre_deal_amount(PreDealNumReq {
                stock_code: stock_code.to_owned(),
                exchange_id,
                time: time.to_string(),
            })
            .await
        {
            Ok(val) => Ok(val.into_inner().prev_period_amount),
            Err(status) => {
                log_error(&format!("hqcenter get_pre_deal_amount异常{}", status.to_string())).await;
                *wr = None;
                error!("{:?}", status);
                return Err(anyhow!(status.to_string()));
            }
        }
    }

    pub async fn post_tick_hq(&self, stock_code: &String) -> Result<YsHqInfo> {
        if self.client.read().await.is_none() {
            error!("hqcenter未连接");
            return Err(anyhow!("hqcenter未连接"));
        }
        let mut wr = self.client.write().await;
        match wr
            .as_mut()
            .unwrap()
            .post_tick_hq(TickHqReq {
                strcontractno: stock_code.to_string(),
                iticktype: 1,
                ticktime: 0,
                realtime: 1,
            })
            .await
        {
            Ok(val) => Ok(val.into_inner().tickhqinfo.first().ok_or(anyhow!("找不到数据"))?.clone()),
            Err(status) => {
                log_error(&format!("hqcenter post_tick_hq异常{}", status.to_string())).await;
                *wr = None;
                error!("{:?}", status);
                return Err(anyhow!(status.to_string()));
            }
        }
    }

    pub async fn post_history_k_line_hq(&mut self, stock_code: String, end_time: &String) -> anyhow::Result<Vec<KLineHqInfo>> {
        if self.client.read().await.is_none() {
            error!("hqcenter未连接");
            return Err(anyhow!("hqcenter未连接"));
        }
        let mut wr = self.client.write().await;
        info!("请求 post_history_k_line_hq stock_code: {} end_time: {}", &stock_code, end_time);
        let ret = wr
            .as_mut()
            .unwrap()
            .post_history_k_line_hq(KLineHqReq {
                strcontractno: stock_code,
                strklinetype: "24".to_string(),
                strendtime: format!("{}000000", end_time),
                limit: 0,
                realtime: 1,
            })
            .await;

        if ret.as_ref().is_err() {
            let str = ret.as_ref().err().unwrap().to_string();
            log_error(&format!("hqcenter post_history_k_line_hq异常{}", str)).await;
            *wr = None;
            return Err(anyhow!(str));
        }

        let kline = ret.unwrap();
        info!("post_history_k_line_hq 返回: {:?}", &kline);
        Ok(kline.into_inner().klineinfo)
    }

    pub async fn get_last_price(&mut self, stock_code: String, exchange_id: i32) -> anyhow::Result<f64> {
        if self.client.read().await.is_none() {
            error!("hqcenter未连接");
            return Err(anyhow!("hqcenter未连接"));
        }
        let mut wr = self.client.write().await;
        info!("请求 post_history_k_line_hq stock_code: {} exchange_id: {}", &stock_code, exchange_id);
        let ret = wr.as_mut().unwrap().get_last_price(LastPriceMsgReq { stock_code, exchange_id }).await;
        if ret.as_ref().is_err() {
            let str = ret.as_ref().err().unwrap().to_string();
            log_error(&format!("hqcenter post_history_k_line_hq异常{}", str)).await;
            *wr = None;
            return Err(anyhow!(str));
        }

        let ret = ret.unwrap();
        info!("post_history_k_line_hq 返回: {:?}", &ret);

        let last_price = ret.into_inner();
        if last_price.data.is_none() {
            return Err(anyhow!("last price are not found."));
        }

        Ok(last_price.data.unwrap().last_price)
    }
}
