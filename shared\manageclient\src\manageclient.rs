use anyhow::Result;
use anyhow::anyhow;
use common::logclient::log_error;
use protoes::managerunit::AlgorithmReq;
use protoes::managerunit::ModifyAlgorithmReq;
use protoes::managerunit::ModifyUnitReq;
use protoes::managerunit::OptionQuotationReq;
use protoes::managerunit::OptionQuotationResp;
use protoes::managerunit::QueryAlgorithmResp;
use protoes::managerunit::manager_unit_client::ManagerUnitClient;
use std::sync::Arc;
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone)]
pub struct Manageclient {
    client: Arc<RwLock<Option<ManagerUnitClient<Channel>>>>,
    uri: String,
}

impl Manageclient {
    pub async fn init(url: String) -> Self {
        let manageclient = Arc::new(RwLock::new(match ManagerUnitClient::connect(url.clone()).await {
            Ok(client) => Some(client),
            Err(err) => {
                error!("connect to Managecenter failed: {:?}", &err);
                log_error(&format!("Connected to Managecenter failed:{:?}", &err)).await;
                None
            }
        }));

        let client = Manageclient { client: manageclient, uri: url };
        client.start_reconnect_listen().await;
        client
    }

    pub async fn start_reconnect_listen(&self) {
        let manageclient_clone = Arc::clone(&self.client);
        let url = self.uri.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(3)) => {
                        if manageclient_clone.read().await.is_none() {
                            info!("manage管理端重新连接中......");
                            log_error(&format!("manage管理端重新连接中......")).await;
                            let client = ManagerUnitClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到manage管理端,3秒后重连");
                                // std::thread::sleep(std::time::Duration::from_secs(3));
                                // continue;
                            }else{
                                info!("manage管理端连接成功......");
                                let mut wr = manageclient_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
        });
    }

    //修改状态
    pub async fn modify_unit_state(&self, request: ModifyUnitReq) -> Result<()> {
        if self.client.read().await.is_none() {
            error!("manage管理端未连接");
            return Err(anyhow!("manage管理端未连接"));
        }
        let mut wr = self.client.write().await;
        let ret = wr.as_mut().unwrap().modify_unit_state(request).await;
        if ret.as_ref().is_err() {
            log_error(&format!("manage管理端 modify_unit_state  error: {:?}", ret.as_ref().err().unwrap())).await;
            *wr = None;
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        Ok(())
    }

    /// 更新算法单
    pub async fn modify_algorithm(&self, uid: i64, ordernum: i32, dealnum: i32, last_execute_time: i64, status: i32) -> Result<()> {
        if self.client.read().await.is_none() {
            error!("manage管理端未连接");
            return Err(anyhow!("manage管理端未连接"));
        }
        let mut wr = self.client.write().await;
        match wr
            .as_mut()
            .unwrap()
            .modify_algorithm(ModifyAlgorithmReq {
                uid,
                ordernum,
                dealnum,
                last_execute_time,
                status,
            })
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                log_error(&format!("manage管理端 modify_algorithm异常")).await;
                *wr = None;
                error!("{:?}", e);
                return Err(anyhow!(e.to_string()));
            }
        }
    }

    /// 查询算法单
    pub async fn query_algorithm(&self) -> Result<Vec<QueryAlgorithmResp>> {
        if self.client.read().await.is_none() {
            error!("manage管理端未连接");
            return Err(anyhow!("manage管理端未连接"));
        }
        let mut wr = self.client.write().await;
        match wr.as_mut().unwrap().query_algorithm(AlgorithmReq {}).await {
            Ok(val) => Ok(val.into_inner().data),
            Err(e) => {
                log_error(&format!("manage管理端 query_algorithm异常")).await;
                *wr = None;
                error!("{:?}", e);
                return Err(anyhow!(e.to_string()));
            }
        }
    }

    pub async fn query_option_quotations(&mut self) -> Option<OptionQuotationResp> {
        let req = OptionQuotationReq::default();

        if self.client.read().await.is_none() {
            error!("manage管理端未连接");
            return None;
        }
        let mut wr = self.client.write().await;
        match wr.as_mut().unwrap().query_option_quotation(req).await {
            Ok(data) => {
                let resp = data.into_inner();
                Some(resp)
            }
            Err(err) => {
                log_error(&format!("manage管理端 query_option_quotations异常")).await;
                *wr = None;
                error!("{:?}", err);
                return None;
            }
        }
    }
}
