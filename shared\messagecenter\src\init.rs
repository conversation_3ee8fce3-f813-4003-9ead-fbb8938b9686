use super::{notificationclient::NotificationClient, quotationclient::QuotationClient};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tracing::*;

pub async fn init_quotation_listen(mut client: QuotationClient) {
    let mut retry_interval = tokio::time::interval(Duration::from_secs(1));

    tokio::spawn(async move {
        // retry_interval.tick().await;
        loop {
            tokio::select! {
                _ = retry_interval.tick() => {
                    info!("trying quotation client consume in init......");
                    if let Err(err) = client.try_consume().await{
                        error!("quotation client consume error: {:?}. start to re-connecting", err);
                        let _ = client.retry_consume().await;
                    }
                }
            }
        }
    });
}

pub async fn init_notification_client(client: Arc<RwLock<NotificationClient>>) {
    let mut retry_interval = tokio::time::interval(Duration::from_secs(1));

    info!("trying client connection in init......");
    if let Err(err) = client.write().await.try_connect().await {
        error!("client connection error: {:?}. start to re-connecting", err);
        let _ = client.write().await.retry_connect().await;
    }

    tokio::spawn(async move {
        retry_interval.tick().await; //skip first tick
        loop {
            tokio::select! {
                _ = retry_interval.tick() => {
                    if !client.read().await.is_connected().await {
                        warn!("start to re-connecting");
                        let _ = client.write().await.retry_connect().await;
                    }

                }
            }
        }
    });
    // Ok(())
}

pub async fn init_notification_listen(client: Arc<RwLock<NotificationClient>>) {
    tokio::spawn(async move {
        loop {
            if client.read().await.is_connected().await {
                info!("trying notification client consume in init......");
                if let Err(err) = client.read().await.try_consume().await {
                    error!("notification client consume error: {:?}. start to re-connecting", err);
                }
                warn!("notification client consume exit");
            }
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    });
}
