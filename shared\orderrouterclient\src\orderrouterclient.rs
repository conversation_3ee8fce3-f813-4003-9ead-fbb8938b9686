// use crate::server::service::push_log;
use anyhow::{Result, anyhow};
use common::logclient::log_error;
use protoes::phoenixordermsg::{ExecMsg, ExecType, MsgType, RouterMsg};
use protoes::phoenixorderrouter::order_router_service_client::OrderRouterServiceClient;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::sync::{broadcast, mpsc};
use tonic::transport::Channel;
use tracing::{error, info};

#[derive(Clone)]
pub struct OrderRouterClient {
    client: Arc<RwLock<Option<OrderRouterServiceClient<Channel>>>>,
    tx_order: broadcast::Sender<RouterMsg>, //收订单消息 -> 报盘
    tx_confirm: mpsc::Sender<ExecMsg>,
    tx_filled: mpsc::Sender<ExecMsg>,
    tx_canceled: mpsc::Sender<ExecMsg>,
    tx_rejected: mpsc::Sender<ExecMsg>,
    uri: String,
}

impl OrderRouterClient {
    pub async fn new(
        uri: &String,
        tx_order: broadcast::Sender<RouterMsg>,
        tx_confirm: mpsc::Sender<ExecMsg>,
        tx_filled: mpsc::Sender<ExecMsg>,
        tx_canceled: mpsc::Sender<ExecMsg>,
        tx_rejected: mpsc::Sender<ExecMsg>,
    ) -> Self {
        // let routerclient = Arc::new(RwLock::new(None::<OrderRouterServiceClient<Channel>>));
        // let client = OrderRouterServiceClient::connect(uri.to_owned()).await;
        // if client.is_ok() {
        //     info!("订单路由连接成功....");
        //     let mut wr = routerclient.write().await;
        //     *wr = Some(client.unwrap());
        // }
        let routerclient = Arc::new(RwLock::new(match OrderRouterServiceClient::connect(uri.clone()).await {
            Ok(client) => Some(client),
            Err(err) => {
                error!("connect to OrderCenterService failed: {:?}", &err);
                log_error(&format!("Connected to OrderCenterService failed:{:?}", &err)).await;
                None
            }
        }));
        let client = OrderRouterClient {
            client: routerclient,
            tx_order,
            tx_confirm,
            tx_filled,
            tx_canceled,
            tx_rejected,
            uri: uri.to_owned(),
        };
        client.start_reconnect_listen().await;
        client
    }

    pub async fn start_reconnect_listen(&self) {
        let routerclient_clone = Arc::clone(&self.client);
        let url = self.uri.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(1)) => {
                        if routerclient_clone.read().await.is_none() {
                            info!("订单路由重新连接中......");
                            let client = OrderRouterServiceClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到订单路由中心,3秒后重连");
                            }else{
                                info!("订单路由中心连接成功......");
                                let mut wr = routerclient_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
        });
    }

    // pub async fn init_client(&self) -> Result<OrderRouterServiceClient<Channel>> {
    //     if self.client.is_some() {
    //         return Ok(self.client.clone().unwrap());
    //     } else {
    //         let ret = OrderRouterServiceClient::connect(self.uri.to_owned()).await;
    //         if ret.as_ref().is_err() {
    //             push_log(format!("connect to OrderRouter failed: {:?}", self.uri).as_str()).await;
    //             return Err(anyhow!(format!("connect to OrderRouter failed")));
    //         }
    //         let client = ret.expect("connect to OrderRouter failed");
    //         info!("订单路由连接成功....");
    //         self.client = Some(client);
    //         return Ok(self.client.clone().unwrap());
    //     }
    // }

    pub async fn order_routing(&self) -> Result<()> {
        if self.client.read().await.is_none() {
            error!("订单路由未连接");
            return Err(anyhow!("订单路由未连接"));
        }

        let mut rx = self.tx_order.subscribe();
        //报单消息
        let outbound = async_stream::stream! {
            loop {
                if let Ok(val) = rx.recv().await {
                    info!("推送到报盘: {:?}", &val);
                    yield val;
                }
            }
        };
        let mut wr = self.client.write().await;

        let response = match wr.as_mut().unwrap().order_routing(outbound).await {
            Ok(val) => val,
            Err(status) => {
                // self.client = None;
                *wr = None;
                error!("{:?}", status);
                log_error(&format!("OrderRouter center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!(format!("OrderRouter err")));
            }
        };

        let mut inbound = response.into_inner();
        while let Ok(inbound_data) = inbound.message().await {
            if inbound_data.is_some() {
                //回报消息
                let value = inbound_data.unwrap();
                info!("回执: {:?}", value);
                self.repay(&value).await;
            } else {
                info!("inbound data empty");
            }
        }
        Ok(())
    }

    pub async fn repay(&self, router_msg: &RouterMsg) {
        match router_msg.msg_type() {
            MsgType::Register => {}
            MsgType::Order => {}
            MsgType::Exec => {
                if let Some(msg_content) = router_msg.msg_content.clone() {
                    if let Some(exec_msg) = msg_content.exec_msg {
                        match exec_msg.exec_type() {
                            ExecType::ExecUndef => {}
                            ExecType::Confirm => {
                                info!("订单{}确认回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_confirm.send(exec_msg).await {
                                    error!("确认消息发送失败: {:?}", err);
                                    log_error(&format!("确认消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Filled => {
                                info!("订单{}成交回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_filled.send(exec_msg).await {
                                    error!("成交消息发送失败: {:?}", err);
                                    log_error(&format!("成交消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Canceled => {
                                info!("订单{}撤单回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_canceled.send(exec_msg).await {
                                    error!("撤单消息发送失败: {:?}", err);
                                    log_error(&format!("撤单消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                            ExecType::Rejected => {
                                info!("订单{}废单回报: {:?}", exec_msg.order_id, exec_msg);
                                if let Err(err) = self.tx_rejected.send(exec_msg).await {
                                    error!("废单消息发送失败: {:?}", err);
                                    log_error(&format!("废单消息发送失败 error:{}", err.to_string())).await;
                                }
                            }
                        }
                    }
                }
            }
            MsgType::Response => {}
        }
    }

    // pub async fn order_routing(&mut self) -> Result<()> {
    //     let mut rx = self.tx_order.subscribe();
    //     //报单消息
    //     let outbound = async_stream::stream! {
    //         loop {
    //             if let Ok(val) = rx.recv().await {
    //                 info!("推送到报盘: {:?}", &val);
    //                 yield val;
    //             }
    //         }
    //     };

    //     let sub_ret = self.client.order_routing(outbound).await;
    //     if sub_ret.as_ref().is_err() {
    //         error!("router error:{:?}", sub_ret.as_ref().err());
    //         //订单到这里失败了怎么办 ?
    //         return Err(anyhow!("order router error"));
    //     }
    //     let response = sub_ret.unwrap();

    //     let mut inbound = response.into_inner();
    //     while let Ok(inbound_data) = inbound.message().await {
    //         if inbound_data.is_some() {
    //             //回报消息
    //             let value = inbound_data.unwrap();
    //             info!("回执: {:#?}", value);
    //             self.repay(&value).await;
    //         } else {
    //             info!("inbound data empty");
    //         }
    //     }
    //     Ok(())
    // }

    // pub async fn retry_order_routing(&mut self) -> Result<()> {
    //     let client = OrderRouterServiceClient::connect(self.uri.to_owned()).await;
    //     if client.as_ref().is_err() {
    //         error!("{:?}", client);
    //         return Err(anyhow!("{:?}", client));
    //     }
    //     self.client = client.unwrap();
    //     let get_ret = self.order_routing().await;
    //     if get_ret.as_ref().is_err() {
    //         error!("{:?}", get_ret.as_ref().err().unwrap());
    //         return Err(anyhow!("{:?}", get_ret.as_ref().err().unwrap()));
    //     }
    //     Ok(())
    // }
}
