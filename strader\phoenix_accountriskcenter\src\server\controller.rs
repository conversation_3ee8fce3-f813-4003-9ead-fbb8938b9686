use super::service::accountassetsservice::PhoenixAccountAssetsService;
use super::service::accountstockpositionservice::PhoenixAccountStockPositionService;
use super::service::basiccacheservice::BasicCacheService;
use super::service::commonservice::CommonService;
// use super::service::hqcenterservice::HqCenterServcie;
use super::service::userassetsservice::UserAssetsService;
use super::service::userpositionservice::UserPositionService;
// use super::service::{assetsservice::AssetsDataServie, basedataservice::BaseDataService};
use crate::config::settings::Settings;
use crate::dataview::userassets::UserAssetsData;
use crate::dataview::userposition::UserPositionData;
// use crate::protofiles::assetscenter::phoenixassetscenter_client::PhoenixassetscenterClient;
use crate::server::service::recalculate;
use crate::{
    app::constdata,
    dataservice::{dbsetup::DbConnection, entities::prelude::*},
    result,
};
use akaclient::{akaclient::AkaClient, calcfee::FeeDetail};
use anyhow::{anyhow, Result};
use assetscenterclient::AssetsCenterClient;
use common::constant::{self, OrderDirection, YesOrNo};
use common::logclient::log_error;
use manageclient::Manageclient;
use messagecenter::notificationclient::NotificationClient;
use protoes::assetscenter::{PhoenixassetscenterQueryRequest, PositionMarginRateReq, PositionPriceChangeReq};
use protoes::phoenixaccountriskcenter::{
    MarginRatioReq, MarginRatioResp, PhoenixAccountAssetsInfo, PhoenixAccountQueryRequest, PhoenixAccountResetRequest, PhoenixAssetsResponse, PhoenixStockPositionRequest, PhoenixStockPositions, PhoenixTransferRequest,
    PhoenixUserPositions, UserAssetsReq, UserAssetsResp, UserPositionReq, UserPositionResp, UserRqListResp, UserRqReq, UserRqResp,
};
use protoes::phoenixakacenter::Currency;
use protoes::phoenixnotification::{
    AccountInfoChange, ExchangeRateChange, MessageBody, NotificationAsset, NotificationMessage, NotificationOrderExec, NotificationPosition, NotificationType, OrderExecType, StockSuspensionChange,
    UserClientNotifyDataChange, UserStockMarginChange,
};
use rust_decimal::prelude::{FromPrimitive, ToPrimitive};
use rust_decimal::Decimal;
use std::collections::{HashMap, HashSet};
// use std::time::SystemTime;
use tracing::*;
use utility::timeutil::current_timestamp;
use utility::{errors, timeutil};

// use std::collections::HashMap;
use crate::client::satclient::SatClient;
use crate::dataservice::entities::{phoenix_account_assets_his, phoenix_stockposition_channel_his};
use crate::server::service::accountdividend::{dvd_register, update_account_assets_by_dividend_record_his};
use crate::server::service::securities_borrow_pool::SecuritiesBorrowPool;
use akaclient::akaclient::get_currency_by_code;
use akaclient::calcfee::FeeField;
use common::redisclient::redispool::RedisClient;
use common::AtomicUidGenerator;
use itertools::Itertools;
use linked_hash_map::LinkedHashMap;
use sea_orm::{ColumnTrait, Condition};
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tonic::Status;

#[derive(Debug)]
#[allow(dead_code)]
pub enum PersistData {
    AccountAssets(Box<Vec<PhoenixAccountAssets>>),
    AccountAssetsHis(Box<Vec<PhoenixAccountAssetsHis>>),
    AccountPosition(Box<Vec<PhoenixStockPositionChannel>>),
    AccountPositionHis(Box<Vec<PhoenixStockPositionChannelHis>>),
    UserAssets(Box<Vec<PhoenixUserAssets>>),
    UserAssetsHis(Box<Vec<PhoenixUserAssetsHis>>),
    TransDetail(Box<PhoenixTransDetail>),
    ResetDetail(Box<PhoenixAccountResetDetail>),
    RiskUserAssets(Box<PhoenixRiskDetails>),
    RiskCancelUserAssets(Box<i64>),
}

#[derive(Debug)]
#[allow(dead_code)]
pub enum NotificationData {
    UserPosition(Box<NotificationPosition>),
    UserAssets(Box<NotificationAsset>),
    UserRateMargin(Box<UserClientNotifyDataChange>),
}

#[derive(Clone)]
pub struct PhoenixController {
    pub dbconn: Arc<DbConnection>,
    //数据库连接
    // pub basedata_svc: Arc<BaseDataService>,
    pub setting: Arc<RwLock<Settings>>,
    pub account_position_svc: Arc<PhoenixAccountStockPositionService>,
    pub account_assets_svc: Arc<PhoenixAccountAssetsService>,
    pub aka_svc: Arc<AkaClient>,
    //基础数据服务
    pub sat_client: Arc<SatClient>,
    pub redis_client: Arc<RedisClient>,
    pub uidsvc: Arc<AtomicUidGenerator>,
    pub sys_info: Arc<RwLock<PhoenixSysSystem>>,
    pub tx_persist: mpsc::Sender<PersistData>,
    pub ignore_accounts: Vec<i64>,
    // pub ignore_fee_accounts: Vec<i64>,
    pub user_assets_svc: Arc<UserAssetsService>,
    pub user_position_svc: Arc<UserPositionService>,
    pub securities_borrow_pool: Arc<SecuritiesBorrowPool>,
    pub basic_cache_svc: Arc<BasicCacheService>,
    pub tx_assets: tokio::sync::mpsc::Sender<(i64, i64)>,
    //推送用户id,推送给客户端的信息，none代表不推送。一般最新价发生变化的不需要推送。这个客户端自己订阅处理
    pub tx_rq: tokio::sync::mpsc::Sender<(i64, i64, i32)>,
    pub assetscenter_svc: Arc<AssetsCenterClient>,
    pub manager_svc: Arc<Manageclient>,
    pub notify: Arc<NotificationClient>,
}

impl PhoenixController {
    #[tracing::instrument(name = "总账户初始化", skip_all)]
    pub async fn init(&self, is_async: bool) -> Result<()> {
        let currenttime = timeutil::current_timestamp_mills();
        // info!("开始从资产中心获取数据......");

        let param = PhoenixassetscenterQueryRequest {
            unit_id: vec![],
            query_type: common::constant::QueryAssetsType::Both as i32,
            user_id: vec![],
            currency: "".to_string(),
        };
        let assets_vec = self.assetscenter_svc.phoenix_assets_query(&param).await;
        if let Err(_) = assets_vec {
            log_error("accountriskcenter查询资产数据失败，缓存初始化失败，服务继续启动...").await;
            return Ok(());
        }
        info!("查询资产耗费时间:{}毫秒", timeutil::current_timestamp_mills() - currenttime);

        // info!("资产数据获取结束，开始初始化交易账户动态数据......");
        let assets_results = assets_vec.unwrap();
        let assets_results = assets_results.assetsinfo;
        let mut assets_vec = Vec::new();
        let mut positions_vec: Vec<_> = Vec::new();
        for val in assets_results.iter() {
            let mut currency = 0;
            if val.assets.is_some() {
                let ast = val.assets.as_ref().unwrap().to_owned();
                currency = get_currency_by_code(&ast.currency_no) as i32;
                assets_vec.push((ast, val.user_id));
            }

            positions_vec.extend(
                val.positionsinfo
                    .iter()
                    .map(|item| UserPositionData::convert_positioninfo_to_userposition(item, val.user_id, currency))
                    .collect_vec(),
            );
        }
        info!("持仓数据获取结束，开始初始化交易账户资产数据......");
        self.user_assets_svc.init(&assets_vec, &self.aka_svc).await?;
        info!("持仓数据获取结束，开始初始化交易账户持仓数据......");
        self.user_position_svc.init(&mut positions_vec, &self.aka_svc).await?;
        info!("持仓数据获取结束，开始初始化融券池数据......");
        self.securities_borrow_pool.init(&self.aka_svc, &self.user_position_svc).await?;
        info!("资产初始化共耗费时间:{}毫秒", timeutil::current_timestamp_mills() - currenttime);
        //重算资产所有人
        if is_async {
            _ = self.tx_assets.send((0, 0)).await;
        } else {
            self.re_calculate_user_assets(0, 0, true).await?;
        }

        //-----------------END OF 新--------------------

        info!("开始初始化分帐户数据......");
        //初始化分帐户资产
        self.account_assets_svc.init(&self.dbconn).await?;
        //初始化分帐户持仓
        self.account_position_svc.init(&self.dbconn).await?;
        info!("分账户资产初始化共耗费时间，{}", timeutil::current_timestamp_mills() - currenttime);
        Ok(())
    }

    //init_position_channel 初始化分帐户持仓
    pub async fn init_position_channel(&self) -> Result<()> {
        //初始化分帐户持仓
        self.account_position_svc.init(&self.dbconn).await?;
        Ok(())
    }

    //推送用户信息变更给客户端
    pub async fn publish_data(&self, data: &NotificationData) -> Result<()> {
        // 1）组包
        // 2）推送
        info!("publish_data:{:?}", data);
        //推送2秒后推送，保证重算资产已处理完
        match data {
            NotificationData::UserAssets(assets) => {
                let routing_key = format!("notification.accountrisk.client.asseets.{}", assets.unit_id);
                let assets = assets.as_ref().to_owned();
                let msgbody = MessageBody {
                    msg_asset: Some(assets),
                    ..Default::default()
                };
                let msg = NotificationMessage {
                    msg_type: NotificationType::AssetChanged as i32,
                    msg_body: Some(msgbody),
                };
                _ = self.notify.try_publish(routing_key.as_str(), &msg).await;
            }
            NotificationData::UserPosition(positions) => {
                let routing_key = format!("notification.accountrisk.client.positions.{}", positions.unit_id);
                let positions = positions.as_ref().to_owned();
                let msgbody = MessageBody {
                    msg_position: Some(positions),
                    ..Default::default()
                };
                let msg = NotificationMessage {
                    msg_type: NotificationType::PositionChanged as i32,
                    msg_body: Some(msgbody),
                };
                _ = self.notify.try_publish(routing_key.as_str(), &msg).await;
            }

            NotificationData::UserRateMargin(ratemargin) => {
                let routing_key = format!("notification.accountrisk.client.marginrate");
                let ratemargin = ratemargin.as_ref().to_owned();
                let msgbody = MessageBody {
                    msg_userclientnotify: Some(ratemargin),
                    ..Default::default()
                };
                let msg = NotificationMessage {
                    msg_type: NotificationType::UserClientNofity as i32,
                    msg_body: Some(msgbody),
                };
                _ = self.notify.try_publish(routing_key.as_str(), &msg).await;
            }
        }
        Ok(())
    }

    //结算完重新刷新系统时间
    pub async fn refresh_system_date(&self) {
        let sys_info = PhoenixSysSystem::query(&self.dbconn).await.expect("query phoenix_sys_system info error");
        *self.sys_info.write().await = sys_info;
    }

    //重制融券池
    pub async fn reset_securities_borrow_pool(&self) -> Result<()> {
        self.securities_borrow_pool.reset(&self.aka_svc, &self.user_position_svc).await
    }

    pub async fn get_stock_positions_quotation_key(&self) -> HashMap<String, i32> {
        let mut qkeys: HashMap<String, i32> = HashMap::new();
        let pos_stocks = self.user_position_svc.query_user_positions(constant::VALUE_ALL, constant::VALUE_ALL, constant::VALUE_ALL).await;

        for val in pos_stocks.iter() {
            if let Ok(mkt) = self.aka_svc.query_market_info(val.exchange_id).await {
                let key = format!("stock.{}.{}_{}", mkt.market_type, val.stock_code, mkt.market_code);
                // info!("get_stock_positions_quotation_key,{}", key);
                qkeys.entry(key).and_modify(|v| *v += 1).or_insert(1);
            }
        }
        // info!("get_stock_positions_quotation_key,{:?}", qkeys);
        qkeys
    }

    pub async fn handle_assets_by_dealinfo(&self, order: &NotificationOrderExec) -> Result<()> {
        if self.ignore_accounts.iter().any(|&x| x == order.unit_id) {
            info!("当前账号:{},属于忽略账号,不处理", order.unit_id);
            return Err(anyhow!("当前账号:{},属于忽略账号,不处理", order.unit_id));
        }

        if order.order_id <= 0 {
            return Err(anyhow!("order_id error:{:?}", &order.order_id));
        }

        let order_flag = format!("{}_{}", order.order_id, order.channel_id);
        if order.exec_type() == OrderExecType::NewOrder {
            if self.redis_client.get_key_is_exists(&order_flag).await {
                return Ok(());
            } else {
                if let Err(e) = self.redis_client.set_str_value(&order_flag, 60 * 60 * 8, "挂单").await {
                    error!("{}", e);
                }
            }
        } else if order.exec_type() == OrderExecType::OrderCancelled {
            if !self.redis_client.get_key_is_exists(&order_flag).await {
                if let Err(e) = self.redis_client.set_str_value(&order_flag, 60 * 60 * 8, "挂单").await {
                    error!("{}", e);
                }
                return Ok(());
            }
        }

        //股票信息
        let ret = self.aka_svc.query_stock_info(order.stock_id).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("queyr stock info error:{}", ret.as_ref().err().unwrap().to_string()));
        }
        let ret = ret.unwrap();
        if let None = ret {
            info!("query_stock_info 找不到信息,{:?}", order.stock_id);
            return Err(anyhow!(format!("query_stock_info 找不到信息,{:?}", order.stock_id)));
        }
        let stockinfo = ret.unwrap();
        info!("股票信息:{:?}", &stockinfo);
        // let stocktype: i32 = stockinfo.stock_type;
        //汇率信息
        let base_currency = self.user_assets_svc.query_currency_by_unit_id(order.unit_id).await.ok_or(anyhow!("账户内存不存在 unit_id: {}", order.unit_id))?;
        let ret = self.aka_svc.query_exchange_rate(stockinfo.trade_currency().as_str_name(), &base_currency).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("queyr stock info error:{}", ret.as_ref().err().unwrap().to_string()));
        }
        let rateinfo = ret.unwrap();
        info!("汇率信息:{:?}", &rateinfo);

        //通道信息
        let ret = self.aka_svc.query_channel_info(order.channel_id as i64).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("queyr channel info error:{}", ret.as_ref().err().unwrap().to_string()));
        }
        let channelinfo = ret.unwrap();
        info!("channel info:{:?}", &channelinfo);

        //产品通道保证金比率
        let mut leverage = Decimal::from_f64(0.25).unwrap_or_default();
        let ret = self.aka_svc.query_stock_channel_margin(order.stock_id, order.channel_id as i64).await;
        if ret.as_ref().is_ok() {
            leverage = Decimal::from_f64(ret.unwrap()).unwrap_or_default();
        } else {
            error!("queyr stock channel leverage info error:{},采用默认保证金比率", ret.as_ref().err().unwrap().to_string());
        }

        info!("产品ID:{},通道id:{}.保证金比率：{}", order.stock_id, order.channel_id, leverage);

        //参考汇率用方向
        let currency_rate: Decimal = Decimal::from_f64(rateinfo.sell_rate).unwrap_or_default();
        //成交汇率根据实际交易方向
        let mut currency_rate_cj: Decimal = Decimal::from_f64(rateinfo.buy_rate).unwrap_or_default();
        if order.order_direction == constant::OrderDirection::SELL as i32 {
            currency_rate_cj = Decimal::from_f64(rateinfo.sell_rate).unwrap_or_default();
        }

        let mut dealdetail = CommonService::convert_dealinfo_to_dealdetail(&order, leverage, currency_rate, currency_rate_cj, &stockinfo, &channelinfo, self.sys_info.read().await.init_date);
        //计算费用
        if order.exec_type() == OrderExecType::OrderFill {
            let ret = FeeDetail::calc_fee_info_v2(
                &FeeField {
                    fee_type: "".to_string(),
                    exchange_id: stockinfo.market_id,
                    unit_id: order.unit_id,
                    user_id: order.user_id,
                    channel_id: channelinfo.channel_id,
                    stock_type: stockinfo.stock_type,
                    amount: order.order_quantity,
                    price: Decimal::from_f64(order.order_price).unwrap_or_default(),
                    currency_no: stockinfo.trade_currency().as_str_name().to_string(),
                    order_direction: order.order_direction,
                    rate: currency_rate_cj,
                },
                &self.aka_svc,
            )
            .await;
            if ret.as_ref().is_err() {
                error!("计算费用错误:{}", ret.as_ref().unwrap_err().to_string());
                return Err(anyhow!("计算费用错误:{}", ret.as_ref().err().unwrap().to_string()));
            }
            let fee_info = ret.unwrap();
            dealdetail.p_fee_total = fee_info.fee_total;
        }
        let sys_info = self.sys_info.read().await;
        info!("Deal Detail:{:#?}", &dealdetail);
        let ret = self.account_position_svc.update_account_positions_by_dealinfo_v2(&order, &mut dealdetail, &channelinfo, sys_info.init_date).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("根据成交处理持仓错误:{}", ret.as_ref().err().unwrap().to_string()));
        }
        let (account_position, is_new) = ret.unwrap();
        info!("根据成交处理持仓结果：{:?} {}", &account_position, is_new);
        if is_new {
            //新持仓，需要写入历史信息表
            let his_account_position = CommonService::convert_stockpositions_to_stockpositionhis(&account_position, sys_info.preinit_date);
            info!("需要写入的分帐户持仓信息(历史记录表):{:#?}", his_account_position);
            let his_account_positions = vec![his_account_position];
            let ret = self.tx_persist.send(PersistData::AccountPositionHis(Box::new(his_account_positions))).await;
            if ret.as_ref().is_err() {
                error!("push error:{}", ret.as_ref().err().unwrap().to_string());
            }
        }
        // let account_positions = vec![account_position];
        // let ret = self.tx_persist.send(PersistData::AccountPosition(Box::new(account_positions))).await;
        // if ret.as_ref().is_err() {
        //   error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        // }

        //处理分帐户得资产信息
        // if order.exec_type() == OrderExecType::OrderFill {
        // //处理分帐户资产信息
        let account_positions = self.account_position_svc.get_account_stock_positions(constant::VALUE_ALL).await;
        let aa_ret = self
            .account_assets_svc
            .update_account_assets_by_dealinfo(order.exec_type() == OrderExecType::OrderFill, &mut dealdetail, &account_positions, sys_info.init_date)
            .await;
        if aa_ret.is_err() {
            error!("update account assets error");
            return Err(anyhow!("update account assets error"));
        }
        let (account_assets, is_new) = aa_ret.unwrap();
        info!("根据成交和持仓处理分帐户资产结果：{:?}", &account_assets);
        if is_new {
            //新持仓，需要写入历史信息表
            let his_account_assets = CommonService::convert_accountassets_to_accountassetshis(&account_assets, sys_info.preinit_date);
            info!("需要写入的分帐户资产信息(历史记录表):{:#?}", his_account_assets);
            let his_account_assesses = vec![his_account_assets];
            let ret = self.tx_persist.send(PersistData::AccountAssetsHis(Box::new(his_account_assesses))).await;
            if ret.as_ref().is_err() {
                error!("push error:{}", ret.as_ref().err().unwrap().to_string());
            }
        }
        // let account_assesses = vec![account_assets];
        // let ret = self.tx_persist.send(PersistData::AccountAssets(Box::new(account_assesses))).await;
        // if ret.as_ref().is_err() {
        //   error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        // }
        // }

        if order.exec_type() == OrderExecType::OrderFill {
            let ret = PhoenixDealDetail::insert(&dealdetail, &self.dbconn).await;
            if ret.as_ref().is_err() {
                return Err(anyhow!("真实成交信息，写入数据库错误:{}", ret.as_ref().err().unwrap().to_string()));
            }
        }

        Ok(())
    }

    pub async fn update_stock_positions_lastprice_by_quotation(&self, stockid: i64, lastprice: f64) -> Result<()> {
        //更新用户持仓的最新价

        self.user_position_svc.update_positions_last_price(lastprice, stockid).await;

        //更新分帐户持仓的最新价
        let price = Decimal::from_f64_retain(lastprice).unwrap_or_default();
        self.account_position_svc.update_account_positions_last_price(&price, stockid).await?;

        Ok(())
    }

    pub async fn query_account_assets(&self, req: &PhoenixAccountQueryRequest) -> Result<Vec<PhoenixAccountAssetsInfo>> {
        let mut ret: Vec<PhoenixAccountAssetsInfo> = Vec::new();

        let account_assets = self.account_assets_svc.get_account_assets(req.account_id).await;

        for val in account_assets {
            let ret_info = CommonService::convert_accountassets_to_assetsinfo(&val);
            ret.push(ret_info);
        }

        Ok(ret)
    }

    pub async fn update_assets_by_position(&self) -> Result<()> {
        //更新品种在通道的保证金比率

        //更新用户账户得资产
        _ = self.re_calculate_user_assets(0, 0, false).await;

        let init_date = self.sys_info.read().await.init_date;
        //获取所有得持仓信息
        let account_positions = self.account_position_svc.get_account_stock_positions(constant::VALUE_ALL).await;
        self.account_assets_svc.summarize_account_assets_from_account_positions(constant::VALUE_ALL, &account_positions, init_date).await;
        //更新总账户数据
        let total_account = self.setting.read().await.system.account_total.parse::<i64>().unwrap_or_default();
        let total_account = self.user_assets_svc.get_user_total_assets(total_account).await;
        let _ = self.account_assets_svc.update_total_account_assets(&total_account).await;
        Ok(())
    }

    //把所有的分帐户持仓数据，用最新价替换均价
    //把对应分帐户的浮动盈亏加到实际盈亏
    //重新计算分帐户的资产数据
    #[tracing::instrument(name = "reset分帐户数据", skip_all)]
    pub async fn reset_profit(&self, accountid: i64, req: &PhoenixAccountResetRequest) -> Result<()> {
        info!("开始处理reset......");
        //1)获取交收汇率
        //2)重新计算当前的浮动盈亏,并把浮动盈亏增加到实际盈亏(浮动盈亏=0，实际盈亏+=浮动盈亏)
        //3)更新分帐户持仓的均价和持仓成本(用最新价代替均价,然后用均价*数量代替total_value)
        //4)保存reset历史记录到数据库
        // let currency_rate = self.redis_ctl.get_rate_by_key("RATE_CNY_HKD_JS").await;
        // info!("当前的交收汇率:{}", currency_rate);

        // info!("start to reset account:{}", accountid);
        // let mut account_position_svc = self.account_position_ctl.write().await;
        // let mut account_asset_svc = self.account_assets_ctl.write().await;
        // let mut tradedate_svc = self.tradedate_ctl.write().await;

        //before reset, re-summarize all assets
        // let account_positions = account_position_svc.get_account_stock_positions(accountid).await;
        // info!("account positions before reset:{:#?}", &account_positions);
        // if let Ok(()) = account_asset_svc
        //     .summarize_account_assets_from_account_positions(accountid, &account_positions, &tradedate_svc)
        //     .await
        // {
        //reset account positions
        let new_financial_borrowed = self.account_position_svc.reset_profit(accountid).await;
        info!("分帐户资产reset后得已借:{}", new_financial_borrowed);
        //reset account assets
        let assetprofit = self.account_assets_svc.reset_profit(accountid, new_financial_borrowed).await;
        info!("持仓reset后得结果:{:?}", assetprofit);
        //generate reset detail
        let reset_detail = PhoenixAccountResetDetail {
            id: 0,
            p_account_unit_id: accountid,
            p_float_profit_before: assetprofit.1,
            p_current_profit_before: assetprofit.0,
            p_float_profit_after: Decimal::from(0),
            p_current_profit_after: assetprofit.1 + assetprofit.0,
            p_current_principal: assetprofit.2,
            p_account_no: req.operator_id,
            p_datetime: current_timestamp(),
            p_remark: "".to_string(),
        };
        // reset_detail.id = 0;
        // reset_detail.p_account_unit_id = accountid;
        // reset_detail.p_current_profit_before = assetprofit.0;
        // reset_detail.p_float_profit_before = assetprofit.1;
        // reset_detail.p_float_profit_after = Decimal::from(0);
        // reset_detail.p_current_profit_after = assetprofit.1 + assetprofit.0;
        // reset_detail.p_current_principal = assetprofit.2;
        // reset_detail.p_datetime = current_timestamp() as u64;
        // reset_detail.p_account_no = req.operator_id;

        //send reset detail to persist command
        info!("reset detail is:{:#?}", &reset_detail);
        let ret = self.tx_persist.send(PersistData::ResetDetail(Box::new(reset_detail))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        let sys_info = self.sys_info.read().await;
        let prev_date = sys_info.preinit_date;
        //persist to database: account assets, account positions
        let account_assets = self.account_assets_svc.get_account_assets(accountid).await;
        let mut account_assets_his: Vec<PhoenixAccountAssetsHis> = account_assets.iter().map(|x| CommonService::convert_accountassets_to_accountassetshis(x, sys_info.init_date)).collect();
        account_assets_his.iter_mut().for_each(|model| {
            model.p_date = prev_date;
        });
        // info!("开始保存资产数据......");
        let ret = self.tx_persist.send(PersistData::AccountAssets(Box::new(account_assets))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        let ret = self.tx_persist.send(PersistData::AccountAssetsHis(Box::new(account_assets_his))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        //send account position to persist command
        let account_positions = self.account_position_svc.get_account_stock_positions(accountid).await;
        let mut current_his_account_positions: Vec<PhoenixStockPositionChannelHis> = account_positions.iter().map(|x| CommonService::convert_stockpositions_to_stockpositionhis(x, sys_info.init_date)).collect();
        current_his_account_positions.iter_mut().for_each(|model| {
            model.p_date = prev_date;
        });
        // info!("开始保存持仓数据......");
        let ret = self.tx_persist.send(PersistData::AccountPosition(Box::new(account_positions))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        let ret = self.tx_persist.send(PersistData::AccountPositionHis(Box::new(current_his_account_positions))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        Ok(())
    }

    pub async fn persist_data_interval(&self) -> Result<()> {
        //定时保存数据，主要需要保存的数据包括：1)用户资产数据;2)分帐户资产数据;3)分帐户持仓数据;
        // let user_assets = user_assets_svc.get_user_assets(constant::VALUE_ALL);
        // let _ = self.tx_persist.send(PersistData::UserAssets(Box::new(user_assets))).await;
        let account_assets = self.account_assets_svc.get_account_assets(constant::VALUE_ALL).await;
        let ret = self.tx_persist.send(PersistData::AccountAssets(Box::new(account_assets))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        let account_positions = self.account_position_svc.get_account_stock_positions(constant::VALUE_ALL).await;
        let ret = self.tx_persist.send(PersistData::AccountPosition(Box::new(account_positions))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        Ok(())
    }

    async fn handle_current_deal_detail(&self, prev_date: i64, currency_rate_buy: Decimal, currency_rate_sell: Decimal) -> Result<Vec<PhoenixDealDetail>> {
        let mut deal_list: Vec<PhoenixDealDetail> = result!(PhoenixDealDetail::query_many(prev_date, &self.dbconn).await, "query deal details error");
        info!("根据日期{}从交易记录查找交易信息，共:{}交易信息", prev_date, deal_list.len());
        for val in deal_list.iter_mut() {
            if val.p_exchange_id as i64 == constant::EXCHANGE_HK {
                val.p_currency_rate_cj = Decimal::from_i32(1).unwrap();
                val.p_currency_rate = Decimal::from_i32(1).unwrap();
            } else {
                // val.p_currency_rate_cj = currency_rate_sell;
                //汇率问题，买卖方向？？？？
                if val.p_order_direction == OrderDirection::BUY as i32 {
                    val.p_currency_rate_cj = currency_rate_buy;
                    val.p_currency_rate = currency_rate_sell;
                } else {
                    val.p_currency_rate_cj = currency_rate_sell;
                    val.p_currency_rate = currency_rate_sell;
                }
            }
            val.p_prebuy_amount = 0;
        }

        Ok(deal_list)
    }

    async fn insert_current_deal_detail_to_his(&self, deal_list: &Vec<PhoenixDealDetail>, prev_date: i32) -> Result<()> {
        let deal_his: Vec<PhoenixDealDetailHis> = deal_list.iter().map(|val| CommonService::convert_dealdetail_to_dealdetailhis(val)).collect();
        //insert into table: phoenix_deal_detail_his
        result!(PhoenixDealDetailHis::insert_many(&deal_his, &self.dbconn).await, "insert many error");

        //delete from phoenix_deal_detail by date;
        result!(PhoenixDealDetail::delete_many(prev_date as i64, &self.dbconn).await, "delete deal detail error");

        Ok(())
    }

    //结算
    #[tracing::instrument(name = "分帐户结算", skip_all)]
    pub async fn resettle_assets(&self) -> Result<()> {
        //获取结算汇率,注意,要从redis获取？？？Key:RATE_CNY_HKD_JS
        let rate = result!(self.aka_svc.query_exchange_rate("CNY", "HKD").await, "query exchange rate error......");
        let currency_rate_buy = Decimal::from_f64(rate.buy_rate).unwrap_or_default();
        let currency_rate_sell = Decimal::from_f64(rate.sell_rate).unwrap_or_default();
        info!("当前买汇率：{}， 卖汇率：{}", currency_rate_buy, currency_rate_sell);
        // //获取日期信息
        // let sys_info: PhoenixSysSystem = result!(PhoenixSysSystem::query(&self.dbconn).await, "query exchange rate error......");
        let sys_info = self.sys_info.read().await;
        let cur_date = sys_info.init_date;
        let prev_date = sys_info.preinit_date;
        let last_date = sys_info.before_preinit_date;
        info!("结算后的当前交易日期:{}, 结算前的交易日期：{},结算前交易日期的前一个交易日期:{}", cur_date, prev_date, last_date);
        //1) 存档用户资产和持仓

        // -----------------------------计算分帐户资产信息 --------------------------------------
        //保存用户持仓信息到历史用户资产信息表
        // let mut account_assets_svc = self.account_assets_ctl.write().await;
        // let mut account_position_svc = self.account_position_ctl.write().await;
        //2)根据订单,重算分帐户的资产和持仓
        // // info!("当前分帐户资产信息:{:#?}", account_assets_svc.get_account_assets(constant::VALUE_ALL));
        // // info!("历史分帐户资产信息:{:#?}", &his_account_assets);
        result!(self.account_assets_svc.update_account_assets_from_his(last_date, &self.dbconn).await, "update account assets from his error");
        //处理前一天的权益分派
        self.account_assets_svc.update_account_assets_by_dividend_record_his(&self.dbconn, prev_date).await?;
        // // info!("用历史分帐户资产信息更新后的结果:{:#?}", account_assets_svc.get_account_assets(constant::VALUE_ALL));
        //2.2 从订单信息表中获取当天的订单
        let mut deal_list = self.handle_current_deal_detail(prev_date as i64, currency_rate_buy, currency_rate_sell).await?;

        //2.3 根据订单重算分帐户的资产信息和持仓信息(计算实际盈亏的时候用交收汇率,计算浮动盈亏,持仓市值时仍然用参考汇率)
        //根据订单，重新计算分帐户持仓信息和分帐户资产信息
        //如果历史分帐户持仓信息中找不到持仓记录，则用分配一条默认值
        // // info!("当前的分帐户持仓信息:{:#?}", &account_position_svc.get_account_stock_positions(constant::VALUE_ALL));
        self.account_position_svc.update_account_stock_position_from_his(&self.dbconn, last_date, currency_rate_sell, cur_date).await?;
        //处理前一天权益分派记录
        self.account_position_svc.update_account_positions_by_dividend_record_his(&self.dbconn, prev_date, currency_rate_sell).await?;
        // info!("用历史分帐户持仓信息更新后的持仓信息:{:?}", &self.account_position_svc.get_account_stock_positions(constant::VALUE_ALL));
        info!("开始根据交易记录重算用户资产信息，使用的交收汇率:{}", currency_rate_buy);

        let mut deal_infos = LinkedHashMap::<String, PhoenixDealDetail>::new();

        for deal in deal_list.iter_mut() {
            // let account_stock_pos_his = his_account_position.iter().find(|&x| x.p_stock_id == deal.p_stock_id && x.p_channel_id == deal.p_channel_id);
            info!("交易信息:{:?}", &deal);
            if deal.p_channel_type != constant::ChannelType::EXTERNAL as i32 {
                info!("订单是内盘交易，不计算分帐户数据......{:?}", deal);
                continue; //忽略所有的内盘交易
            }

            deal_infos
                .entry(format!("{}_{}", deal.p_order_no, deal.p_channel_id))
                .and_modify(|info| {
                    let total_price = info.p_deal_price * Decimal::from(info.p_deal_amount) + deal.p_deal_price * Decimal::from(deal.p_deal_amount);
                    info.p_deal_amount += deal.p_deal_amount;
                    info.p_deal_price = total_price / Decimal::from(info.p_deal_amount);
                })
                .or_insert(deal.to_owned());
        }

        for (_, deal) in deal_infos.iter_mut() {
            let positions = match self.account_position_svc.update_account_stock_positions_by_dealinfo(deal, cur_date, &self.dbconn).await {
                Ok(value) => value,
                Err(_) => {
                    error!("update account stock positions error with dealinfo:{:?}", deal);
                    continue;
                }
            };
            //更新预买量
            //结算时可以不需要处理预买量
            // let ret = account_position_svc.update_account_positions_prebuy_amount(&deal, &tradedate_svc, &self.dbconn).await;
            // if ret.as_ref().is_err() {
            //     error!("更新预买量错误......");
            // }

            let ret = self.aka_svc.query_stock_info(deal.p_stock_id as i64).await;
            if ret.as_ref().is_err() {
                return Err(anyhow!("queyr stock info error:{}", ret.as_ref().err().unwrap().to_string()));
            }
            let ret = ret.unwrap();
            if let None = ret {
                info!("query_stock_info 找不到信息,{:?}", deal.p_stock_id);
                return Err(anyhow!(format!("query_stock_info 找不到信息,{:?}", deal.p_stock_id)));
            }
            let stockinfo = ret.unwrap();

            let ret = FeeDetail::calc_fee_info_v2(
                &FeeField {
                    fee_type: "".to_string(),
                    exchange_id: stockinfo.market_id,
                    unit_id: deal.p_unit_id,
                    user_id: deal.user_id,
                    channel_id: deal.p_channel_id as i64,
                    stock_type: stockinfo.stock_type,
                    amount: deal.p_deal_amount,
                    price: deal.p_deal_price,
                    currency_no: stockinfo.trade_currency().as_str_name().to_string(),
                    order_direction: deal.p_order_direction,
                    rate: if deal.p_order_direction == OrderDirection::SELL as i32 { currency_rate_sell } else { currency_rate_buy },
                },
                &self.aka_svc,
            )
            .await;
            if ret.as_ref().is_err() {
                error!("计算费用错误:{}", ret.as_ref().unwrap_err().to_string());
                log_error(&format!("计算费用错误:{}", ret.as_ref().unwrap_err().to_string())).await;
                return Err(anyhow!("计算费用错误:{}", ret.as_ref().err().unwrap().to_string()));
            }
            let fee_info = ret.unwrap();
            deal.p_fee_total = fee_info.fee_total * deal.p_currency_rate_cj;

            if let Err(e) = self.account_assets_svc.update_account_assets_by_dealinfo(true, deal, &positions, cur_date).await {
                log_error(&format!("根据交易更新总账户资产错误:{:?}", &e)).await;
                error!("update account assets error, dealinfo:{:?}", deal);
                continue;
            }
        }

        //处理前一天划转记录
        let trans_details = PhoenixTransDetail::query_many(constant::VALUE_ALL, prev_date as u32, &self.dbconn).await?;

        for trans_detail in trans_details {
            if let Err(e) = self.account_assets_svc.transfer_fund(&trans_detail).await {
                error!("trans fund error:{}", &e);
            }
        }

        // // info!("根据交易更新后的分帐户持仓数据:{:#?}", account_position_svc.get_account_stock_positions(constant::VALUE_ALL));
        //计算费用数据
        // let ret = PhoenixOrdSettledetail::query_many(cur_date, &self.dbconn).await;
        // if ret.as_ref().is_err() {
        //   error!("query pendsettle error:{:?}", &ret);
        //   return Err(anyhow!("query pendsettle error:{:?}", &ret));
        // }
        // let total_settles = ret.unwrap();
        // let pend_settles: Vec<PhoenixOrdSettledetail> = total_settles.iter().filter(|&s| self.ignore_fee_accounts.iter().find(|&&x| x == s.unit_id).is_none()).map(|x| x.clone()).collect();
        // //更新分帐户持仓的费用数据
        // let ret = self.account_position_svc.update_fee_total(&currency_rate_buy, &currency_rate_sell, &pend_settles).await;
        // if ret.as_ref().is_err() {
        //   error!("update fee total error:{:?}", &ret);
        //   return Err(anyhow!("error:{:?}", &ret));
        // }
        // let channel_data = self.channel_ctl.get_cached_channels_clone().await;
        // account_assets_svc.update_fee_total(&currency_rate_buy, &currency_rate_sell, &pend_settles, &channel_data, &his_account_assets);
        // // info!("计算费用后的分帐户持仓数据:{:#?}", account_position_svc.get_account_stock_positions(constant::VALUE_ALL));

        // //分账户持仓信息
        let mut account_positions = self.account_position_svc.get_account_stock_positions(constant::VALUE_ALL).await;
        info!("更新最新价后的分帐户持仓数据:{:#?}", &account_positions);
        let current_his_account_positions: Vec<PhoenixStockPositionChannelHis> = account_positions.iter().map(|x| CommonService::convert_stockpositions_to_stockpositionhis(x, prev_date)).collect();
        info!("转换后的历史分帐户持仓数据:{:#?}", &current_his_account_positions);
        account_positions.iter_mut().for_each(|val| {
            val.p_date = cur_date; //更新结算时保存的日期
            val.p_frozen_amount = 0;
            val.p_frozen_amount_temp = 0;
            if val.p_exchange_id as i64 == constant::EXCHANGE_HK {
                //更新汇率
                val.p_currency_rate = Decimal::from(1);
            } else {
                val.p_currency_rate = currency_rate_sell;
            }
            // val.p_currency_rate = currency_rate; //更新汇率
            val.p_prebuy_amount = 0;
        });
        // info!("更新后的历史分帐户持仓数据:{:#?}", &current_his_account_positions);
        //需要重新计算汇总分帐户资产信息
        self.account_assets_svc.summarize_account_assets_from_account_positions(constant::VALUE_ALL, &account_positions, cur_date).await;
        let total_account = self.setting.read().await.system.account_total.parse::<i64>().unwrap_or_default();
        let total_assets = self.user_assets_svc.get_user_total_assets(total_account).await;
        self.account_assets_svc.update_total_account_assets(&total_assets).await;
        let mut account_assets = self.account_assets_svc.get_account_assets(constant::VALUE_ALL).await;
        account_assets.iter_mut().for_each(|val| {
            val.p_date = cur_date;
        });
        let account_assets_his: Vec<PhoenixAccountAssetsHis> = account_assets.iter().map(|x| CommonService::convert_accountassets_to_accountassetshis(x, prev_date)).collect();

        info!("send分帐户持仓数据:{:#?}", &account_positions);
        info!("send分帐户历史持仓数据:{:#?}", &current_his_account_positions);
        info!("send分帐户资产数据:{:#?}", &account_assets);
        info!("send分帐户历史资产数据:{:#?}", &account_assets_his);

        let ret = self.tx_persist.send(PersistData::AccountAssetsHis(Box::new(account_assets_his))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        let ret = self.tx_persist.send(PersistData::AccountAssets(Box::new(account_assets))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        let ret = self.tx_persist.send(PersistData::AccountPositionHis(Box::new(current_his_account_positions))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        // info!("保存分帐户当前持仓信息，详细数据:{:#?}", &account_positions);
        let _ = self.tx_persist.send(PersistData::AccountPosition(Box::new(account_positions))).await;
        //需要归档交易记录 phoenix_deal_detail_his
        // info!("开始归档交易详情......");
        self.insert_current_deal_detail_to_his(&deal_infos.iter().map(|(_, b)| b.to_owned()).collect_vec(), prev_date).await?;

        // // -----------------------------------
        // //用户资产信息
        // // info!("开始重新归档用户资产信息......");
        // let mut user_assets_info = user_assets_svc.get_user_assets(constant::VALUE_ALL);
        // for val in user_assets_info.iter_mut() {
        //   val.p_date = tradedate_svc.cur_date;
        // }
        // let user_assets_his: Vec<PhoenixUserAssetsHis> = user_assets_info.iter().map(|x| PhoenixUserAssetsHis::convert_from_userassets(x, prev_date)).collect();
        // let _ = self.tx_persist.send(PersistData::UserAssets(Box::new(user_assets_info))).await;
        // let _ = self.tx_persist.send(PersistData::UserAssetsHis(Box::new(user_assets_his))).await;
        // ------------------------------------------------------------------
        Ok(())
    }

    //用于数据重算,不需要迁移
    #[tracing::instrument(name = "根据交易重算资产", skip_all)]
    pub async fn compute_assets_from_deal(&self, account_id: i64, curdate: i32, beforedate: i32, currency_rate_buy: Decimal, currency_rate_sell: Decimal) -> Result<()> {
        info!("开始重算{} {}的数据，前一个交易日:{}, 买汇率：{},卖汇率:{}", curdate, account_id, beforedate, currency_rate_buy, currency_rate_sell);

        let sys_info = self.sys_info.read().await;
        let prev_date = sys_info.preinit_date;
        let last_date = sys_info.before_preinit_date;

        //开始重算user_assets_his表
        let ret = recalculate::recalculate_user_assets(curdate, beforedate, currency_rate_sell, currency_rate_buy, &self.dbconn).await;
        info!("recalculate::recalculate_user_assets:{:?}", ret);

        let mut deals = PhoenixOrdStockdealHis::find_by_date(&self.dbconn, curdate).await?;
        info!("{}的交易明细数:{}", curdate, deals.len());

        deals.sort_by(|a, b| a.did.cmp(&b.did));

        let mut deal_list = LinkedHashMap::<String, PhoenixDealDetailHis>::new();
        let mut stock_currency = HashMap::new();
        for new_dealinfo in deals.iter() {
            if new_dealinfo.deal_amount <= 0 {
                info!("交易数量<=0,不处理");
                continue;
            }
            // let order_direction = new_dealinfo.c_order_direction.parse::<i32>().unwrap_or_default();
            // let common_data = self
            //     .get_common_data(new_dealinfo.l_exchange_id, new_dealinfo.l_stock_id, new_dealinfo.l_channel_id, order_direction)
            //     .await;
            let cmdrate = self.aka_svc.query_stock_channel_margin(new_dealinfo.stock_id, new_dealinfo.channel_id as i64).await?;
            let chninfo = self.aka_svc.query_channel_info(new_dealinfo.channel_id as i64).await?;
            if account_id != 0 && chninfo.account_id != account_id {
                continue;
            }
            // info!("通道id:{} 对应的通道信息:{:?}", &channelid, &chninfo);
            let (stock_type, trade_currency) = match self.aka_svc.query_stock_info(new_dealinfo.stock_id).await {
                Ok(stock_info) => {
                    if let None = stock_info {
                        (1, Currency::Cny.as_str_name().to_string())
                    } else {
                        let stock_info = stock_info.unwrap();
                        (stock_info.stock_type, stock_info.trade_currency().as_str_name().to_string())
                    }
                }
                Err(_) => (1, Currency::Cny.as_str_name().to_string()),
            };
            let deal = PhoenixDealDetailHis::convert_from_dealinfo_his(
                new_dealinfo,
                Decimal::from_f64(cmdrate).unwrap_or_default(),
                chninfo.account_id as i32,
                currency_rate_sell,
                currency_rate_sell,
                stock_type,
                chninfo.qfii_state,
            );

            if deal.p_channel_type != constant::ChannelType::EXTERNAL as i32 {
                info!("订单是内盘交易，不计算分帐户数据......{:?}", deal);
                continue; //忽略所有的内盘交易
            }
            deal_list
                .entry(format!("{}_{}", deal.p_order_no, deal.p_channel_id))
                .and_modify(|info| {
                    let total_price = info.p_deal_price * Decimal::from(info.p_deal_amount) + deal.p_deal_price * Decimal::from(deal.p_deal_amount);
                    info.p_deal_amount += deal.p_deal_amount;
                    info.p_deal_price = total_price / Decimal::from(info.p_deal_amount);
                })
                .or_insert(deal.clone());

            stock_currency.entry(new_dealinfo.stock_id).or_insert(trade_currency);
        }

        let mut account_positions = PhoenixStockPositionChannelHis::query_many(account_id, beforedate as i64, &self.dbconn).await?;
        info!("日期:{}得持仓数据数：{:?}", beforedate, account_positions.len());
        let mut account_assets = PhoenixAccountAssetsHis::query_many(account_id, beforedate, &self.dbconn).await?;
        info!("日期:{}得资产数据数：{:?}", beforedate, account_assets.len());

        // 处理前一天权益分派记录
        // dvd_register(&self.dbconn, &self.aka_svc, self.uidsvc.clone(), beforedate, curdate, &mut account_assets, &mut account_positions).await?;
        update_account_assets_by_dividend_record_his(&self.dbconn, account_id, curdate, &mut account_assets, &mut account_positions).await?;

        info!("开始根据交易记录重算用户资产信息，使用的交收汇率:{}", currency_rate_buy);
        for (_, deal) in deal_list.iter_mut() {
            // let account_stock_pos_his = his_account_position.iter().find(|&x| x.p_stock_id == deal.p_stock_id && x.p_channel_id == deal.p_channel_id);
            info!("交易信息:{:?}", deal);

            if deal.p_exchange_id == constant::EXCHANGE_HK as i32 {
                deal.p_currency_rate_cj = Decimal::from_i32(1).unwrap_or_default();
            } else {
                if deal.p_order_direction == constant::OrderDirection::BUY as i32 {
                    deal.p_currency_rate_cj = currency_rate_buy;
                } else {
                    deal.p_currency_rate_cj = currency_rate_sell;
                }
            }

            //更新持仓信息
            if let Err(_) = PhoenixAccountStockPositionService::compute_his_account_positions(&mut account_positions, deal, curdate as i32).await {
                error!("update account stock positions error with dealinfo:{:?}", deal);
                continue;
            };

            let fee_total = if curdate < last_date || (curdate < prev_date && deal.p_exchange_id != constant::EXCHANGE_HK as i32) {
                PhoenixOrdSettledetailHis::query_fee(deal.p_order_no, deal.p_channel_id, &self.dbconn).await?
            } else if (curdate == last_date && deal.p_exchange_id == constant::EXCHANGE_HK as i32) || (curdate == prev_date && deal.p_exchange_id != constant::EXCHANGE_HK as i32) {
                PhoenixOrdSettledetail::query_fee(deal.p_order_no, deal.p_channel_id, &self.dbconn).await?
            } else {
                let ret = FeeDetail::calc_fee_info_v2(
                    &FeeField {
                        fee_type: "".to_string(),
                        exchange_id: deal.p_exchange_id as i64,
                        unit_id: deal.p_unit_id,
                        user_id: deal.user_id,
                        channel_id: deal.p_channel_id as i64,
                        stock_type: deal.p_stock_type,
                        amount: deal.p_deal_amount,
                        price: deal.p_deal_price,
                        currency_no: stock_currency.get(&(deal.p_stock_id as i64)).unwrap_or(&"CNY".to_string()).to_owned(),
                        order_direction: deal.p_order_direction,
                        rate: if deal.p_order_direction == OrderDirection::SELL as i32 { currency_rate_sell } else { currency_rate_buy },
                    },
                    &self.aka_svc,
                )
                .await;
                if ret.as_ref().is_err() {
                    error!("计算费用错误:{}", ret.as_ref().unwrap_err().to_string());
                    return Err(anyhow!("计算费用错误:{}", ret.as_ref().err().unwrap().to_string()));
                }
                ret.unwrap().fee_total
            };

            deal.p_fee_total = fee_total * deal.p_currency_rate_cj;

            //更新持仓信息
            if let Err(_) = PhoenixAccountAssetsService::compute_his_account_assets(&mut account_assets, &account_positions, deal, curdate as i32).await {
                error!("update account assets error, dealinfo:{:?}", deal);
            };
        }

        //处理前一天划转记录
        let trans_details = PhoenixTransDetail::query_many(account_id, curdate as u32, &self.dbconn).await?;

        if account_id == 0 {
            for trans_detail in trans_details {
                info!("start to transfer fund: {:#?}", &trans_detail);

                let target_account = account_assets.iter().position(|asset| asset.p_account_id == trans_detail.p_account_target);

                if target_account.is_none() {
                    return Err(anyhow!(format!("target account doesn't exist: {}", trans_detail.p_account_target)));
                }
                let target_id = target_account.unwrap();

                let source_account = account_assets.iter().position(|asset| asset.p_account_id == trans_detail.p_account_source);

                if source_account.is_some() && account_assets[source_account.unwrap()].p_account_type == account_assets[target_id].p_account_type {
                    return Err(anyhow!(format!("source account is exist: {}", trans_detail.p_account_source)));
                }

                let mut trans_value = trans_detail.p_trans_value;
                if trans_detail.p_op_flag == constant::TransDirection::DEC as i32 {
                    trans_value = -trans_value;
                }
                info!("current trans value is: {}", trans_value);

                account_assets[target_id].p_current_principal += trans_value;
                if source_account.is_some() {
                    let source_id = source_account.unwrap();
                    account_assets[source_id].p_current_principal -= trans_value;
                }
            }
        } else {
            for trans_detail in trans_details {
                info!("start to transfer fund: {:#?}", &trans_detail);

                let mut trans_value = trans_detail.p_trans_value;
                if trans_detail.p_op_flag == constant::TransDirection::DEC as i32 {
                    trans_value = -trans_value;
                }
                info!("current trans value is: {}", trans_value);

                let target_account = account_assets.iter().position(|asset| asset.p_account_id == trans_detail.p_account_target);

                if let Some(target_id) = target_account {
                    account_assets[target_id].p_current_principal += trans_value;
                }

                let source_account = account_assets.iter().position(|asset| asset.p_account_id == trans_detail.p_account_source);

                if let Some(source_id) = source_account {
                    account_assets[source_id].p_current_principal -= trans_value;
                }
            }
        }

        let user_assets = PhoenixUserAssetsHis::query_many(curdate, &self.dbconn).await?;
        let user_positions = PhoenixAstStockpositionHis::query_many(curdate, &self.dbconn).await?;

        let mut last_price_map = HashMap::new();
        user_positions.iter().for_each(|pos| {
            if !last_price_map.contains_key(&pos.stock_id) {
                last_price_map.insert(pos.stock_id, pos.last_price);
            }
        });

        account_positions.iter_mut().for_each(|val| {
            val.id = 0;
            val.p_date = curdate; //更新结算时保存的日期
            val.p_frozen_amount = 0;
            val.p_frozen_amount_temp = 0;
            if val.p_exchange_id as i64 == constant::EXCHANGE_HK {
                //更新汇率
                val.p_currency_rate = Decimal::from(1);
            } else {
                val.p_currency_rate = currency_rate_sell;
            }
            // val.p_currency_rate = currency_rate; //更新汇率
            val.p_prebuy_amount = 0;
            val.p_last_price = match last_price_map.get(&val.p_stock_id) {
                None => val.p_last_price,
                Some(last_price) => last_price.to_owned(),
            }
        });

        //重新计算汇总分帐户资产信息
        account_assets.iter_mut().for_each(|ret| {
            let pos_asset = PhoenixAccountAssetsService::compute_account_assets_from_positions_his(ret.p_account_id as i32, &account_positions);
            ret.id = 0;
            ret.p_financing_occupied = pos_asset.p_financing_occupied;
            ret.p_position_value = pos_asset.p_position_value;
            ret.p_position_value_star = pos_asset.p_position_value_star;
            ret.p_prebuy_capital_star = pos_asset.p_prebuy_capital_star;
            ret.p_floating_profit = pos_asset.p_floating_profit;
            ret.p_prebuy_margin = pos_asset.p_prebuy_margin;
            ret.p_prebuy_margin_star = pos_asset.p_prebuy_margin_star;
            ret.p_date = curdate;
            ret.p_updatetime = timeutil::current_timestamp();
        });

        let total_account = self.setting.read().await.system.account_total.parse::<i64>().unwrap_or_default();

        if account_id == total_account || account_id == 0 {
            let mut account_total = PhoenixAccountAssetsHis {
                id: 0,
                p_account_id: total_account,
                p_account_type: constant::AccountType::TotalAccount as i32,
                p_date: curdate as i32,
                p_updatetime: current_timestamp() as i64,
                // p_lastdate: currency_rate(),
                ..Default::default()
            };

            // info!("current user assets cache:{}", &self.user_assets_cache.len());
            for val in user_assets.iter() {
                let current_cash = val.real_cash;
                let position_value = val.total_position_value;
                let position_value_cyb = val.gem_position_value;
                let margin_use = val.real_margin;
                let margin_use_cyb = val.gem_margin_frozen_capital;
                let hold_yk = val.hold_yk;

                let mut current_borrowed = position_value - current_cash; //已借金额
                if current_borrowed < Decimal::from(0) {
                    current_borrowed = Decimal::from(0);
                }

                account_total.p_real_profit += current_borrowed; //已借金额保存在"实际盈亏"字段
                account_total.p_current_principal += current_cash;
                // account_total.p_credit_cash += Decimal::from_f64_retain(val.en_credit_cash).unwrap_or_default();
                account_total.p_position_value += position_value;
                account_total.p_position_value_star += position_value_cyb;

                account_total.p_prebuy_capital_star += margin_use_cyb + val.gem_trade_frozen_capital; //创业板维持保证金保存在"p_prebuy_capital_star"字段
                account_total.p_financing_occupied += margin_use + val.trade_frozen_capital; //维持保证金保存在"保证金占用"字段

                // account_total.p_prebuy_capital_star += Decimal::from_f64_retain(val.st_prebuy_value_star).unwrap_or_default();

                // account_total.p_financing_borrowed += current_borrowed;
                account_total.p_floating_profit += hold_yk;
            }

            info!("summarized total account: {:#?}", &account_total);

            let total_account = account_assets.iter_mut().find(|item| item.p_account_id == total_account);
            if let Some(total_account) = total_account {
                total_account.clone_from(&account_total)
            } else {
                account_assets.push(account_total);
            }
        }

        let _ = PhoenixStockPositionChannelHis::delete_many(account_id, curdate as i64, &self.dbconn).await?;
        let _ = PhoenixAccountAssetsHis::delete_many(account_id, curdate as i64, &self.dbconn).await?;

        let ret = self.tx_persist.send(PersistData::AccountAssetsHis(Box::new(account_assets))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }
        let ret = self.tx_persist.send(PersistData::AccountPositionHis(Box::new(account_positions))).await;
        if ret.as_ref().is_err() {
            error!("push error:{}", ret.as_ref().err().unwrap().to_string());
        }

        //delete from phoenix_deal_detail by date;
        result!(PhoenixDealDetailHis::delete_many(account_id, curdate as i64, &self.dbconn).await, "delete deal detail error");

        //insert into table: phoenix_deal_detail_his
        result!(
            PhoenixDealDetailHis::insert_many(&deal_list.iter().map(|(_, b)| b.to_owned()).collect_vec(), &self.dbconn).await,
            "insert many error"
        );

        Ok(())
    }
    #[allow(unused)]
    pub async fn compute_dvd_register(&self, before_date: i32, curdate: i32, _currency_rate_buy: Decimal, _currency_rate_sell: Decimal) -> Result<()> {
        info!("重跑权益分派: {}", curdate);

        let mut account_positions = PhoenixStockPositionChannelHis::query_many(constant::VALUE_ALL, curdate as i64, &self.dbconn).await?;
        info!("日期:{}得持仓数据数：{:?}", curdate, account_positions.len());
        let mut account_assets = PhoenixAccountAssetsHis::query_many(constant::VALUE_ALL, curdate, &self.dbconn).await?;
        info!("日期:{}得资产数据数：{:?}", curdate, account_assets.len());

        // let _ = PhoenixStockPositionChannelHis::delete_many(curdate as i64, &self.dbconn).await?;
        // let _ = PhoenixAccountAssetsHis::delete_many(curdate as i64, &self.dbconn).await?;

        // 处理前一天权益分派记录
        dvd_register(&self.dbconn, &self.aka_svc, self.uidsvc.clone(), before_date, curdate, &mut account_assets, &mut account_positions).await?;
        Ok(())
    }

    pub async fn persist_data(&self, persist_data: &PersistData) -> Result<()> {
        // info!("start to persist data ......{:?}", &persist_data);
        let mainnode = self.setting.read().await.system.mainnode;
        if mainnode == YesOrNo::NO as i32 {
            info!("非主节点，不保存到数据库");
            return Ok(());
        }
        // let now = SystemTime::now();
        match persist_data {
            PersistData::AccountAssets(data) => {
                // info!("start to persist data ......AccountAssets");
                let mut mut_data = data.to_owned();
                for val in mut_data.iter_mut() {
                    val.p_updatetime = current_timestamp();
                }
                let ret = PhoenixAccountAssets::save_many(&mut_data, &self.dbconn).await;
                if ret.as_ref().is_err() {
                    error!("update account assets error: {:?}", ret.as_ref().err());
                    return Err(anyhow!("update account assets error: {:?}", ret.as_ref().err()));
                }
                let _ = self.account_assets_svc.update_new_assets_cache_id(data, &self.dbconn).await;
            }
            PersistData::AccountAssetsHis(datas) => {
                // info!("start to persist data ......AccountAssetsHis");

                let mut mut_data = datas.to_owned();
                for val in mut_data.iter_mut() {
                    val.p_updatetime = current_timestamp();

                    let condition = Condition::all()
                        .add(phoenix_account_assets_his::Column::PAccountId.eq(val.p_account_id))
                        .add(phoenix_account_assets_his::Column::PDate.eq(val.p_date));

                    let opt: Option<PhoenixAccountAssetsHis> = result!(PhoenixAccountAssetsHis::find_by_condition(&self.dbconn, condition).await, "");
                    match opt {
                        None => {
                            val.id = 0;
                        }
                        Some(model) => {
                            val.id = model.id;
                        }
                    }
                }
                let ret = PhoenixAccountAssetsHis::save_many(&mut_data, &self.dbconn).await;
                if ret.as_ref().is_err() {
                    error!("save account assets into history error: {:?}", ret.as_ref().err());
                }
            }
            PersistData::AccountPosition(data) => {
                // info!("start to persist data ......AccountPosition");

                let ret = PhoenixStockPositionChannel::save_many(&data, &self.dbconn).await;
                if ret.as_ref().is_err() {
                    error!("update account stock position error: {:?}", ret.as_ref().err());
                    return Err(anyhow!("update account stock position error: {:?}", ret.as_ref().err()));
                }
                let _ = self.account_position_svc.update_new_position_cache_id(data, &self.dbconn).await;
            }
            PersistData::AccountPositionHis(datas) => {
                // info!("start to persist data ......AccountPositionHis");

                let mut mut_data = datas.to_owned();
                for val in mut_data.iter_mut() {
                    let condition = Condition::all()
                        .add(phoenix_stockposition_channel_his::Column::PAccountUnitId.eq(val.p_account_unit_id))
                        .add(phoenix_stockposition_channel_his::Column::PStockId.eq(val.p_stock_id))
                        .add(phoenix_stockposition_channel_his::Column::PChannelId.eq(val.p_channel_id))
                        .add(phoenix_stockposition_channel_his::Column::PDate.eq(val.p_date));

                    let opt: Option<PhoenixStockPositionChannelHis> = result!(PhoenixStockPositionChannelHis::find_by_condition(&self.dbconn, condition).await, "");
                    match opt {
                        None => {
                            val.id = 0;
                        }
                        Some(model) => {
                            val.id = model.id;
                        }
                    }
                }
                let ret = PhoenixStockPositionChannelHis::save_many(&mut_data, &self.dbconn).await;
                if ret.as_ref().is_err() {
                    error!("save account position into history error: {:?}", ret.as_ref().err());
                }
            }
            PersistData::UserAssets(_data) => {
                // info!("saveing user assets: {}(LOG ONLY)", &data.len());
                // let mut mut_data = data.to_owned();
                // for val in mut_data.iter_mut() {
                //   val.p_updatetime = current_timestamp();
                // }
                // let ret = PhoenixUserAssets::update_many(&data, &self.dbconn).await;
                // if ret.as_ref().is_err() {
                //   error!("insert user assets error: {:?}", ret.as_ref().err());
                // }
            }
            PersistData::UserAssetsHis(_data) => {
                // info!("saveing user assets history: {} (LOG ONLY)", &data.len());
                // let mut mut_data = data.to_owned();
                // for val in mut_data.iter_mut() {
                //   val.p_updatetime = current_timestamp();
                // }
                // let ret = PhoenixUserAssetsHis::save_many(&data, &self.dbconn).await;
                // if ret.as_ref().is_err() {
                //   error!("insert user assets error: {:?}", ret.as_ref().err());
                // }
            }
            PersistData::ResetDetail(data) => {
                // info!("start to persist data ......ResetDetail");

                let ret = PhoenixAccountResetDetail::insert(&data, &self.dbconn).await;
                if ret.as_ref().is_err() {
                    error!("save reset detail error:{:?}", ret.as_ref().err());
                }
            }
            PersistData::TransDetail(data) => {
                // info!("start to persist data ......TransDetail");

                // info!("save fund transfer detail: {:?}", &data);
                let ret = PhoenixTransDetail::insert(&data, &self.dbconn).await;
                if ret.as_ref().is_err() {
                    error!("save trans detail error:{:?}", ret.as_ref().err());
                }
            }

            PersistData::RiskUserAssets(data) => {
                // info!("start to persist data ......RiskUserAssets");

                if let Err(e) = PhoenixRiskDetails::insert(&data, &self.dbconn).await {
                    error!("{:?}", e);
                }
            }
            PersistData::RiskCancelUserAssets(data) => {
                // info!("start to persist data ......RiskCancelUserAssets");

                let user_id = data.to_i64();
                if user_id.is_some() {
                    _ = self.user_assets_svc.risk_cancel_user_assets(&self.manager_svc, user_id.unwrap()).await;
                }
            }
        }
        // info!("persist completed, elapsed: {}", now.elapsed().unwrap().as_secs_f32());
        Ok(())
    }

    // async fn update_positions_prebuy_amount(&self, dealdetail: &PhoenixDealDetail, channelinfo: &ChannelInfo) -> Result<()> {
    //   let ret = self.account_position_svc.update_account_positions_prebuy_amount(&dealdetail, &channelinfo, &self.sys_info, &self.dbconn).await;
    //   if ret.as_ref().is_err() {
    //     return Err(anyhow!("更新分帐户预买量错误:{}", ret.as_ref().err().unwrap().to_string()));
    //   }
    //   let (account_position, is_new) = ret.unwrap();
    //   if is_new {
    //     //insert new into his table
    //     let his_account_position = CommonService::convert_stockpositions_to_stockpositionhis(&account_position, self.sys_info.preinit_date);
    //     info!("需要写入的分帐户持仓信息:{:#?}", his_account_position);
    //     let his_account_positions = vec![his_account_position];
    //     let ret = self.tx_persist.send(PersistData::AccountPositionHis(Box::new(his_account_positions))).await;
    //     if ret.as_ref().is_err() {
    //       error!("push error:{}", ret.as_ref().err().unwrap().to_string());
    //     }
    //   }
    //   let account_positions = vec![account_position];
    //   let ret = self.tx_persist.send(PersistData::AccountPosition(Box::new(account_positions))).await;
    //   if ret.as_ref().is_err() {
    //     error!("push error:{}", ret.as_ref().err().unwrap().to_string());
    //   }
    //   Ok(())
    // }
    #[allow(unused)]
    async fn update_assets_and_position_by_deal(&self, dealinfo: &mut PhoenixDealDetail) -> Result<()> {
        //根据订单信息计算资产数据
        //如果是内撮合的订单,则不处理分账户资产,但需要处理用户资产。
        //T0交易,则不需要处理用户资产,但要处理分帐户资产
        //还要判断通道类型，是否
        let sys_info = self.sys_info.read().await;
        let ap_ret = self.account_position_svc.update_account_stock_positions_by_dealinfo(&dealinfo, sys_info.init_date, &self.dbconn).await;
        if ap_ret.is_err() {
            error!("update account stock position error");
            return Err(anyhow!("update account stock position error"));
        }
        let ap_ret = ap_ret.unwrap();

        let aa_ret = self.account_assets_svc.update_account_assets_by_dealinfo(true, dealinfo, &ap_ret, sys_info.init_date).await;
        if aa_ret.is_err() {
            error!("update account assets error");
            return Err(anyhow!("update account assets error"));
        }

        Ok(())
    }
    //运营账户，分帐户和分帐户之间不能直接划转
    #[tracing::instrument(name = "资金划转", skip_all)]
    pub async fn transfer_fund(&self, req: &PhoenixTransferRequest) -> Result<PhoenixAssetsResponse> {
        let mut res = PhoenixAssetsResponse {
            ret_code: errors::get_error_code(errors::ErrorCode::CodeOk).0,
            ret_msg: errors::get_error_code(errors::ErrorCode::CodeOk).1,
            data: vec![],
        };

        if req.target_account == constant::VALUE_ALL {
            error!("target account shoule not be 0");
            res.ret_code = errors::get_error_code(errors::ErrorCode::CodeSystemNotPermitted).0;
            res.ret_msg = errors::get_error_code(errors::ErrorCode::CodeSystemNotPermitted).1;
            res.data = vec![];
            return Ok(res);
        }

        // let account_asset = account_assets[0];
        let transfer_detail = PhoenixTransDetail {
            id: 0,
            p_account_target: req.target_account,
            p_account_source: req.source_account,
            p_op_flag: req.transfer_type,
            p_capital_type: req.capital_type,
            p_account_no: req.operator_id as i32,
            p_remark: req.memo.to_owned(),
            p_trans_value: Decimal::from_f64(req.transfer_value).unwrap_or_default(),
            p_datetime: current_timestamp(),
            p_date: self.sys_info.read().await.init_date as u32,
        };

        match self.account_assets_svc.transfer_fund(&transfer_detail).await {
            Ok(_) => {
                //send data to persist command
                let mut account_target = self.account_assets_svc.get_account_assets(transfer_detail.p_account_target).await;
                let source_target = self.account_assets_svc.get_account_assets(transfer_detail.p_account_source).await;
                account_target.extend(source_target);
                let ret = self.tx_persist.send(PersistData::AccountAssets(Box::new(account_target))).await;
                if ret.as_ref().is_err() {
                    error!("push error:{}", ret.as_ref().err().unwrap().to_string());
                }
                let ret = self.tx_persist.send(PersistData::TransDetail(Box::new(transfer_detail))).await;
                if ret.as_ref().is_err() {
                    error!("push error:{}", ret.as_ref().err().unwrap().to_string());
                }
                Ok(res)
            }
            Err(e) => {
                error!("trans fund error:{}", &e);
                res.ret_code = errors::get_error_code(errors::ErrorCode::CodeSystemNotPermitted).0;
                res.ret_msg = format!("trans fund error:{}", &e);
                res.data = vec![];
                return Ok(res);
            }
        }
        // Ok(res)
    }

    pub async fn query_stock_positions(&self, req: &PhoenixStockPositionRequest) -> Result<Vec<PhoenixStockPositions>> {
        // let mut ret: Vec<PhoenixStockPositions> = Vec::new();

        let account_assets = self.account_position_svc.query_stock_positions(req.stock_id, req.channel_id).await;

        let ret: Vec<PhoenixStockPositions> = account_assets
            .iter()
            .map(|x| PhoenixStockPositions {
                stock_id: x.p_stock_id as i64,
                channel_id: x.p_channel_id as i64,
                current_amount: x.p_current_amount as i64,
                prebuy_amount: x.p_prebuy_amount as i64, //预买数量
                frozen_amount: x.p_frozen_amount as i64,
                frozen_amount_temp: x.p_frozen_amount_temp as i64,
                total_value: x.p_total_value.to_f64().unwrap_or_default(),
                total_value_hkd: x.p_total_value_hkd.to_f64().unwrap_or_default(),
                stock_type: x.p_stock_type,
                is_qfii: x.p_qfii_state,
            })
            .collect();

        Ok(ret)
    }

    //每日定时持久化用户风控数据
    pub async fn interval_save_unit_risk_data(&self) -> Result<()> {
        let sysdate = self.sys_info.read().await.preinit_date;
        _ = self.user_assets_svc.interval_save_unit_risk_data(sysdate, &self.dbconn).await;
        Ok(())
    }

    //收到资产变更消息
    pub async fn update_user_assets_by_notification(&self, nassets: &NotificationAsset) {
        let data = UserAssetsData::convert_notificationasset_to_assetsdata(&nassets);
        let ret = self.user_assets_svc.update_user_assets(&data, &self.aka_svc).await;
        if ret.as_ref().is_err() {
            error!("update user assets error");
        }
        //需要重算资产......
        let send_ret = self.tx_assets.send((data.unit_id, data.user_id)).await;
        if send_ret.as_ref().is_err() {
            error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
        }
    }
    //收到持仓变更消息
    pub async fn update_user_position_by_notification(&self, npositions: &NotificationPosition) {
        if self.securities_borrow_pool.is_exit(npositions.stock_id).await {
            info!("交易品种在融券池内");
            let send_ret = self.tx_rq.send((npositions.stock_id, npositions.unit_id, npositions.use_credit)).await;
            if send_ret.as_ref().is_err() {
                error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
            }
        }

        let currency = self.user_assets_svc.query_currency_by_unit_id(npositions.unit_id).await.unwrap_or_default();
        let user_posinfo = UserPositionData::convert_notificationposition_to_userpositioninfo(npositions, &self.aka_svc, &currency).await;

        self.user_position_svc.update_user_positions(&user_posinfo).await;

        //需要重算资产......
        let send_ret = self.tx_assets.send((user_posinfo.unit_id, npositions.user_id)).await;
        if send_ret.as_ref().is_err() {
            error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
        }
    }

    //重算资产 caltype//是否需要推送给客户重新刷新页面
    pub async fn re_calculate_user_assets(&self, unit_id: i64, user_id: i64, _caltype: bool) -> Result<()> {
        //重算用户的资产信息,包括风险率
        // 1)从用户持仓模块获取所有的持仓数据（根据unitid)
        // 2)计算用户资产和风险率 update_user_assets_by_positions
        let mut vec_unit = Vec::new();
        if unit_id > 0 {
            vec_unit.push((unit_id, user_id));
        } else {
            vec_unit = self.user_assets_svc.get_all_unit_id().await;
        }
        let risk_restore = self.setting.read().await.system.risk_restore;

        for (unit_id, user_id) in vec_unit {
            let p_vec = self.user_position_svc.query_user_positions(unit_id, user_id, 0).await;

            let init_date = self.sys_info.read().await.init_date;
            _ = self
                .user_assets_svc
                .compute_user_assets(&self.manager_svc, unit_id, user_id, &p_vec, &self.tx_persist, risk_restore, init_date)
                .await;
        }

        // if caltype {
        //   let data = NotificationPosition { unit_id: unit_id, ..Default::default() };
        //   _ = self.publish_data(&NotificationData::UserPosition(Box::new(data))).await;
        //   let data = NotificationAsset { unit_id: unit_id, ..Default::default() };
        //   _ = self.publish_data(&NotificationData::UserAssets(Box::new(data))).await;
        // }
        Ok(())
    }

    pub async fn update_rq_and_check(&self, stock_id: i64, user_id: i64, use_credit: i32) -> Result<()> {
        if self.securities_borrow_pool.user_is_exit(stock_id, user_id).await {
            self.update_stock_use_credit(stock_id, user_id, use_credit).await?;
            self.sat_client.modify_used(stock_id, user_id, use_credit).await?;
        }

        if let (Some(total_amount), Some(total_used)) = (self.securities_borrow_pool.get_total_amount(stock_id).await, self.securities_borrow_pool.get_total_used_amount(stock_id).await) {
            let available_amount = total_amount - total_used;

            let all_total_amount = self.query_account_available_amount(stock_id).await?;

            if available_amount > all_total_amount {
                warn!("触发召回 stock_id: {}", stock_id);
                info!("available_amount: {}, all_total_amount: {}", available_amount, all_total_amount);
                self.securities_borrow_pool.clear_amount(stock_id).await?;
                self.sat_client.recall_rq(stock_id).await?;
            }
        } else {
            error!("获取融券池失败： stock_id: {}", stock_id);
        }

        Ok(())
    }

    //持仓发生变化时，改变股票已卖数
    pub async fn update_stock_use_credit(&self, stock_id: i64, user_id: i64, use_credit: i32) -> Result<()> {
        let (_, _, _, last_used_amount) = self.securities_borrow_pool.get_amount(stock_id, user_id).await;
        self.securities_borrow_pool.set_use_credit(stock_id, user_id, use_credit).await?;
        self.securities_borrow_pool.update_total_used_amount(stock_id, use_credit - last_used_amount).await
    }

    //汇率发生变化,需要更新持仓的卖汇率
    pub async fn set_stock_position_sell_rate(&self, req: &ExchangeRateChange) {
        info!("set_stock_position_rate收到推送信息,{:?}", req);
        self.user_position_svc.update_positions_sell_rate(req).await;
        //需要重算所有人资产......
        let send_ret = self.tx_assets.send((0, 0)).await;
        if send_ret.as_ref().is_err() {
            error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
        }
    }

    //接受停牌信息推送
    pub async fn set_stock_suspension(&self, req: &StockSuspensionChange) {
        info!("set_stock_suspension收到推送信息,{:?}", req);
        let req_vec = &req.stock_suspension;
        let mut unitid_vec = HashSet::new();
        for item in req_vec {
            let positions = self.user_position_svc.query_user_positions(0, 0, item.stock_id).await;
            if positions.len() == 0 {
                continue;
            }
            for i in positions {
                //获取最新的比例
                let credit_multiple = self.user_assets_svc.get_user_credit_multiple(i.user_id).await;
                if let Err(err) = credit_multiple {
                    info!("get_unit_credit_multiple异常,{:?}", err);
                    continue;
                }
                let ret = self.basic_cache_svc.calculation_user_stock_rate(&self.aka_svc, item.stock_id, i.user_id, credit_multiple.unwrap()).await;
                if let Err(err) = ret {
                    info!("calculation_user_stock_rate异常,{:?}", err);
                    continue;
                }
                let rate = ret.unwrap();
                //更新持仓里面的保证金比例
                let v = self.user_position_svc.update_positions_margin_rate(rate, i.user_id, item.stock_id).await;
                //更新资产中心缓存及数据库保证金比例
                let param = PositionMarginRateReq { rates: v };
                _ = self.assetscenter_svc.phoenix_positions_marginrate_change(&param).await;
                unitid_vec.insert((i.unit_id, i.user_id));

                let data = UserClientNotifyDataChange {
                    user_id: i.user_id,
                    stock_id: item.stock_id,
                    margin_rate: rate,
                };
                _ = self.publish_data(&NotificationData::UserRateMargin(Box::new(data))).await;
            }
        }
        //重算资产
        for i in unitid_vec {
            let send_ret = self.tx_assets.send(i).await;
            if send_ret.as_ref().is_err() {
                error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
            }
        }
    }

    //接收推送设置品种的保证金，
    pub async fn set_stock_margin_rate(&self, stock_id: i64) {
        //删除缓存信息
        _ = self.aka_svc.delete_stock_info_cache(stock_id).await;

        let positions = self.user_position_svc.query_user_positions(0, 0, stock_id).await;
        for item in positions {
            //获取最新的比例
            let credit_multiple = self.user_assets_svc.get_user_credit_multiple(item.user_id).await;
            if let Err(err) = credit_multiple {
                info!("get_unit_credit_multiple异常,{:?}", err);
                continue;
            }
            let ret = self.basic_cache_svc.calculation_user_stock_rate(&self.aka_svc, stock_id, item.user_id, credit_multiple.unwrap()).await;
            if let Err(err) = ret {
                info!("calculation_user_stock_rate异常,{:?}", err);
                continue;
            }
            let rate = ret.unwrap();
            //更新持仓里面的保证金比例
            let v = self.user_position_svc.update_positions_margin_rate(rate, item.user_id, stock_id).await;
            //更新资产中心缓存及数据库保证金比例
            let param = PositionMarginRateReq { rates: v };
            _ = self.assetscenter_svc.phoenix_positions_marginrate_change(&param).await;
            //需要重算资产......
            let send_ret = self.tx_assets.send((item.unit_id, item.user_id)).await;
            if send_ret.as_ref().is_err() {
                error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
            }

            let data = UserClientNotifyDataChange {
                user_id: item.user_id,
                stock_id,
                margin_rate: rate,
            };
            _ = self.publish_data(&NotificationData::UserRateMargin(Box::new(data))).await;
        }
    }

    //接受推送设置用户品种的保证金比例
    pub async fn set_unit_stock_margin_rate(&self, req: &UserStockMarginChange) {
        let req_vec = &req.margin_data;
        for item in req_vec {
            //判断此用户是否有该品种的持仓。没有直接返回不处理
            let positions = self.user_position_svc.query_user_positions(0, item.user_id, item.stock_id).await;
            if positions.len() == 0 {
                continue;
            }
            //获取最新的比例
            let credit_multiple = self.user_assets_svc.get_user_credit_multiple(item.user_id).await;
            if let Err(err) = credit_multiple {
                info!("get_unit_credit_multiple异常,{:?}", err);
                return;
            }
            let ret = self.basic_cache_svc.calculation_user_stock_rate(&self.aka_svc, item.stock_id, item.user_id, credit_multiple.unwrap()).await;
            if let Err(err) = ret {
                info!("calculation_user_stock_rate异常,{:?}", err);
                return;
            }
            let rate = ret.unwrap();
            //更新持仓里面的保证金比例
            let v = self.user_position_svc.update_positions_margin_rate(rate, item.user_id, item.stock_id).await;
            //更新资产中心缓存及数据库保证金比例

            let param = PositionMarginRateReq { rates: v };
            _ = self.assetscenter_svc.phoenix_positions_marginrate_change(&param).await;
            //需要重算资产......
            let send_ret = self.tx_assets.send((0, item.user_id)).await;
            if send_ret.as_ref().is_err() {
                error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
            }

            let data = UserClientNotifyDataChange {
                user_id: item.user_id,
                stock_id: item.stock_id,
                margin_rate: rate,
            };
            _ = self.publish_data(&NotificationData::UserRateMargin(Box::new(data))).await;
        }
    }

    //接受推送设置用户的杠杠比例
    pub async fn set_unit_credit_multiple(&self, req: &AccountInfoChange) {
        //判断杠杠倍数是否相同
        // let credit_multiple = self.user_assets_svc.get_unit_credit_multiple(req.unit_id).await;
        // if let Err(err) = credit_multiple {
        //   return;
        // }

        // let level_rate = req.level_rate.parse::<f64>();
        // if let Err(err) = level_rate {
        //   return;
        // }

        // let r = level_rate.unwrap();
        // //若杠杠倍数未发生变化，直接返回
        // if credit_multiple.unwrap() == r {
        //   return;
        // }
        //获取最新的比例
        let credit_multiple = self.user_assets_svc.get_user_credit_multiple(req.user_id).await;
        if let Err(err) = credit_multiple {
            info!("get_unit_credit_multiple异常,{:?}", err);
            return;
        }

        let credit_multiple = credit_multiple.unwrap();
        let positions = self.user_position_svc.query_user_positions(0, req.user_id, 0).await;
        for item in positions {
            //获取最新的比例
            let ret = self.basic_cache_svc.calculation_user_stock_rate(&self.aka_svc, item.stock_id, req.user_id, credit_multiple).await;
            if let Err(err) = ret {
                info!("calculation_user_stock_rate异常,{:?}", err);
                return;
            }
            let rate = ret.unwrap();
            //更新持仓里面的保证金比例
            let v = self.user_position_svc.update_positions_margin_rate(rate, req.user_id, item.stock_id).await;
            //更新资产中心缓存及数据库保证金比例

            let param = PositionMarginRateReq { rates: v };
            _ = self.assetscenter_svc.phoenix_positions_marginrate_change(&param).await;

            let data = UserClientNotifyDataChange {
                user_id: req.user_id,
                stock_id: item.stock_id,
                margin_rate: rate,
            };
            _ = self.publish_data(&NotificationData::UserRateMargin(Box::new(data))).await;
        }
        //需要重算资产......
        let send_ret = self.tx_assets.send((0, req.user_id)).await;
        if send_ret.as_ref().is_err() {
            error!("send error:{}", send_ret.as_ref().err().unwrap().to_string());
        }
    }

    //接收推送更新用户的交易状态
    pub async fn is_changed(&self, req: &AccountInfoChange) -> bool {
        self.user_assets_svc.is_changed(req).await
    }

    //接收推送更新用户的交易状态
    pub async fn set_user_trade_flag(&self, req: &AccountInfoChange) {
        //更新状态
        _ = self
            .user_assets_svc
            .set_user_trade_flag(req.user_id, req.trade_state as i32, req.warning_line, req.close_line, req.level_rate.parse::<f64>().unwrap_or_default())
            .await;
    }

    //查询用户品种保证金比例
    pub async fn query_margin_ratio(&self, req: &MarginRatioReq) -> Result<MarginRatioResp, Status> {
        let mut result = MarginRatioResp {
            stock_id: req.stock_id,
            ret_code: constdata::DEFAULT_SUCCESS_CODE.to_string(),
            ret_msg: constdata::DEFAULT_SUCCESS.to_string(),
            margin_ratio: 0.0,
            user_id: req.user_id,
        };

        let credit_multiple = self.user_assets_svc.get_user_credit_multiple(req.user_id).await;
        if let Err(err) = credit_multiple {
            info!("get_unit_credit_multiple异常,{:?}", err);
            result.ret_code = constdata::DEFAULT_ERROR_CODE.to_string();
            result.ret_msg = constdata::DEFAULT_USER_NOT_FOUND.to_string();
            return Ok(result);
        }

        let ret = self.basic_cache_svc.calculation_user_stock_rate(&self.aka_svc, req.stock_id, req.user_id, credit_multiple.unwrap()).await;
        if let Err(err) = ret {
            info!("calculation_user_stock_rate异常,{:?}", err);
            result.ret_code = constdata::DEFAULT_ERROR_CODE.to_string();
            result.ret_msg = constdata::DEFAULT_USER_NOT_FOUND.to_string();
            return Ok(result);
        }

        result.margin_ratio = ret.unwrap();

        Ok(result)
    }

    //查询用户资产
    pub async fn query_user_asset(&self, req: &UserAssetsReq) -> Result<UserAssetsResp, Status> {
        let mut result = UserAssetsResp { ..Default::default() };
        info!("query_user_asset1,{:?}", req);
        let r = self.user_assets_svc.query_assets(req).await;

        if let Err(err) = r {
            result.ret_code = constdata::DEFAULT_ERROR_CODE.to_string();
            result.ret_msg = err.to_string();
        } else {
            result = r.unwrap();
            result.ret_code = constdata::DEFAULT_SUCCESS_CODE.to_string();
            result.ret_msg = constdata::DEFAULT_SUCCESS.to_string();
        }
        Ok(result)
    }

    //查询用户持仓
    pub async fn query_user_positions(&self, req: &mut UserPositionReq) -> Result<UserPositionResp, Status> {
        let mut result = UserPositionResp { ..Default::default() };
        info!("query_user_positions,{:?}", req);

        if req.user_id != 0 {
            req.unit_id = 0;
        } else if req.unit_id != 0 {
            req.user_id = self
                .user_assets_svc
                .query_user_id_by_unit_id(req.unit_id)
                .await
                .ok_or(Status::unknown(format!("用户不存在 unit_id:{}", req.unit_id)))?;
        }
        let mut r = self.user_position_svc.query_user_positioins_rpc(req.unit_id, req.user_id, req.stock_id).await;

        if req.unit_id != 0 || req.user_id != 0 {
            match req.stock_id.eq(&constant::VALUE_ALL) {
                true => {
                    info!("查询用户:{} 的所有持仓信息", req.user_id);
                    let mut rq_amounts = self.securities_borrow_pool.get_amount_by_user_id(req.user_id).await;
                    info!("获取到{}条用户融券信息", rq_amounts.len());
                    for item in r.iter_mut() {
                        let index = rq_amounts.iter().position(|v| v.0 == item.stock_id);
                        item.securities_borrow_available = if let Some(index) = index {
                            let available_amount = rq_amounts[index].2;
                            rq_amounts.remove(index);
                            available_amount
                        } else {
                            0
                        };
                    }

                    if !rq_amounts.is_empty() {
                        info!("用户存在{}条未持仓的融券额度", rq_amounts.len());
                        for rq_amount in rq_amounts.iter() {
                            let stock_info = result!(self.aka_svc.query_stock_info(rq_amount.0).await);
                            if let None = stock_info {
                                info!("query_stock_info 找不到信息,{:?}", rq_amount.0);
                                return Err(Status::not_found(format!("query_stock_info 找不到信息,{:?}", rq_amount.0)));
                            }
                            let stock_info = stock_info.unwrap();
                            r.push(PhoenixUserPositions {
                                unit_id: req.unit_id,
                                stock_id: stock_info.stock_id,
                                stock_code: stock_info.stock_code,
                                exchange_id: stock_info.market_id,
                                amount: 0,
                                frozen_amount: 0,
                                prebuy_amount: 0,
                                qfii_amount: 0,
                                margin_ratio: stock_info.margin_rate,
                                total_value_hkd: 0.0,
                                last_price: 0.0,
                                stock_type: stock_info.stock_type,
                                securities_borrow_available: self.securities_borrow_pool.get_amount(stock_info.stock_id, req.user_id).await.2,
                                presale_amount: 0,
                                user_id: req.user_id,
                            })
                        }
                    }
                }
                false => {
                    info!("查询用户:{} 的品种 {}持仓信息", req.user_id, req.stock_id);
                    if r.is_empty() {
                        warn!("没有找到用户持仓信息");
                        let stock_info = result!(self.aka_svc.query_stock_info(req.stock_id).await);
                        if let None = stock_info {
                            info!("query_stock_info 找不到信息,{:?}", req.stock_id);
                            return Err(Status::not_found(format!("query_stock_info 找不到信息,{:?}", req.stock_id)));
                        }
                        let stock_info = stock_info.unwrap();
                        r.push(PhoenixUserPositions {
                            unit_id: req.unit_id,
                            stock_id: req.stock_id,
                            stock_code: stock_info.stock_code,
                            exchange_id: stock_info.market_id,
                            amount: 0,
                            frozen_amount: 0,
                            prebuy_amount: 0,
                            qfii_amount: 0,
                            margin_ratio: stock_info.margin_rate,
                            total_value_hkd: 0.0,
                            last_price: 0.0,
                            stock_type: stock_info.stock_type,
                            securities_borrow_available: self.securities_borrow_pool.get_amount(stock_info.stock_id, req.user_id).await.2,
                            presale_amount: 0,
                            user_id: req.user_id,
                        })
                    } else {
                        for item in r.iter_mut() {
                            item.securities_borrow_available = self.securities_borrow_pool.get_amount(item.stock_id, item.user_id).await.2;
                        }
                    }
                }
            }
        } else {
            info!("查询该品种全部用户持仓");
            for item in r.iter_mut() {
                item.securities_borrow_available = self.securities_borrow_pool.get_amount(item.stock_id, item.user_id).await.2;
            }
        }

        info!("找到{}条持仓数据并返回", r.len());

        result.positions = r;
        result.ret_code = constdata::DEFAULT_SUCCESS_CODE.to_string();
        result.ret_msg = constdata::DEFAULT_SUCCESS.to_string();
        Ok(result)
    }

    pub async fn query_user_rq(&self, req: &UserRqReq) -> Result<UserRqResp, Status> {
        let pos_infos = self.user_position_svc.query_user_positions(0, req.user_id, req.stock_id).await;

        let mut rq_infos = self.securities_borrow_pool.get_amounts(req.stock_id, req.user_id).await;

        let mut data: Vec<_> = pos_infos
            .into_iter()
            .filter_map(|info| {
                let used_amount = info.sale_amount + info.presale_amount - info.begin_amount;

                let index = rq_infos.iter().position(|v| v.0 == info.stock_id && v.1 == info.user_id);

                let available_amount = if let Some(index) = index {
                    let available_amount = rq_infos[index].2;
                    rq_infos.remove(index);
                    available_amount
                } else {
                    if used_amount <= 0 {
                        return None;
                    } else {
                        0
                    }
                };

                Some(UserRqListResp {
                    user_id: info.user_id,
                    stock_id: info.stock_id,
                    total: available_amount,
                    user_credit: used_amount.max(0),
                })
            })
            .collect();

        if !rq_infos.is_empty() {
            let data_: Vec<_> = rq_infos
                .into_iter()
                .map(|info| UserRqListResp {
                    user_id: info.1,
                    stock_id: info.0,
                    total: info.2,
                    user_credit: 0,
                })
                .collect();
            data.extend_from_slice(&data_)
        }

        Ok(UserRqResp {
            ret_code: constdata::DEFAULT_SUCCESS_CODE.to_string(),
            ret_msg: constdata::DEFAULT_SUCCESS.to_string(),
            rqlist: data,
        })
    }

    pub async fn query_account_available_amount(&self, stock_id: i64) -> Result<i32> {
        let vec_data = self.account_position_svc.query_stock_positions(stock_id, constant::VALUE_ALL).await;
        let all_total_amount: i32 = vec_data
            .iter()
            .filter_map(|data| {
                if data.p_channel_id == 7 {
                    None
                } else {
                    Some(data.p_current_amount - data.p_frozen_amount - data.p_frozen_amount_temp)
                }
            })
            .sum();

        Ok(all_total_amount)
    }

    //定时持久化持仓最新价
    pub async fn phoenix_positions_price_change(&self) -> Result<()> {
        let ret = self.user_position_svc.query_position_price().await;
        let param = PositionPriceChangeReq { list: ret };
        _ = self.assetscenter_svc.phoenix_positions_price_change(&param).await;
        Ok(())
    }

    //查询通道持仓
    // pub async fn query_channel_position(&self, req: &ChannelPositionReq) -> Result<ChannelPositionResp, Status> {
    //   let mut result = ChannelPositionResp { ..Default::default() };
    //   let ret = self.assets_svc.query_channel_position(req, &self.dbconn).await;
    //   if let Err(err) = ret {
    //     result.ret_code = constdata::DEFAULT_ERROR_CODE.to_string();
    //     result.ret_msg = err.to_string();
    //   } else {
    //     result = ret.unwrap();
    //     result.ret_code = constdata::DEFAULT_SUCCESS_CODE.to_string();
    //     result.ret_msg = constdata::DEFAULT_SUCCESS.to_string();
    //   }
    //   Ok(result)
    // }
}
