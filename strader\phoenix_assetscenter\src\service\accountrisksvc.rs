use std::sync::Arc;

use anyhow::{anyhow, Result};
use common::{
    constant,
    logclient::{LogClient, LogLevel},
};
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;

use crate::{
    app::constdata,
    config::settings::Settings,
    protofiles::phoenixaccountriskcenter::{account_risk_center_client::AccountRiskCenterClient, MarginRatioReq},
};

#[derive(Clone)]
pub struct Accountrisksvc {
    pub accountrisk_client: Arc<RwLock<Option<AccountRiskCenterClient<Channel>>>>, //资产中心
}

impl Accountrisksvc {
    pub async fn push_log(&self, log: String) {
        error!("{}", log);
        let logclient = LogClient::get();
        if let Ok(c) = logclient {
            _ = c.push(LogLevel::Error, log.as_str()).await;
        }
    }

    //风控服务和资产服务相互依赖了。若在这里初始化会导致启动不起来
    pub async fn new(config: &Settings) -> Self {
        let serverurl = config.system.accountriskserver.clone();
        let client = AccountRiskCenterClient::connect(serverurl).await;
        match client {
            Ok(svc) => {
                let ret = Accountrisksvc {
                    accountrisk_client: Arc::new(RwLock::new(Some(svc))),
                };
                return ret;
            }
            Err(err) => {
                let logclient = LogClient::get();
                if let Ok(c) = logclient {
                    _ = c.push(LogLevel::Error, format!("风控服务连接失败:{:?}", err).as_str()).await;
                }
                let ret = Accountrisksvc {
                    accountrisk_client: Arc::new(RwLock::new(None)),
                };
                return ret;
            }
        }
    }

    pub async fn query_margin_rate(&self, unit_id: i64, stock_id: i64) -> Result<f64> {
        let mut margin_rate = 1.0;
        let req = MarginRatioReq { unit_id: unit_id, stock_id: stock_id };
        let mut client = self.accountrisk_client.to_owned();
        let mut flag = false;

        {
            if client.read().await.is_none() {
                flag = true;
            }
        }
        if flag {
            let config = Settings::new();
            if let Ok(c) = config {
                let serverurl = c.system.accountriskserver.clone();
                let serverclient = AccountRiskCenterClient::connect(serverurl).await;
                match serverclient {
                    Ok(svc) => {
                        *client.write().await = Some(svc);
                    }
                    Err(err) => {
                        self.push_log(format!("风控服务连接失败:{:?}", err)).await;
                        return Err(anyhow!("风控服务连接失败!"));
                    }
                }
                client = self.accountrisk_client.to_owned();
            }
        }

        let mut c = client.read().await.to_owned().unwrap();
        let ret = c.query_margin_ratio(req).await;
        if let Err(err) = ret {
            error!("query_margin_rate查询失败,{},{},{:?}", unit_id, stock_id, err);
            return Ok(margin_rate);
        }
        let m = ret.unwrap().into_inner();
        info!("query_margin_rate返回,{:?}", m);
        if m.ret_code == constdata::SUCCESS_CODE {
            margin_rate = m.margin_ratio;
        }
        Ok(margin_rate)
    }
}
