use crate::protofiles::hqcenter::svr_post_subscribe_hq_msg_client::SvrPostSubscribeHqMsgClient;
use crate::protofiles::hqcenter::{KLineHqInfo, KLineHqReq, LastPriceMsgReq};
use common::logclient::LogClient;
use tonic::transport::Channel;
use tracing::*;

#[derive(Clone)]
pub struct HqCenterClient {
    pub client: SvrPostSubscribeHqMsgClient<Channel>,
}
impl HqCenterClient {
    pub async fn new(url: &String) -> Self {
        loop {
            match SvrPostSubscribeHqMsgClient::connect(url.clone()).await {
                Ok(hqclient) => return Self { client: hqclient },
                Err(err) => {
                    error!("connect to HqCenterClient failed: {:?}", err);
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("HqCenterClient connect faild :{}", err.to_string())).await;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn post_history_k_line_hq(&mut self, stock_code: String, end_time: &String) -> anyhow::Result<Vec<KLineHqInfo>> {
        info!("请求 post_history_k_line_hq stock_code: {} end_time: {}", &stock_code, end_time);
        let ret = self
            .client
            .post_history_k_line_hq(KLineHqReq {
                strcontractno: stock_code,
                strklinetype: "24".to_string(),
                strendtime: format!("{}000000", end_time),
                limit: 0,
                realtime: 1,
            })
            .await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }

        let kline = ret.unwrap();
        info!("post_history_k_line_hq 返回: {:?}", &kline);
        Ok(kline.into_inner().klineinfo)
    }

    pub async fn get_last_price(&mut self, stock_code: String, exchange_id: i32) -> anyhow::Result<f64> {
        info!("请求 post_history_k_line_hq stock_code: {} exchange_id: {}", &stock_code, exchange_id);
        let ret = self.client.get_last_price(LastPriceMsgReq { stock_code, exchange_id }).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }

        let ret = ret.unwrap();
        info!("post_history_k_line_hq 返回: {:?}", &ret);

        let last_price = ret.into_inner();
        if last_price.data.is_none() {
            return Err(anyhow!("last price are not found."));
        }

        Ok(last_price.data.unwrap().last_price)
    }
}

#[cfg(test)]
mod test {
    use crate::protofiles::hqcenter::KLineHqReq;
    use crate::server::client::hq_client::HqCenterClient;

    #[tokio::test]
    async fn hq_test() {
        let mut client = HqCenterClient::new(&"http://52.131.220.224:8416".to_string()).await;
        let req = KLineHqReq {
            strcontractno: "03690_XHKG".to_string(),
            strklinetype: "24".to_string(),
            strendtime: "20240624000000".to_string(),
            limit: 0,
            realtime: 1,
        };
        println!("{:#?}", client.post_history_k_line_hq(req.strcontractno, &req.strendtime).await)
    }
}
