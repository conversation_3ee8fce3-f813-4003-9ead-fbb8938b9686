use crate::protofiles::managerunit::manager_unit_client::ManagerUnitClient;
use crate::protofiles::managerunit::{OptionQuotationReq, OptionQuotationResp};
use common::logclient::LogClient;
use tonic::transport::Channel;
use tracing::*;

#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct ManagerClient {
    pub client: ManagerUnitClient<Channel>,
    pub url: String,
}

impl ManagerClient {
    pub async fn new(url: &String) -> Self {
        loop {
            match ManagerUnitClient::connect(url.clone()).await {
                Ok(hqclient) => {
                    info!("connect to {} Ok...", url);
                    return Self { client: hqclient, url: url.to_string() };
                }
                Err(err) => {
                    error!("connect to ManagerUnitClient failed: {:?}", err);
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("ManagerUnitClient connect faild :{}", err.to_string())).await;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn query_option_quotations(&mut self) -> Option<OptionQuotationResp> {
        let req = OptionQuotationReq::default();
        match self.client.query_option_quotation(req).await {
            Ok(data) => {
                let resp = data.into_inner();
                Some(resp)
            }
            Err(err) => {
                error!("{:?}", err);
                return None;
            }
        }
    }
}
