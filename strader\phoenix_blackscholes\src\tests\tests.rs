use crate::bscommon::blackscholes::OptionType;
use crate::config::settings::Settings;
use crate::dataservice::dbsetup::DbConnection;
use crate::server::client::hq_client::HqCenterClient;
use crate::server::client::managerunitclient::ManagerClient;
use crate::server::controller::{BlackscholesController, VolatilityFlag};
use akaclient::akaclient::{AkaCacheOption, AkaClient};
use std::sync::Arc;
use tokio::sync::RwLock;
use utility::loggings;

#[cfg(test)]
mod tests {

    use crate::bscommon::{
        blackscholes::{black_scholes, black_scholes_v2, cnd, volatility},
        bscommon::compute_volatility,
    };

    use super::*;

    #[test]
    fn test_black_scholes_v2_call() {
        let result = black_scholes_v2(OptionType::Call, 100.0, 100.0, 1.0, 0.05, 0.2);
        assert!(result.is_ok());
        let value = result.unwrap();
        assert!((value - 10.450583).abs() < 1e-6); // Replace with actual expected value
    }

    #[test]
    fn test_black_scholes_v2_put() {
        let result = black_scholes_v2(OptionType::Put, 100.0, 100.0, 1.0, 0.05, 0.2);
        assert!(result.is_ok());
        let value = result.unwrap();
        assert!((value - 5.57352).abs() < 1e-6); // Replace with actual expected value
    }

    #[test]
    fn test_black_scholes_v2_invalid_s() {
        let result = black_scholes_v2(OptionType::Call, -100.0, 100.0, 1.0, 0.05, 0.2);
        assert!(result.is_err());
        assert_eq!(result.err().unwrap(), "现货价格、行权价格必须大于0，剩余到期时间和波动率不能为负数.");
    }

    #[test]
    fn test_black_scholes_v2_invalid_x() {
        let result = black_scholes_v2(OptionType::Call, 100.0, 0.0, 1.0, 0.05, 0.2);
        assert!(result.is_err());
        assert_eq!(result.err().unwrap(), "现货价格、行权价格必须大于0，剩余到期时间和波动率不能为负数.");
    }

    #[test]
    fn test_black_scholes_v2_invalid_t() {
        let result = black_scholes_v2(OptionType::Call, 100.0, 100.0, -1.0, 0.05, 0.2);
        assert!(result.is_err());
        assert_eq!(result.err().unwrap(), "现货价格、行权价格必须大于0，剩余到期时间和波动率不能为负数.");
    }

    #[test]
    fn test_black_scholes_v2_invalid_v() {
        let result = black_scholes_v2(OptionType::Call, 100.0, 100.0, 1.0, 0.05, -0.1);
        assert!(result.is_err());
        assert_eq!(result.err().unwrap(), "现货价格、行权价格必须大于0，剩余到期时间和波动率不能为负数.");
    }

    #[tokio::test]
    async fn test_calc_price() {
        let cfg = "config/blackscholes.yaml";
        loggings::log_init(cfg);
        let setting = Settings::new().unwrap();

        let db = DbConnection::new(&setting.database.stock_uri).await;

        let opt = AkaCacheOption::default();
        let aka_client = AkaClient::init(setting.servers.akacenterserver.to_string(), &opt).await;
        let hq_client = Arc::new(RwLock::new(HqCenterClient::new(&setting.servers.hqcenterserver).await));
        let manager_client = Arc::new(RwLock::new(ManagerClient::new(&setting.servers.managerserver).await));

        let stub = BlackscholesController {
            hq_client,
            aka_client: Arc::new(aka_client),
            db: Arc::new(db),
            manager_client,
        };

        let price = stub
            .calc_price("000034".to_string(), 22.37, 22.37, 0.035, 30, OptionType::Call, VolatilityFlag::On, crate::server::controller::ProfitType::Common)
            .await;
        println!("{price:?}")
    }

    #[test]
    fn test_black_scholes() {
        let numbers = vec![
            -0.016115702,
            -0.01865369,
            -0.1,
            -0.039607431,
            0.004577465,
            0.000352237,
            -0.000352113,
            -0.044414536,
            0.016068376,
            -0.013490725,
            0.002027712,
            0.01509434,
            0.013913043,
            -0.002428869,
            -0.001731902,
            -0.025649679,
            0.011953552,
            -0.02562396,
            -0.008250825,
            -0.000659631,
            -0.015584416,
            0.013157895,
            0.048275862,
            0.005896635,
        ];

        let volatility = volatility(&numbers);

        println!("volatility: {}", volatility);

        println!("{}", black_scholes(OptionType::Put, 23.0, 23.0, 30.0 / 360.00, 0.035, volatility))
    }

    #[test]
    fn test_cnd_happy_path() {
        assert!((cnd(0.0) - 0.5).abs() < 1e-6);
        assert!((cnd(1.0) - 0.8413447460685429).abs() < 1e-6);
        assert!((cnd(-1.0) - 0.1586552539314571).abs() < 1e-6);
        assert!((cnd(2.0) - 0.9772498680518208).abs() < 1e-6);
        assert!((cnd(-2.0) - 0.02275013194817921).abs() < 1e-6);
    }

    #[test]
    fn test_cnd_edge_cases() {
        assert!((cnd(f64::INFINITY) - 1.0).abs() < 1e-6);
        assert!((cnd(f64::NEG_INFINITY) - 0.0).abs() < 1e-6);
    }

    #[test]
    fn test_cnd_negative_values() {
        assert!((cnd(-0.5) - 0.3085375387259869).abs() < 1e-6);
        assert!((cnd(-3.0) - 0.001349898031630103).abs() < 1e-6);
    }

    #[tokio::test]
    async fn test_compute_volatility_happy_path() {
        let stock_code = "000001".to_string();
        let market_code = "XSHE".to_string();
        let days = 30;
        let hq_client = Arc::new(RwLock::new(HqCenterClient::new(&"http://52.131.220.224:8416".to_string()).await));
        let db = Arc::new(DbConnection::new("mysql://companytest:<EMAIL>:13301/phoenix_stock").await);

        let result = compute_volatility(stock_code, market_code, days, hq_client.clone(), &db).await;

        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_compute_volatility_no_prices() {
        let stock_code = "000001".to_string();
        let market_code = "XSHE".to_string();
        let days = 30;
        // let profit_type = ProfitType::Common;
        let hq_client = Arc::new(RwLock::new(HqCenterClient::new(&"http://52.131.220.224:8416".to_string()).await));
        let db = Arc::new(DbConnection::new("mysql://companytest:<EMAIL>:13301/phoenix_stock").await);

        let result = compute_volatility(stock_code, market_code, days, hq_client.clone(), &db).await;

        assert!(!result.is_err());
        // assert_eq!(result.unwrap_err().code(), StatusCode::Unknown);
    }

    #[tokio::test]
    async fn test_compute_volatility_invalid_days() {
        let stock_code = "000001".to_string();
        let market_code = "XSHE".to_string();
        let days = -1;
        // let profit_type = ProfitType::Common;
        let hq_client = Arc::new(RwLock::new(HqCenterClient::new(&"http://52.131.220.224:8416".to_string()).await));
        let db = Arc::new(DbConnection::new("mysql://companytest:<EMAIL>:13301/phoenix_stock").await);

        let result = compute_volatility(stock_code, market_code, days, hq_client.clone(), &db).await;

        assert!(result.is_ok()); // assuming the implementation handles negative days correctly
    }
}
