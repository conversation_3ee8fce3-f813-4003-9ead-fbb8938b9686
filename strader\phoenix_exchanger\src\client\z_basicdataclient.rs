use akaclient::protofiles::phoenixakacenter::{
    phoenix_aka_center_client::PhoenixAkaCenterClient, AccountInfoReq, AccountInfoResp, ChannelHoldLimitReq, ChannelHoldLimitResp, ChannelInfoReq, ChannelInfoResp, ExchangeRateReq, ExchangeRateResp, MarketCloseInfoReq,
    MarketCloseInfoResp, MarketInfoReq, MarketInfoResp, SpecialAccountInfoReq, SpecialAccountInfoResp, SpecialAccountType, StockChannelReq, StockChannelResp, StockInfoReq, StockInfoResp,
};
use anyhow::{anyhow, Result};
use tracing::*;

use tonic::transport::Channel;
#[derive(Clone)]
pub struct BasicdataClient {
    pub client: PhoenixAkaCenterClient<Channel>,
    pub url: String,
}

impl BasicdataClient {
    pub async fn new(url: &String) -> Self {
        // let client = PhoenixAkaCenterClient::connect(url.to_owned()).await.expect("基础数据服务连接失败......");
        // BasicdataClient { client, url: url.to_owned() }
        loop {
            match PhoenixAkaCenterClient::connect(url.clone()).await {
                Ok(client) => return Self { client: client, url: url.to_owned() },
                Err(err) => {
                    error!("connect to akacenter failed: {:?}", err);
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }
    pub async fn query_stock_info(&self, req: &StockInfoReq) -> Result<StockInfoResp> {
        let mut client = self.client.clone();
        match client.query_stock_info(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_stock_info(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_channel_info(&self, req: &ChannelInfoReq) -> Result<ChannelInfoResp> {
        let mut client = self.client.clone();
        match client.query_channel_info(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_channel_info(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_channel_hold_limit(&self, req: ChannelHoldLimitReq) -> Result<ChannelHoldLimitResp> {
        let mut client = self.client.clone();
        match client.query_channel_hold_limit(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_channel_hold_limit(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_stock_channel(&self, req: StockChannelReq) -> Result<StockChannelResp> {
        let mut client = self.client.clone();
        match client.query_stock_channel(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_stock_channel(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_special_account(&self) -> Result<SpecialAccountInfoResp> {
        let mut client = self.client.clone();
        let mut req = SpecialAccountInfoReq::default();
        req.account_type = SpecialAccountType::Counterparty as i32;
        match client.query_special_account(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_special_account(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_market_info(&self, req: &MarketInfoReq) -> Result<MarketInfoResp> {
        let mut client = self.client.clone();
        match client.query_market_info(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_market_info(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_market_close_info(&self, req: &MarketCloseInfoReq) -> Result<MarketCloseInfoResp> {
        let mut client = self.client.clone();
        match client.query_market_close_info(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_market_close_info(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_exchange_rate(&self, req: &ExchangeRateReq) -> Result<ExchangeRateResp> {
        let mut client = self.client.clone();
        match client.query_exchange_rate(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_exchange_rate(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
    pub async fn query_account_info(&self, req: &AccountInfoReq) -> Result<AccountInfoResp> {
        let mut client = self.client.clone();
        match client.query_account_info(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = PhoenixAkaCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_account_info(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
}
