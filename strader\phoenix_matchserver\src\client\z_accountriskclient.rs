use anyhow::{anyhow, Result};
use protoes::phoenixaccountriskcenter::account_risk_center_client::AccountRiskCenterClient;
use protoes::phoenixaccountriskcenter::{UserPositionReq, UserPositionResp};
use tracing::*;

use common::logclient::LogClient;
use tonic::transport::Channel;

#[derive(Clone)]
pub struct AccountRiskClient {
    pub client: AccountRiskCenterClient<Channel>,
    pub url: String,
}
impl AccountRiskClient {
    pub async fn new(url: &String) -> Self {
        loop {
            match AccountRiskCenterClient::connect(url.clone()).await {
                Ok(accountclient) => {
                    return Self {
                        client: accountclient,
                        url: url.to_owned(),
                    }
                }
                Err(err) => {
                    error!("connect to accountriskcenter failed: {:?}", err);
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("accountriskcenter connect faild :{}", err.to_string())).await;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn query_user_positions(&mut self, req: &UserPositionReq) -> Result<UserPositionResp> {
        // let mut client = self.client.clone();
        info!("call query_user_positions");
        let mut client = self.client.clone();
        match client.query_user_positions(req.to_owned()).await {
            Ok(val) => {
                // info!("val:{:#?}",val);
                Ok(val.into_inner())
            }
            Err(status) => {
                error!("basicdata server status: {:?}", status);
                info!("try connect basicdata server !");
                let ret = AccountRiskCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connectb basicdata server  err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_user_positions(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                return Err(anyhow!("basicdata server status: {:?}", status));
            }
        }
    }
}
