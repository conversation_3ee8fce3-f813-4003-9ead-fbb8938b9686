// use crate::config::settings::Settings;
use anyhow::{anyhow, Result};
// use std::process;
// use tokio::sync::broadcast::Sender;
use tonic::transport::Channel;
// use tokio_stream::StreamExt;
// use tokio::sync::mpsc::{Sender, Receiver};

use protoes::phoenixordercenter::ReplenishOrderReq;
use protoes::phoenixordercenter::{order_center_service_client::OrderCenterServiceClient, OrderReq};
// use crate::protofiles::phoenixordermsg::RouterMsg;
use tracing::*;

#[derive(Clone)]
pub struct OrderCenterClient {
    pub client: OrderCenterServiceClient<Channel>,
    uri: String,
}

impl OrderCenterClient {
    pub async fn new(uri: &String) -> Self {
        // let client = OrderCenterServiceClient::connect(uri.to_owned()).await.expect("订单中心连接失败...");
        // OrderCenterClient {
        //     client,
        //     uri: uri.to_owned(),
        // }
        loop {
            match OrderCenterServiceClient::connect(uri.clone()).await {
                Ok(client) => return Self { client: client, uri: uri.to_owned() },
                Err(err) => {
                    error!("connect to OrderCenterClient failed: {:?}", err);
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }
    pub async fn place_order(&self, req: &OrderReq) -> Result<()> {
        let mut client = self.client.clone();
        info!("推送到订单中心: {:?}", &req);
        match client.place_order(req.to_owned()).await {
            Ok(val) => {
                info!("place order success:{:?}", &val);
                // if let Some(price_info) = val.into_inner().data {
                //     Ok()
                // } else {
                //     return Err(anyhow!("未找到{}最新价信息", &stock_code));
                // }
                return Ok(());
            }
            Err(status) => {
                error!("order center status: {:?}", status);
                info!("try connect order center!");
                let ret = OrderCenterServiceClient::connect(self.uri.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connect order center err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.place_order(req.to_owned()).await {
                    info!("place order success:{:?}", &val);
                    // if let Some(price_info) = val.into_inner().data {
                    //     return Ok(price_info)
                    // } else {
                    //     return Err(anyhow!("未找到{}最新价信息", &stock_code));
                    // }
                    return Ok(());
                }
                return Err(anyhow!("order center status: {:?}", status));
            }
        }
    }
    pub async fn replenishment_order(&self, req: &ReplenishOrderReq) -> Result<()> {
        let mut client = self.client.clone();
        info!("推送到订单中心: {:?}", &req);
        match client.replenishment_order(req.to_owned()).await {
            Ok(val) => {
                info!("replenishment order success:{:?}", &val);
                // if let Some(price_info) = val.into_inner().data {
                //     Ok()
                // } else {
                //     return Err(anyhow!("未找到{}最新价信息", &stock_code));
                // }
                return Ok(());
            }
            Err(status) => {
                error!("order center status: {:?}", status);
                info!("try connect order center!");
                let ret = OrderCenterServiceClient::connect(self.uri.to_owned()).await;
                if ret.as_ref().is_err() {
                    return Err(anyhow!("try connect order center err: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.replenishment_order(req.to_owned()).await {
                    info!("replenishment order success:{:?}", &val);
                    return Ok(());
                }
                return Err(anyhow!("order center status: {:?}", status));
            }
        }
    }
}
