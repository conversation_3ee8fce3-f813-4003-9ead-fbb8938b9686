# Phoenix Order Center Performance Optimization Guide

## Overview

This guide provides comprehensive performance optimization strategies for the phoenix_ordercenter service, focusing on reducing latency, improving throughput, and enhancing scalability.

## 1. Database Connection Pool Optimization

### Current State Analysis

The current database client configuration has commented out connection pool settings:

```rust
// .max_connections(64)
// .min_connections(16)
// .connect_timeout(Duration::from_secs(10))
// .max_lifetime(Duration::from_secs(10))
```

### Recommendations

#### A. Optimize Database Connection Pool

```rust
// In src/client/dbclient.rs
impl DbClient {
    pub async fn new(uri: &String) -> Self {
        let mut opt = ConnectOptions::new(uri.to_owned());
        opt
            .max_connections(50)      // Increase max connections
            .min_connections(10)      // Set minimum pool size
            .connect_timeout(Duration::from_secs(30))
            .acquire_timeout(Duration::from_secs(10))
            .idle_timeout(Duration::from_secs(600))
            .max_lifetime(Duration::from_secs(3600))
            .sqlx_logging(false);
    }
}
```

#### B. Database Query Optimization

- Implement prepared statements for frequently used queries
- Add database indexes for order lookup operations
- Use batch operations for bulk inserts

## 2. Redis Connection Pool Enhancement

### Current Configuration

```toml
# config/ordercenter.toml
[redis]
# Current: max_size = 16, min_idle = 8
```

### Optimizations

#### A. Increase Redis Pool Size

```rust
// In src/server/server.rs
let mut rds_cfg = RedisConfig::default();
rds_cfg.urls = settings.redis.uri.to_owned();
rds_cfg.prefix = settings.redis.prefix.to_owned();
rds_cfg.max_size = 32;     // Increase from 16
rds_cfg.min_idle = 16;     // Increase from 8
rds_cfg.connection_timeout = Duration::from_secs(10);
rds_cfg.idle_timeout = Duration::from_secs(300);
```

#### B. Redis Cache Strategy Optimization

```rust
// Implement cache-aside pattern with TTL optimization
const CACHE_TTL_ORDER: i64 = 300;      // 5 minutes for active orders
const CACHE_TTL_HISTORY: i64 = 3600;   // 1 hour for order history
const CACHE_TTL_REFERENCE: i64 = 86400; // 24 hours for reference data
```

## 3. Eliminate RwLock Contention

### Current Issues

Multiple RwLock contentions identified:

- `uidgen: Arc<RwLock<UidgenService>>`
- `assetscenter_client: Arc<RwLock<AssetsCenterClient>>`

### Solutions

#### A. Replace UID Generation with Lock-Free Solution

```rust
// Create lock-free UID generator using atomic operations
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{SystemTime, UNIX_EPOCH};

pub struct AtomicUidGenerator {
    machine_id: u64,
    node_id: u64,
    sequence: AtomicU64,
}

impl AtomicUidGenerator {
    pub fn new(machine_id: u64, node_id: u64) -> Self {
        Self {
            machine_id,
            node_id,
            sequence: AtomicU64::new(0),
        }
    }
    
    pub fn get_uid(&self) -> u64 {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        let seq = self.sequence.fetch_add(1, Ordering::Relaxed) & 0xFFF;
        
        (timestamp << 22) | (self.machine_id << 17) | (self.node_id << 12) | seq
    }
}
```

#### B. Connection Pool for External Services

```rust
// Replace RwLock with connection pool
pub struct AssetsCenterPool {
    pool: Pool<AssetsCenterClient>,
}

impl AssetsCenterPool {
    pub async fn new(server_uri: &str, pool_size: usize) -> Self {
        let mut clients = Vec::with_capacity(pool_size);
        for _ in 0..pool_size {
            let client = AssetsCenterClient::new(server_uri).await;
            clients.push(client);
        }
        
        Self {
            pool: Pool::from(clients),
        }
    }
    
    pub async fn get_client(&self) -> AssetsCenterClient {
        self.pool.get().await
    }
}
```

## 4. Async/Await Optimization

### Current Issues1

Sequential await calls create unnecessary latency:

```rust
// Current sequential execution
if let Err(err) = self.aka_stock_trade_info(&mut order_detail).await {
    // error handling
}
let ret = self.risk_check(&mut order_detail).await;
if let Err(err) = self.calc_fee_info(&mut order_detail).await {
    // error handling
}
```

### Solution: Parallel Execution

```rust
// Optimize with parallel execution
pub async fn place_order_optimized(&self, req_order: &OrderReq) -> Result<OrderResp> {
    let now = std::time::Instant::now();
    let mut order_detail = OrderDetail::place_order(&req_order).await;
    
    // Generate IDs without lock contention
    let order_id = self.uid_generator.get_uid();
    let msg_id = self.uid_generator.get_uid();
    order_detail.order_id = order_id;
    order_detail.msg_id = msg_id;

    // Execute independent operations in parallel
    let (stock_info_result, risk_check_result) = tokio::join!(
        self.aka_stock_trade_info_async(&order_detail),
        self.risk_check_async(&order_detail)
    );
    
    // Handle results
    let stock_info = stock_info_result?;
    let risk_ret = risk_check_result?;
    
    // Calculate fees after getting stock info
    self.calc_fee_info_async(&mut order_detail, &stock_info).await?;
    
    // Continue with order generation...
}
```

## 5. Channel Optimization

### Current Channel Sizes

```rust
let (tx_persist, mut rx_persist) = mpsc::channel::<PersistData>(1024);
let (tx_confirm, mut rx_confirm) = mpsc::channel::<ExecMsg>(2048);
let (tx_filled, mut rx_filled) = mpsc::channel::<ExecMsg>(2048);
```

### Optimizations Detail

#### A. Increase Channel Sizes for High-Throughput Operations

```rust
let (tx_persist, mut rx_persist) = mpsc::channel::<PersistData>(8192);
let (tx_confirm, mut rx_confirm) = mpsc::channel::<ExecMsg>(16384);
let (tx_filled, mut rx_filled) = mpsc::channel::<ExecMsg>(16384);
let (tx_canceled, mut rx_canceled) = mpsc::channel::<ExecMsg>(4096);
```

#### B. Implement Batch Processing

```rust
// Batch persist operations
pub async fn batch_persist_handler(
    &self,
    mut rx_persist: mpsc::Receiver<PersistData>,
    batch_size: usize,
    batch_timeout: Duration,
) {
    let mut batch = Vec::with_capacity(batch_size);
    let mut timeout = tokio::time::interval(batch_timeout);
    
    loop {
        tokio::select! {
            Some(data) = rx_persist.recv() => {
                batch.push(data);
                if batch.len() >= batch_size {
                    self.flush_batch(&mut batch).await;
                }
            }
            _ = timeout.tick() => {
                if !batch.is_empty() {
                    self.flush_batch(&mut batch).await;
                }
            }
        }
    }
}
```

## 6. Caching Strategy Enhancement

### A. Multi-Level Caching

```rust
pub struct OrderCacheManager {
    l1_cache: DashMap<u64, CachedOrder>,     // In-memory cache
    l2_cache: Arc<RedisClient>,              // Redis cache
    l3_cache: Arc<DbClient>,                 // Database
}

impl OrderCacheManager {
    pub async fn get_order(&self, order_id: u64) -> Result<Order> {
        // L1 Cache check
        if let Some(cached) = self.l1_cache.get(&order_id) {
            return Ok(cached.order.clone());
        }
        
        // L2 Cache check (Redis)
        if let Ok(order) = self.get_from_redis(order_id).await {
            self.l1_cache.insert(order_id, CachedOrder::new(order.clone()));
            return Ok(order);
        }
        
        // L3 Cache (Database)
        let order = self.get_from_database(order_id).await?;
        self.update_all_caches(order_id, &order).await;
        Ok(order)
    }
}
```

### B. Precomputed Cache Warm-up

```rust
pub async fn warm_up_cache(&self) {
    // Pre-load frequently accessed data
    let active_orders = self.get_active_orders_from_db().await;
    for order in active_orders {
        self.cache_manager.preload_order(order).await;
    }
    
    // Pre-load reference data
    self.cache_manager.preload_fee_settings().await;
    self.cache_manager.preload_stock_info().await;
}
```

## 7. Memory Optimization

### A. Object Pooling

```rust
use object_pool::Pool;

pub struct OrderDetailPool {
    pool: Pool<OrderDetail>,
}

impl OrderDetailPool {
    pub fn new() -> Self {
        Self {
            pool: Pool::new(100, || OrderDetail::default()),
        }
    }
    
    pub fn get(&self) -> OrderDetail {
        self.pool.try_pull().unwrap_or_else(|| OrderDetail::default())
    }
    
    pub fn return_object(&self, mut obj: OrderDetail) {
        obj.reset();
        let _ = self.pool.attach(obj);
    }
}
```

### B. Reduce Clone Operations

```rust
// Instead of cloning large objects, use references where possible
pub async fn process_order_efficient(&self, order: &OrderReq) -> Result<OrderResp> {
    // Use references instead of cloning
    let order_detail = self.create_order_detail_from_ref(order);
    
    // Pass references to avoid unnecessary clones
    self.validate_order_ref(&order_detail).await?;
    self.process_risk_check_ref(&order_detail).await?;
    
    Ok(self.create_response(&order_detail))
}
```

## 8. Error Handling Optimization

### A. Fast Path for Common Cases

```rust
pub async fn optimized_error_handling(&self, order: &OrderReq) -> Result<OrderResp> {
    // Fast validation checks first
    if !self.quick_validate(order) {
        return Err(anyhow!("Invalid order"));
    }
    
    // Pre-allocated response for success case
    let mut response = OrderResp {
        msg_id: 0,
        order_id: 0,
        error_code: 0,
        error_msg: String::with_capacity(0), // Avoid allocation for success
    };
    
    // Process order...
    Ok(response)
}
```

## 9. Monitoring and Metrics

### A. Performance Monitoring

```rust
use prometheus::{Counter, Histogram, Gauge};

pub struct OrderMetrics {
    pub order_processing_duration: Histogram,
    pub active_orders_count: Gauge,
    pub orders_processed_total: Counter,
    pub cache_hit_ratio: Gauge,
}

impl OrderMetrics {
    pub fn new() -> Self {
        Self {
            order_processing_duration: Histogram::new(
                "order_processing_duration_seconds",
                "Time spent processing orders"
            ).unwrap(),
            active_orders_count: Gauge::new(
                "active_orders_count",
                "Number of active orders"
            ).unwrap(),
            orders_processed_total: Counter::new(
                "orders_processed_total",
                "Total number of orders processed"
            ).unwrap(),
            cache_hit_ratio: Gauge::new(
                "cache_hit_ratio",
                "Cache hit ratio"
            ).unwrap(),
        }
    }
}
```

## 10. Configuration Tuning

### A. Runtime Configuration

```toml
# config/ordercenter.toml - Performance section
[performance]
max_concurrent_orders = 10000
batch_size_persist = 100
batch_timeout_ms = 50
cache_ttl_seconds = 300
worker_threads = 8
max_blocking_threads = 32

[database]
max_connections = 50
min_connections = 10
connection_timeout_sec = 30
idle_timeout_sec = 600
query_timeout_sec = 10

[redis]
max_size = 32
min_idle = 16
connection_timeout_sec = 10
idle_timeout_sec = 300
pipeline_size = 100
```

## Implementation Priority

1. **High Priority (Immediate Impact)**
   - Fix database connection pool settings
   - Replace RwLock with atomic operations for UID generation
   - Implement parallel execution for independent operations

2. **Medium Priority (Significant Impact)**
   - Enhance Redis connection pool
   - Implement batch processing for persist operations
   - Add multi-level caching

3. **Low Priority (Optimization)**
   - Object pooling
   - Memory optimization
   - Advanced monitoring

## Expected Performance Improvements

- **Latency Reduction**: 40-60% reduction in average order processing time
- **Throughput Increase**: 3-5x increase in concurrent order handling capacity
- **Memory Usage**: 20-30% reduction in memory footprint
- **CPU Efficiency**: 25-40% improvement in CPU utilization

## Testing and Validation

1. **Load Testing**: Use tools like wrk or custom Rust benchmarks
2. **Profiling**: Use perf, flamegraph, or cargo-profiler
3. **Monitoring**: Implement metrics collection and alerting
4. **A/B Testing**: Deploy changes incrementally with performance comparison

## Conclusion

These optimizations will significantly improve the performance of phoenix_ordercenter by addressing the key bottlenecks:

- Lock contention
- Sequential processing
- Inefficient caching
- Suboptimal connection pooling

The improvements should be implemented incrementally, with thorough testing at each stage to ensure system stability and measure performance gains.
