# Optimized Phoenix Order Center Configuration
# This configuration file contains performance-tuned settings for high-throughput trading

[application]
ip = "0.0.0.0"
port = 8405
machineid = 1
nodeid = 8405
# Performance settings
max_concurrent_orders = 10000
worker_threads = 8
max_blocking_threads = 32

[mysql]
uri = "mysql://companytest:<EMAIL>:13301/phoenix_stock"
# Optimized connection pool settings
max_connections = 50
min_connections = 10
connection_timeout_sec = 30
acquire_timeout_sec = 10
idle_timeout_sec = 600
max_lifetime_sec = 3600
query_timeout_sec = 10

[redis]
uri = "****************************************************************************************************/,****************************************************************************************************/,****************************************************************************************************/"
prefix = "phoenix_order_"
# Optimized Redis settings
max_size = 32
min_idle = 16
connection_timeout_sec = 10
idle_timeout_sec = 300
pipeline_size = 100

[performance]
# Batch processing settings
batch_size_persist = 100
batch_timeout_ms = 50
batch_size_orders = 10

# Cache settings
cache_ttl_order_active = 300     # 5 minutes for active orders
cache_ttl_order_history = 3600   # 1 hour for order history
cache_ttl_reference_data = 86400 # 24 hours for reference data
cache_ttl_stock_info = 300       # 5 minutes for stock info
cache_ttl_fee_settings = 1800    # 30 minutes for fee settings

# Channel buffer sizes
channel_order_buffer = 16384
channel_persist_buffer = 8192
channel_exec_buffer = 16384
channel_cancel_buffer = 4096
channel_notification_buffer = 1024

# Timeout settings
risk_check_timeout_sec = 5
fee_calculation_timeout_sec = 3
asset_processing_timeout_sec = 10
order_placement_timeout_sec = 5

# Connection pool sizes
assetscenter_pool_size = 10
external_service_pool_size = 8

[system]
cachelong = 60
oppaccount = 200844

[mq]
amqpaddr = "amqp://pp:<EMAIL>:5672/%2f"
exchanger = "notification_center"
queue = "phoenix_notification_client_queue"
routingkey = "notification.order.status.*,notification.order.exec.*,notification.order.info.*"
# Performance settings
prefetch_count = 100
message_ttl = 300000 # 5 minutes in milliseconds

[grpcserver]
riskcenter = "http://**************:8407"
orderrouter = "http://**************:7406"
assetscenter = "http://**************:8403"
akacenter = "http://**************:8402"
accountrisk = "http://**************:8404"

# gRPC performance settings
[grpc_performance]
max_receive_message_size = ******** # 16MB
max_send_message_size = ********    # 16MB
keepalive_time_sec = 30
keepalive_timeout_sec = 5
keepalive_without_calls = true
max_connection_idle_sec = 300
max_connection_age_sec = 1800

[notification]
notification_exchanger = "notification_center"
vhost = "/phoenix"
ordercenter_routing_key = "notification.order.status.*,notification.order.exec.*,notification.order.info.*"

[logging]
# Performance-oriented logging settings
level = "info"
async_logging = true
buffer_size = 8192
flush_interval_ms = 1000

[monitoring]
# Metrics collection settings
enable_metrics = true
metrics_port = 9090
health_check_interval_sec = 30
performance_sampling_rate = 0.1 # Sample 10% of requests for detailed metrics
