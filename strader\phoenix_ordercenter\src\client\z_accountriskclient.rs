use crate::server::service::push_log;
use anyhow::Result;
use protoes::phoenixaccountriskcenter::{account_risk_center_client::AccountRiskCenterClient, MarginRatioReq, MarginRatioResp, UserAssetsReq};
use tonic::transport::Channel;
use tracing::{error, info};
#[derive(Clone)]
#[allow(unused)]
pub struct AccountRiskClient {
    pub client: AccountRiskCenterClient<Channel>,
    _uri: String,
}

impl AccountRiskClient {
    pub async fn new(uri: &String) -> Self {
        loop {
            match AccountRiskCenterClient::connect(uri.to_owned()).await {
                Ok(client) => {
                    info!("分帐户风控中心连接成功....");
                    return Self { client, _uri: uri.to_owned() };
                }
                Err(err) => {
                    error!("connect error: {:?}", err);
                    push_log(format!("connect to AccountRiskCenter failed: {:?}", uri).as_str()).await;
                    tokio::time::sleep(std::time::Duration::from_secs(3)).await;
                    continue;
                }
            }
        }
    }

    pub async fn query_margin_ratio(&mut self, user_id: i64, stock_id: i64) -> Result<MarginRatioResp> {
        info!("query margin ratio user_id:{}, stock_id:{}", user_id, stock_id);

        let request = MarginRatioReq { user_id, stock_id };
        match self.client.query_margin_ratio(request.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("{:?}", status);
                push_log(format!("account risk center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!("account risk center error"));
            }
        }
    }

    pub async fn query_user_currency(&mut self, user_id: i64, unit_id: i64) -> Result<String> {
        info!("query user currency user_id:{}, unit_id:{}", user_id, unit_id);
        let mut request = UserAssetsReq::default();
        request.user_id.push(user_id);
        request.unit_id.push(unit_id);
        match self.client.query_user_assets(request.to_owned()).await {
            Ok(val) => {
                let user_assets = val.into_inner().assets;
                if user_assets.is_empty() {
                    error!("account risk user assets is empty");
                    return Err(anyhow!("account risk user assets is empty"));
                }
                info!("account risk user assets: {:?}", user_assets);

                let currency = if let Some(assets) = user_assets.iter().find(|x| x.unit_id == unit_id) {
                    assets.currency.to_owned()
                } else {
                    "".to_string()
                };
                if currency.is_empty() {
                    error!("没有找到用户资产: {}, {}", user_id, unit_id);
                    return Err(anyhow!("account risk user assets is empty"));
                }

                return Ok(currency);
            }
            Err(status) => {
                error!("{:?}", status);
                push_log(format!("account risk center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!("account risk center error"));
            }
        }
    }

    // pub async fn get_conn(&self) -> AccountRiskCenterClient<Channel>{
    //     self.client.clone()
    // }
}
