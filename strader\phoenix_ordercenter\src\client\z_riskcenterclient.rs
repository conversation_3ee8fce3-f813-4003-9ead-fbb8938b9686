use crate::odcommon::common::OrderDetail;
use crate::server::service::push_log;
use anyhow::Result;
use protoes::phoenixriskcenter::{phoenix_riskcenter_client::PhoenixRiskcenterClient, PhoenixRiskCheckInfo, PhoenixRiskCheckRequest, PhoenixRiskCheckResponse};
use rust_decimal::prelude::ToPrimitive;
use tonic::transport::Channel;
use tracing::{error, info};
#[derive(Clone)]
#[allow(dead_code)]
pub struct RiskCenterClient {
    pub client: PhoenixRiskcenterClient<Channel>,
    pub _uri: String,
}

impl RiskCenterClient {
    pub async fn new(uri: &String) -> Self {
        loop {
            match PhoenixRiskcenterClient::connect(uri.to_owned()).await {
                Ok(client) => {
                    info!("风控中心连接成功....");
                    return Self { client, _uri: uri.to_owned() };
                }
                Err(err) => {
                    error!("connect error: {:?}", err);
                    push_log(format!("connect to riskcenter failed: {:?}", uri).as_str()).await;
                    tokio::time::sleep(std::time::Duration::from_secs(3)).await;
                    continue;
                }
            }
        }
    }

    pub async fn phoenix_risk_check(&mut self, order_detail: &OrderDetail) -> Result<PhoenixRiskCheckResponse> {
        let request = crate::odcommon::common::OrderDetail::convert_to_riskinfo(order_detail).await;
        match self.client.phoenix_risk_check(request.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("{:?}", status);
                push_log(format!("risk center err code: {:?} message: {:?}", status.code(), status.message()).as_str()).await;
                return Err(anyhow!("risk center err"));
            }
        }
    }

    // pub async fn convert_to_riskinfo(order_detail: &OrderDetail) -> PhoenixRiskCheckRequest {
    //     let mut req = PhoenixRiskCheckRequest::default();
    //     let mut queryinfo = PhoenixRiskCheckInfo::default();
    //     queryinfo.unit_id = order_detail.unit_id; //用户unit_id,
    //     queryinfo.stock_id = order_detail.stock_id; //股票ID,
    //     queryinfo.order_price = order_detail.order_price.to_f64().unwrap_or_default(); //价格,
    //     queryinfo.order_amount = order_detail.order_amount; //数量，
    //     queryinfo.order_direction = order_detail.order_direction; //方向（ 1:买 2：卖）
    //                                                               // queryinfo.channel_type = 6;//通道类型：1：外盘 2：内盘
    //     queryinfo.order_type = order_detail.order_type; //委托类型 1:app下单  2:跟单  3:风控止盈止损平仓单,4:风控总资产预警平仓单 5:pc客户端单 6:结算平仓单 7:管理端强平仓单,8:app清仓,9:pc清仓,10,管理员平仓,11,合约到期日强平
    //                                                     // queryinfo.order_type  = 8;//通道
    //     queryinfo.market_id = order_detail.exchange_id as i64; //市场ID
    //     queryinfo.trade_mode = order_detail.trade_mode; // 1:USER(用户直连) 2:AGENT(代理托管)
    //     queryinfo.agent_account = order_detail.agent_account; // 代理账户
    //     queryinfo.user_id = order_detail.user_id; //用户user_id
    //     req.queryinfo = Some(queryinfo);
    //     info!("request risk msg: {:?}", &req);
    //     req
    // }

    // pub async fn get_conn(&self) -> PhoenixRiskcenterClient<Channel>{
    //     self.client.clone()
    // }
}
