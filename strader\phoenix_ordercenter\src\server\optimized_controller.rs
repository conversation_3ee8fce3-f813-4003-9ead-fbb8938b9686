// Optimized controller implementation focusing on performance improvements
use crate::client::{AssetsCenterClient, DbClient};
use crate::server::service::{assetchange, convertdata, notificationmsg, queryinfo};
use crate::{
    config::settings::Settings,
    server::service::{ordercache::<PERSON><PERSON><PERSON><PERSON>, tool},
};
use accountriskcenterclient::AccountRiskClient;
use akaclient::{
    akaclient::AkaClient,
    calcfee::{FeeDetail, FeeField},
};
use anyhow::Result;
use common::{
    constant::{ChannelType, OrderDirection, OrderStatus},
    redisclient::redispool::RedisClient,
};
use messagecenter::notificationclient::NotificationClient;
use protoes::phoenixordercenter::{CancelReq, OrderReq, OrderResp, ReplenishOrderReq, Riskinfo};
use protoes::phoenixordermsg::{ExecMsg, ExecType, RouterMsg};
use protoes::phoenixriskcenter::PhoenixRiskCheckInfo;
use riskcenterclient::RiskCenterClient;
use rust_decimal::prelude::*;
use rust_decimal_macros::dec;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::{
    ops::AddAssign,
    sync::atomic::{AtomicU64, Ordering},
    sync::Arc,
};
use tokio::sync::{broadcast, mpsc};
use tracing::{error, info, instrument};

use crate::dataservice::entities::{prelude::PhoenixOrdCancel, prelude::PhoenixOrdPendSettle, prelude::PhoenixOrdStockdeal, prelude::PhoenixOrdStockorder, prelude::PhoenixOrdSuborder};
use crate::odcommon::common::OrderDetail;

// Lock-free UID Generator for better performance
pub struct AtomicUidGenerator {
    machine_id: u64,
    node_id: u64,
    sequence: AtomicU64,
}

impl AtomicUidGenerator {
    pub fn new(machine_id: u64, node_id: u64) -> Self {
        Self {
            machine_id,
            node_id,
            sequence: AtomicU64::new(0),
        }
    }

    pub fn get_uid(&self) -> u64 {
        let timestamp = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
        let seq = self.sequence.fetch_add(1, Ordering::Relaxed) & 0xFFF;

        (timestamp << 22) | (self.machine_id << 17) | (self.node_id << 12) | seq
    }
}

// Connection pool for external services to avoid RwLock contention
pub struct AssetsCenterPool {
    pool: crossbeam_queue::SegQueue<AssetsCenterClient>,
    server_uri: String,
    max_size: usize,
}

impl AssetsCenterPool {
    pub async fn new(server_uri: String, pool_size: usize) -> Self {
        let pool = crossbeam_queue::SegQueue::new();

        for _ in 0..pool_size {
            let client = AssetsCenterClient::new(&server_uri).await;
            pool.push(client);
        }

        Self { pool, server_uri, max_size: pool_size }
    }

    pub async fn get_client(&self) -> AssetsCenterClient {
        if let Some(client) = self.pool.pop() {
            client
        } else {
            // Create new client if pool is empty
            AssetsCenterClient::new(&self.server_uri).await
        }
    }

    pub fn return_client(&self, client: AssetsCenterClient) {
        if self.pool.len() < self.max_size {
            self.pool.push(client);
        }
        // Otherwise let it drop
    }
}

#[non_exhaustive]
pub enum PersistData {
    StockDeal(Box<PhoenixOrdStockdeal>),
    PendSettle(Box<PhoenixOrdPendSettle>),
    StockOrder(Box<PhoenixOrdStockorder>),
    SubOrder(Box<Vec<PhoenixOrdSuborder>>),
    OrdCancel(Box<PhoenixOrdCancel>),
    // Add batch operations for better performance
    BatchStockDeals(Vec<PhoenixOrdStockdeal>),
    BatchOrders(Vec<PhoenixOrdStockorder>),
}

#[derive(Clone)]
pub struct OptimizedOrderCenterController {
    pub settings: Arc<Settings>,
    pub tx_persist: mpsc::Sender<PersistData>,
    pub tx_order: broadcast::Sender<RouterMsg>,
    pub tx_filled: mpsc::Sender<ExecMsg>,
    pub tx_cancel: mpsc::Sender<ExecMsg>,
    pub tx_opponent: mpsc::Sender<OrderDetail>,
    pub db_client: Arc<DbClient>,
    pub aka_client: Arc<AkaClient>,
    pub riskcenter_client: RiskCenterClient,
    // Replace RwLock with connection pool
    pub assetscenter_pool: Arc<AssetsCenterPool>,
    pub account_risk_client: AccountRiskClient,
    pub redis_client: Arc<RedisClient>,
    pub mq_client: Arc<NotificationClient>,
    // Replace RwLock with atomic operations
    pub uid_generator: Arc<AtomicUidGenerator>,
}

impl OptimizedOrderCenterController {
    #[instrument(name = "place_order_optimized", skip_all)]
    pub async fn place_order_optimized(&self, req_order: &OrderReq) -> Result<OrderResp> {
        let start_time = Instant::now();
        info!("Processing order: {}", req_order.order_id);

        // Fast path for order validation
        if let Err(msg) = self.quick_validate_order(req_order) {
            return Ok(OrderResp {
                msg_id: 0,
                order_id: req_order.order_id,
                error_code: 1,
                error_msg: msg,
            });
        }

        let mut order_detail = OrderDetail::place_order(req_order).await;

        // Lock-free UID generation
        order_detail.order_id = self.uid_generator.get_uid();
        order_detail.msg_id = self.uid_generator.get_uid();

        let mut resp = OrderResp {
            msg_id: order_detail.msg_id,
            order_id: order_detail.order_id,
            error_code: 0,
            error_msg: "委托成功".to_string(),
        };

        // Parallel execution of independent operations
        let (stock_info_result, risk_check_result) = tokio::join!(self.aka_stock_trade_info_async(&order_detail), self.risk_check_async(&order_detail));

        // Handle stock info result
        if let Err(err) = stock_info_result {
            error!("Stock info error: {:?}", err);
            resp.error_code = 1;
            resp.error_msg = format!("{:?}", err);
            return Ok(resp);
        }
        let mut order_detail = stock_info_result.unwrap();

        // Handle risk check result
        let risk_ret = match risk_check_result {
            Ok(risk) => risk,
            Err(err) => {
                error!("Risk check error: {:?}", err);
                resp.error_code = 1;
                resp.error_msg = err.to_string();
                return Ok(resp);
            }
        };

        info!("Risk check completed in: {:?}", start_time.elapsed());

        // Calculate fees asynchronously
        if let Err(err) = self.calc_fee_info_async(&mut order_detail).await {
            error!("Fee calculation error: {:?}", err);
            resp.error_code = 1;
            resp.error_msg = format!("{}", err);
            return Ok(resp);
        }

        order_detail.pre_fee = order_detail.fee_total;
        order_detail.capital = (Decimal::from(order_detail.order_amount) * order_detail.order_price * order_detail.margin_rate + order_detail.fee_total) * order_detail.rate;

        // Generate order asynchronously
        let order_generation_result = self.generate_order_async(&order_detail, &risk_ret).await;
        let (new_order, new_sub_orders) = match order_generation_result {
            Ok(result) => result,
            Err(err) => {
                error!("Order generation error: {:?}", err);
                resp.error_code = 1;
                resp.error_msg = err.to_string();
                return Ok(resp);
            }
        };

        // Process internal matching asynchronously
        let internal_orders = self.process_internal_orders(&order_detail, &new_sub_orders).await;

        // Parallel execution of asset processing and order placement
        let (asset_result, order_placement_result) = tokio::join!(self.process_assets_async(&order_detail), self.place_order_to_bp_async(&order_detail, &new_sub_orders));

        if let Err(err) = asset_result {
            error!("Asset processing error: {:?}", err);
            resp.error_code = 1;
            resp.error_msg = err.to_string();
            return Ok(resp);
        }

        // Send notifications asynchronously (fire and forget)
        let notification_client = self.mq_client.clone();
        let order_detail_clone = order_detail.clone();
        let new_order_clone = new_order.clone();
        tokio::spawn(async move {
            let _ = notificationmsg::notificationmsg(&order_detail_clone, &new_order_clone, &notification_client).await;
        });

        info!("Order processing completed in: {:?}", start_time.elapsed());
        Ok(resp)
    }

    // Fast validation without external calls
    fn quick_validate_order(&self, order: &OrderReq) -> Result<(), String> {
        if order.order_amount <= 0 {
            return Err("Invalid order amount".to_string());
        }

        if order.order_price <= 0.0 {
            return Err("Invalid order price".to_string());
        }

        if order.stock_id <= 0 {
            return Err("Invalid stock ID".to_string());
        }

        Ok(())
    }

    // Async version of stock trade info with caching
    async fn aka_stock_trade_info_async(&self, order_detail: &OrderDetail) -> Result<OrderDetail> {
        let mut detail = order_detail.clone();

        // Check cache first
        let cache_key = format!("stock_info_{}_{}", detail.stock_id, detail.exchange_id);
        if let Ok(cached_data) = self.redis_client.get_value_by_get(&cache_key).await {
            if !cached_data.is_empty() {
                // Use cached data if available
                info!("Using cached stock info for: {}", detail.stock_id);
                return Ok(detail);
            }
        }

        // Parallel execution of stock info and trade date queries
        let account_risk_client = self.account_risk_client.clone();
        let (stock_info_result, trade_date_result) = tokio::join!(
            queryinfo::get_stock_info(&mut detail, &self.aka_client, &mut account_risk_client),
            queryinfo::query_trade_date(detail.exchange_id as i64, 0, 1, 0, &self.aka_client)
        );

        if let Err(err) = stock_info_result {
            error!("Stock info query failed: {:?}", err);
            return Err(anyhow::anyhow!("Query stock info failed"));
        }

        match trade_date_result {
            Ok(trade_date) => detail.sys_date = trade_date.target_date,
            Err(err) => {
                error!("Trade date query failed: {:?}", err);
                return Err(anyhow::anyhow!("Query trade date failed"));
            }
        }

        // Cache the result for future use
        let cache_data = serde_json::json!({
            "stock_id": detail.stock_id,
            "sys_date": detail.sys_date,
            "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs()
        })
        .to_string();

        let _ = self.redis_client.set_str_value(&cache_key, 300, &cache_data).await; // 5 minutes TTL

        Ok(detail)
    }

    // Async version of risk check with timeout
    async fn risk_check_async(&self, order_detail: &OrderDetail) -> Result<Vec<PhoenixRiskCheckInfo>> {
        let risk_check_req = OrderDetail::convert_to_riskinfo(order_detail).await;

        // Add timeout for risk check
        let risk_check_future = self.riskcenter_client.phoenix_risk_check(&risk_check_req);
        let risk_result = tokio::time::timeout(Duration::from_secs(5), risk_check_future).await;

        match risk_result {
            Ok(Ok(risk_ret)) => {
                if risk_ret.retinfo.is_empty() {
                    error!("Risk check failed: {}", risk_ret.ret_msg);
                    return Err(anyhow::anyhow!(risk_ret.ret_msg));
                }
                Ok(risk_ret.retinfo)
            }
            Ok(Err(err)) => {
                error!("Risk check error: {:?}", err);
                Err(anyhow::anyhow!(err.to_string()))
            }
            Err(_) => {
                error!("Risk check timeout");
                Err(anyhow::anyhow!("Risk check timeout"))
            }
        }
    }

    // Async version of fee calculation
    async fn calc_fee_info_async(&self, detail: &mut OrderDetail) -> Result<()> {
        // Get exchange rate asynchronously
        if let Err(err) = queryinfo::get_aka_rate(detail, &self.aka_client).await {
            error!("Exchange rate query error: {:?}", err);
            return Err(anyhow::anyhow!("Exchange rate query error: {:?}", err));
        }

        let fee_field = FeeField {
            fee_type: detail.fee_type.clone(),
            exchange_id: detail.exchange_id as i64,
            unit_id: detail.unit_id,
            user_id: detail.user_id,
            channel_id: detail.channel_id as i64,
            stock_type: if detail.stock_type != 6 { 1 } else { detail.stock_type },
            amount: detail.order_amount,
            price: detail.order_price,
            currency_no: detail.currency_no.clone(),
            order_direction: detail.order_direction,
            rate: detail.rate,
        };

        // Add timeout for fee calculation
        let fee_calc_future = FeeDetail::calc_fee_info_v2(&fee_field, &self.aka_client);
        let fee_result = tokio::time::timeout(Duration::from_secs(3), fee_calc_future).await;

        match fee_result {
            Ok(Ok(fee_detail)) => {
                detail.fee_jy = fee_detail.fee_jy;
                detail.fee_yh = fee_detail.fee_yh;
                detail.fee_gh = fee_detail.fee_gh;
                detail.fee_yj = fee_detail.fee_yj;
                detail.fee_js = fee_detail.fee_js;
                detail.fee_zg = fee_detail.fee_zg;
                detail.fee_qt = fee_detail.fee_qt;
                detail.fee_js2 = fee_detail.fee_js2;
                detail.fee_jg = fee_detail.fee_jg;
                detail.fee_total = fee_detail.fee_total;
                Ok(())
            }
            Ok(Err(err)) => Err(anyhow::anyhow!(err.to_string())),
            Err(_) => Err(anyhow::anyhow!("Fee calculation timeout")),
        }
    }

    // Async version of order generation
    async fn generate_order_async(&self, order_detail: &OrderDetail, risk_info: &Vec<PhoenixRiskCheckInfo>) -> Result<(PhoenixOrdStockorder, Vec<PhoenixOrdSuborder>)> {
        let mut order = convertdata::convert_to_stockorder(order_detail).await;
        let mut sub_order = convertdata::convert_to_suborder(order_detail).await;

        let sub_orders: Vec<PhoenixOrdSuborder> = risk_info
            .iter()
            .map(|v| {
                let mut so = sub_order.clone();
                so.sub_id = self.uid_generator.get_uid();
                so.channel_id = v.order_channel as i32;
                so.channel_type = v.channel_type;
                so.order_amount = v.order_amount;
                so.relate_order = 0;
                order.trade_type = v.is_rq;

                if so.channel_id == 0 {
                    so.order_status = OrderStatus::INVALID as i32;
                    so.remark = "channel未知".to_string();
                    order.order_status = OrderStatus::INVALID as i32;
                    order.order_memo = "channel未知".to_string();
                }

                if so.channel_type == ChannelType::INTERNAL as i32 {
                    so.relate_order = self.uid_generator.get_uid();
                }

                so
            })
            .collect();

        // Asynchronous persist and cache update
        self.persist_and_update_cache_async(&sub_orders, &order).await;

        if sub_orders.iter().any(|x| x.channel_id == 0) {
            return Err(anyhow::anyhow!("channel未知"));
        }

        Ok((order, sub_orders))
    }

    // Process internal orders asynchronously
    async fn process_internal_orders(&self, order_detail: &OrderDetail, sub_orders: &[PhoenixOrdSuborder]) -> Vec<OrderDetail> {
        let mut internal_orders = Vec::new();

        for sub_order in sub_orders {
            if sub_order.channel_type == ChannelType::INTERNAL as i32 {
                let opponent_order = OrderDetail::opponent_order(order_detail, sub_order).await;
                internal_orders.push(opponent_order.clone());

                // Send to opponent processing asynchronously
                let _ = self.tx_opponent.send(opponent_order).await;
            }
        }

        internal_orders
    }

    // Async version of asset processing using connection pool
    async fn process_assets_async(&self, order_detail: &OrderDetail) -> Result<()> {
        let client = self.assetscenter_pool.get_client().await;

        let result = assetchange::place_order_asset_change(order_detail, &mut client).await;

        // Return client to pool
        self.assetscenter_pool.return_client(client);

        if let Err(err) = result {
            error!("Asset change error: {:?}", err);
            tool::push_log(&err.to_string()).await;
            return Err(err);
        }

        Ok(())
    }

    // Async version of order placement
    async fn place_order_to_bp_async(&self, order_detail: &OrderDetail, sub_orders: &[PhoenixOrdSuborder]) -> Result<()> {
        let mut futures = Vec::new();

        for sub_order in sub_orders {
            let router_msg = convertdata::convert_to_orderrouterinfo(sub_order, order_detail).await;
            let tx_order = self.tx_order.clone();

            let future = async move {
                if let Err(err) = tx_order.send(router_msg) {
                    error!("Failed to send order to router: {:?}", err);
                }
            };

            futures.push(future);
        }

        // Send all orders in parallel
        futures::future::join_all(futures).await;
        Ok(())
    }

    // Async persist and cache update
    async fn persist_and_update_cache_async(&self, sub_orders: &[PhoenixOrdSuborder], order: &PhoenixOrdStockorder) {
        // Send persist operations asynchronously
        let persist_futures = vec![
            self.tx_persist.send(PersistData::StockOrder(Box::new(order.clone()))),
            self.tx_persist.send(PersistData::SubOrder(Box::new(sub_orders.to_vec()))),
        ];

        for future in persist_futures {
            if let Err(err) = future.await {
                error!("Persist operation failed: {}", err);
            }
        }

        // Update cache asynchronously
        let cache_key = CacheKey {
            order_id: order.order_no,
            ..Default::default()
        };

        let cache_futures = vec![cache_key.update_order_cache(order, &self.redis_client), cache_key.update_sub_order_cache(&sub_orders.to_vec(), &self.redis_client)];

        for future in cache_futures {
            if let Err(err) = future.await {
                error!("Cache update failed: {}", err);
            }
        }
    }

    // Batch processing for high-throughput scenarios
    pub async fn batch_process_orders(&self, orders: Vec<OrderReq>) -> Vec<Result<OrderResp>> {
        const BATCH_SIZE: usize = 10;
        let mut results = Vec::with_capacity(orders.len());

        for chunk in orders.chunks(BATCH_SIZE) {
            let futures = chunk.iter().map(|order| self.place_order_optimized(order));
            let batch_results = futures::future::join_all(futures).await;
            results.extend(batch_results);
        }

        results
    }
}

// Performance monitoring utilities
pub struct OrderMetrics {
    pub total_orders: AtomicU64,
    pub successful_orders: AtomicU64,
    pub failed_orders: AtomicU64,
    pub avg_processing_time: AtomicU64,
}

impl OrderMetrics {
    pub fn new() -> Self {
        Self {
            total_orders: AtomicU64::new(0),
            successful_orders: AtomicU64::new(0),
            failed_orders: AtomicU64::new(0),
            avg_processing_time: AtomicU64::new(0),
        }
    }

    pub fn record_order(&self, success: bool, processing_time_ms: u64) {
        self.total_orders.fetch_add(1, Ordering::Relaxed);

        if success {
            self.successful_orders.fetch_add(1, Ordering::Relaxed);
        } else {
            self.failed_orders.fetch_add(1, Ordering::Relaxed);
        }

        // Simple moving average
        let current_avg = self.avg_processing_time.load(Ordering::Relaxed);
        let new_avg = (current_avg + processing_time_ms) / 2;
        self.avg_processing_time.store(new_avg, Ordering::Relaxed);
    }

    pub fn get_success_rate(&self) -> f64 {
        let total = self.total_orders.load(Ordering::Relaxed);
        if total == 0 {
            return 0.0;
        }

        let successful = self.successful_orders.load(Ordering::Relaxed);
        successful as f64 / total as f64
    }
}
