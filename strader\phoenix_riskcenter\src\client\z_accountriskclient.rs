use anyhow::{anyhow, Result};
use common::logclient::LogClient;
use protoes::phoenixaccountriskcenter::account_risk_center_client::AccountRiskCenterClient;
use protoes::phoenixaccountriskcenter::{MarginRatioReq, MarginRatioResp, PhoenixStockPositionRequest, PhoenixStockPositionResponse, UserAssetsReq, UserAssetsResp, UserPositionReq, UserPositionResp};
use tonic::transport::Channel;
use tracing::*;
#[derive(Clone)]
pub struct AccountRiskClient {
    pub client: AccountRiskCenterClient<Channel>,
}

impl AccountRiskClient {
    pub async fn new(url: &String) -> Self {
        loop {
            match AccountRiskCenterClient::connect(url.clone()).await {
                Ok(accountclient) => return Self { client: accountclient },
                Err(err) => {
                    error!("connect to accountriskcenter failed: {:?}", err);
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("accountriskcenter connect faild :{}", err.to_string())).await;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn query_margin_ratio(&mut self, req: &MarginRatioReq) -> Result<MarginRatioResp> {
        let ret = self.client.query_margin_ratio(req.to_owned()).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }

        let margin_ratio = ret.unwrap();
        Ok(margin_ratio.into_inner())
    }
    // pub async fn query_user_toal_assets(&mut self, req: &UserAssetsReq) -> Result<UserTotalAssetsResp> {
    //   let ret = self.client.query_user_toal_assets(req.to_owned()).await;
    //   if ret.as_ref().is_err() {
    //     error!("{:?}", ret.as_ref().err().unwrap());
    //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
    //   }
    //   let user_toal_assets = ret.unwrap();
    //   Ok(user_toal_assets.into_inner())
    // }

    pub async fn query_user_assets(&mut self, req: &UserAssetsReq) -> Result<UserAssetsResp> {
        let ret = self.client.query_user_assets(req.to_owned()).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let user_assets = ret.unwrap();
        Ok(user_assets.into_inner())
    }

    pub async fn query_user_positions(&mut self, req: &UserPositionReq) -> Result<UserPositionResp> {
        let ret = self.client.query_user_positions(req.to_owned()).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let user_positions = ret.unwrap();
        Ok(user_positions.into_inner())
    }
    pub async fn query_stock_positions(&mut self, req: &PhoenixStockPositionRequest) -> Result<PhoenixStockPositionResponse> {
        let ret = self.client.query_stock_positions(req.to_owned()).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let stock_positions = ret.unwrap();
        Ok(stock_positions.into_inner())
    }
}
