pub use super::controller::*;
use futures;

// use crate::client::{AccountRiskClient, HqCenterClient};
// use crate::service::ordercontroller::OrderController;
// use akaclient::akaclient::{AkaCacheOption, AkaClient};
use crate::config::settings::Settings;
use protoes::phoenixriskcenter::phoenix_riskcenter_server::PhoenixRiskcenter;
use protoes::phoenixriskcenter::PhoenixRiskCheckRequest;
use protoes::phoenixriskcenter::{PhoenixRiskCheckResponse, PhoenixRiskRequest, PhoenixRiskResponse};
use std::fmt::Debug;
use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot, RwLock};
use tonic::{self, Request, Response, Status};
use tracing::*;
use utility::{errors, errors::ErrorCode};
type StubType = Arc<RwLock<RiskCenterController>>;
// use tracing::*;
// use crate::basicdata::marketclosetimecache::CloseTimeCached;

// use common::logclient::*;

// type StubType = Arc<Controller>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    // order_dispacther: mpsc::Sender<PhoenixRiskCheckInfo>,
    set_close: Option<oneshot::Sender<()>>,
    // mqclient: QuotationClient,
}
struct ControllerDispatch<OT>(ControllerAction, oneshot::Receiver<OT>);

impl<OT: 'static + Debug + Send> ControllerDispatch<OT> {
    fn new<T>(f: T) -> Self
    where
        T: for<'c> FnOnce(&'c mut RiskCenterController) -> Pin<Box<dyn futures::Future<Output = OT> + Send + 'c>>,
        T: Send + 'static,
    {
        let (tx, rx) = oneshot::channel();

        ControllerDispatch(
            Box::new(move |ctrl: StubType| -> Pin<Box<dyn futures::Future<Output = ()> + Send + 'static>> {
                Box::pin(async move {
                    let mut wg = ctrl.write().await;
                    if let Err(t) = tx.send(f(&mut wg).await) {
                        error!("Controller action can not be return: {:?}", t);
                    }
                })
            }),
            rx,
        )
    }
}

fn map_dispatch_err<T: 'static>(_: mpsc::error::SendError<T>) -> tonic::Status {
    tonic::Status::unknown("Server temporary unavaliable")
}
type ControllerRet<OT> = Result<OT, tonic::Status>;
type ServerRet<OT> = Result<Response<OT>, tonic::Status>;

fn map_dispatch_ret<OT: 'static>(recv_ret: Result<ControllerRet<OT>, oneshot::error::RecvError>) -> ServerRet<OT> {
    match recv_ret {
        Ok(ret) => {
            info!("收到结果，开始分发结果返回客户端");
            ret.map(Response::new)
        }
        Err(_) => Err(Status::unknown("Dispatch ret unreach")),
    }
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        // let mut akacache = AkaCacheOption::default();
        // akacache = AkaCacheOption {
        //     use_cache: settings.system.cacheflag,
        //     mq_uri: format!("{}{}", &settings.mq.amqpaddr, &settings.notification.vhost),
        //     exchange: settings.notification.notification_exchanger.to_string(),
        //     routing_keys: settings.notification.riskcenter_routing_key.to_string(),
        //     ..akacache.clone()
        // };
        // info!("缓存开关:{:?}", &akacache);
        // let hq_center_client = HqCenterClient::new(&settings.servers.hqcenterserver).await;
        // let akacenterconn = AkaClient::init(settings.servers.akacenterserver.to_string(), &akacache).await;
        // let account_risk_client = AccountRiskClient::new(&settings.servers.accountriskserver).await;
        // let order_controller = OrderController::new(&akacenterconn).await;
        // let stub = RiskCenterController {
        //     order_controller,
        //     account_risk_client,
        //     akacenterconn,
        //     hq_center_client,
        // };
        let stub = RiskCenterController::new(settings).await;

        let stub = Arc::new(RwLock::new(stub));

        let (tx, _rx) = mpsc::channel(16);
        let (tx_close, _rx_close) = oneshot::channel();

        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
        };

        svr_handler
    }

    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

#[tonic::async_trait]
impl PhoenixRiskcenter for ServerHandler {
    async fn phoenix_risk_check(&self, request: Request<PhoenixRiskCheckRequest>) -> Result<Response<PhoenixRiskCheckResponse>, Status> {
        info!("收到获取订单通道的请求...:{:?}", &request);

        let mut stub = self.stub.write().await;

        let mut order = match request.into_inner().queryinfo {
            Some(v) => v,
            None => {
                let result = PhoenixRiskCheckResponse {
                    ret_code: errors::get_error_code(ErrorCode::CodeSystemErrRequest).0,
                    ret_msg: errors::get_error_code(ErrorCode::CodeSystemErrRequest).1,
                    retinfo: Vec::new(),
                };
                return Ok(Response::new(result));
            }
        };

        let ret = stub.phoenix_risk_check(&mut order);
        if let Ok(res_ret) = ret.await {
            Ok(Response::new(res_ret))
        } else {
            // Ok(Response::new(ret.await.unwrap()))
            let result = PhoenixRiskCheckResponse {
                ret_code: errors::get_error_code(ErrorCode::CodeSystemErrRequest).0,
                ret_msg: errors::get_error_code(ErrorCode::CodeSystemErrRequest).1,
                retinfo: Vec::new(),
            };
            return Ok(Response::new(result));
        }
    }

    async fn phoenix_risk_test(&self, request: Request<PhoenixRiskRequest>) -> Result<Response<PhoenixRiskResponse>, Status> {
        let ControllerDispatch(act, rt) = ControllerDispatch::new(move |ctrl: &mut RiskCenterController| Box::pin(async move { ctrl.phoenix_risk_test(request.into_inner()).await }));

        self.task_dispacther.send(act).await.map_err(map_dispatch_err)?;
        map_dispatch_ret(rt.await)
    }
}
