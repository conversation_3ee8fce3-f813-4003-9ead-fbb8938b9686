use crate::client::AccountRiskClient;
// use crate::config::settings::Settings;
use akaclient::akaclient::AkaClient;
use anyhow::Result;
use chrono::{offset::TimeZone, Local};
use common::constant;
use common::logclient::{log_debug, log_error, LogClient};
use core::option::Option;
use protoes::phoenixaccountriskcenter::{PhoenixStockPositionRequest, PhoenixStockPositions, PhoenixUserAssets, PhoenixUserPositions, UserPositionReq};
use protoes::phoenixakacenter::AccountInfo;
use protoes::phoenixakacenter::SpecialAccount;
use protoes::phoenixakacenter::{ChannelConfig, ChannelHoldLimit, MarketInfo, StockChannelReq, StockInfo, StockQuota};
use protoes::phoenixriskcenter::{PhoenixRiskCheckInfo, PhoenixRiskCheckResponse};
use tonic::{self, Status};
use tracing::*;
use utility::{errors, errors::ErrorCode};
#[derive(Clone)]
pub struct OrderController {
    count_channel: i64,
    bottom_channel: i64,
}
impl OrderController {
    pub async fn new(aka_client: &AkaClient) -> Self {
        let mut internal_bottom_channel = 7 as i64;
        let mut internal_count_channel = 16 as i64;
        if let Ok(channels) = aka_client.query_all_channel_info().await {
            if let Some(internal_channel) = channels
                .iter()
                .find(|&p| p.channel_attr == common::constant::InternalChannelAttr::Bottom as i32 && p.channel_type == common::constant::ChannelType::INTERNAL as i32)
            {
                internal_bottom_channel = internal_channel.channel_id as i64;
            }
            if let Some(internal_channel) = channels
                .iter()
                .find(|&p| p.channel_attr == common::constant::InternalChannelAttr::Count as i32 && p.channel_type == common::constant::ChannelType::INTERNAL as i32)
            {
                internal_count_channel = internal_channel.channel_id as i64;
            }
        }
        info!("兜底通道编号:{},交易柜台编号:{}", internal_bottom_channel, internal_count_channel);
        OrderController {
            count_channel: internal_count_channel,
            bottom_channel: internal_bottom_channel,
        }
    }

    //买单处理逻辑:
    //1. 检查账户状态是否有效,如果无效则返回
    //2. 检查商品是否有效,无效则返回
    //3. 然后根据用户下单的商品和用户id,判断下单通道。判断规则:
    // 1. 根据用户ID和商品ID从接口获取所有的可用通道
    // 2. 判断用户的通道规则,按照默认通道还是按照用户自定义通道,
    // 3. 如果是用户通道优先,则查找该用户的配置的通道信息,没有则按照默认通用通道。
    //       如果给该用户配置过通道,则以该用户配置的通道为最高优先级,如果全部关闭了(禁止交易）,则不允许下单；
    //       如果配置了多个通道,其中一个关闭了,还有一个没关闭,则认为该用户只允许下该未关闭通道的订单。
    // 4. 如果是默认通用通道,则查找用户的通用通道配置;
    // 5. 如果通道状态已经关闭,则不使用该通道

    // 如果是空头账户，下买单后，持仓量不能>0
    pub async fn phoenix_handle_buy_order(
        &mut self,
        order: &mut PhoenixRiskCheckInfo,
        account_info: &PhoenixUserAssets,
        market_info: &MarketInfo,
        aka_client: &AkaClient,
        counter_partys: &Vec<SpecialAccount>,
        account_risk_client: &mut AccountRiskClient,
        stockquota: &Option<StockQuota>,
        counter_stockquota: &Option<StockQuota>,
        marginratio: f64,
        user_account: &AccountInfo,
    ) -> Result<PhoenixRiskCheckResponse, Status> {
        info!("处理买单:{:?}", &order);
        let mut resp = PhoenixRiskCheckResponse {
            ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
            ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
            retinfo: Vec::new(),
        };

        /* 3. 根据用户unit_id和商品ID,从接口获取所有的通道信息*/
        let mut stock_channel_req = StockChannelReq::default();
        stock_channel_req.user_id = order.user_id;
        stock_channel_req.stock_id = order.stock_id;
        stock_channel_req.order_direction = order.order_direction;

        let channel_configs_result = aka_client.query_stock_channel(stock_channel_req).await;
        if channel_configs_result.as_ref().is_err() {
            error!(
                "不能根据用户id:{}和品种ID:{}找到相应的通道配置信息:{:?}",
                order.unit_id,
                order.stock_id,
                channel_configs_result.as_ref().err().unwrap()
            );
            common::logclient::log_error(&format!(
                "不能根据用户id:{}和品种ID:{}找到相应的通道配置信息:{:?}",
                order.unit_id,
                order.stock_id,
                channel_configs_result.as_ref().err().unwrap()
            ))
            .await;
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;

            return Ok(resp);
        }
        let channel_configs = channel_configs_result.unwrap();

        //// 如果没有配置该品种的通道信息,则不能下买单
        if channel_configs.len() <= 0 && order.order_direction == constant::OrderDirection::BUY as i32 {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;
            error!("不能根据用户id:{}和品种ID:{}找到相应的通道配置信息,不能下买单", order.unit_id, order.stock_id);
            log_error(&format!("不能根据用户id:{}和品种ID:{}找到相应的通道配置信息,不能下买单", order.unit_id, order.stock_id)).await;
            if let Ok(log_client) = LogClient::get() {
                log_client
                    .push_error(&format!("不能根据用户id:{}和品种ID:{}找到相应的通道配置信息,不能下买单", order.unit_id, order.stock_id))
                    .await;
            }

            return Ok(resp);
        }
        //账户下买单判断上限，柜台通道卖MAX(运营二持仓可用数-配置的下限，0)
        // let mut user_positions_req = UserPositionReq::default();
        // user_positions_req.unit_id = 0;
        // user_positions_req.stock_id = order.stock_id;

        let user_positions_req = UserPositionReq {
            unit_id: order.unit_id,
            stock_id: order.stock_id,
            user_id: 0,
        };

        let user_positions_ret = account_risk_client.query_user_positions(&user_positions_req).await;
        if user_positions_ret.as_ref().is_err() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).0;
            resp.ret_msg = format!("获取用户的当前持仓量错误:{}", user_positions_ret.as_ref().err().unwrap());
            error!("获取用户的当前持仓量错误");
            return Ok(resp);
        }
        let userpositions: Vec<PhoenixUserPositions> = user_positions_ret.unwrap().positions.clone(); //Vec::new();
        info!("接口返回的账户持仓信息:{:?}", userpositions);
        let unit_positions: PhoenixUserPositions;
        if userpositions.len() > 0 {
            unit_positions = userpositions[0].clone();
        } else {
            unit_positions = PhoenixUserPositions::default();
        }
        info!("该用户股票代码:{}的持仓:{:?}", order.stock_id, unit_positions);
        // userpositions = user_positions_ret.unwrap().positions.clone();
        // let mut unit_positions = &PhoenixUserPositions::default();
        // let pos = userpositions.iter().find(|&x| x.unit_id == order.unit_id);
        // if pos.is_some() {
        //     unit_positions = pos.unwrap();
        // }
        // info!("接口返回的账户持仓信息:{:?}", unit_positions);

        //空头账户下单后数量判定 持仓量不能>0
        if user_account.rq_account_type == common::constant::RqAccountType::Short as i32 {
            let new_amount = unit_positions.amount + order.order_amount as i64;
            if new_amount > 0 {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).1;
                error!("空头账户下买单,持仓量不能>0");
                log_error(&format!("空头账户下买单,持仓量不能>0")).await;
                return Ok(resp);
            }
        }

        //账户持仓上下限
        if stockquota.is_some() {
            let quota = stockquota.clone().unwrap();
            if unit_positions.amount as i32 + unit_positions.prebuy_amount as i32 + order.order_amount > quota.max_hold {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderErrorNumber).0;
                resp.ret_msg = format!("下单后持仓将超上限,下单失败！");
                error!("下单量超过上限,最大可持仓量{}", quota.max_hold);
                log_error(&format!("下单量超过上限,最大可持仓量{}", quota.max_hold)).await;
                return Ok(resp);
            }
        }

        //对手方持仓
        let mut counter_party_positions = &PhoenixUserPositions::default();
        for c in counter_partys {
            let counter_party_pos = userpositions.iter().find(|&x| x.user_id == c.unit_id);
            if counter_party_pos.is_some() {
                counter_party_positions = counter_party_pos.unwrap();
            }
        }
        info!("交易对手方账号持仓信息{:?}", counter_party_positions);

        let mut available_channels: Vec<ChannelConfig>;

        //根据用户ID拆分所有可用通道
        //如果用户配了通道优先使用配置通道，否则使用品种组通道
        let (user_channels, group_channels): (Vec<ChannelConfig>, Vec<ChannelConfig>) = channel_configs.into_iter().partition(|v| v.user_id > 0);
        if user_channels.len() > 0 {
            available_channels = user_channels;
        } else {
            available_channels = group_channels;
        }
        available_channels.sort_by(|a, b| a.channel_level.cmp(&b.channel_level));

        let ret = aka_client.query_channel_hold_limit(order.stock_id, 0).await;
        if ret.as_ref().is_err() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;
            error!("获取品种通道的最大量错误:{}", ret.as_ref().err().unwrap());
            return Ok(resp);
        }

        //品种通道的最大持仓量信息
        let commidity_channel_data = ret.as_ref().unwrap().1.clone();
        let mut total_max_hold = ret.as_ref().unwrap().0;
        let mut new_total: i64 = 0;
        for val in commidity_channel_data.iter() {
            new_total += val.max_holdnum;
        }

        if new_total < total_max_hold {
            total_max_hold = new_total;
        }

        //query current positions
        let pos_req = PhoenixStockPositionRequest {
            stock_id: order.stock_id as i64,
            channel_id: 0 as i64,
        };

        //品种当前持仓
        let ret = account_risk_client.query_stock_positions(&pos_req).await;
        if ret.as_ref().is_err() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
            resp.ret_msg = format!("获取品种的当前持仓量错误:{}", ret.as_ref().err().unwrap());
            error!("获取品种的当前持仓量错误:{},", ret.as_ref().unwrap_err());
            log_error(&format!("获取品种的当前持仓量错误:{},", ret.as_ref().unwrap_err())).await;
            return Ok(resp);
        }
        //股票得所有持仓数据
        let stock_positions = ret.unwrap().data;
        info!("该股票得持仓数据:{:?}", &stock_positions);

        //根据优先级查找相应的通道
        // let mut matched_channel: Option<&ChannelConfigurations> = None;
        info!("买单通道匹配顺序:{:?}", &available_channels);

        // 1) 先判断有没有全部关闭得通道
        let closed_channel: Vec<&ChannelConfig> = available_channels
            // .clone()
            .iter()
            .filter(|c| c.channel_id == constant::VALUE_ALL)
            .collect();

        info!("通道ID为0(全部通道)得数量:{}", &closed_channel.len());
        for chn in closed_channel {
            if chn.channel_status == (constant::ChannelStatus::OFF as i32) || chn.channel_status == (constant::ChannelStatus::CLOSED as i32) || chn.channel_status == (constant::ChannelStatus::SELL as i32) {
                //如果是所有通道 都禁止交易
                resp.ret_code = errors::get_error_code(ErrorCode::CodeChannelClosed).0;
                resp.ret_msg = errors::get_error_code(ErrorCode::CodeChannelClosed).1;
                error!("该账户【{}】的所有交易通道已经被关闭,禁止交易", account_info.unit_id);
                log_error(&format!("该账户【{}】的所有交易通道已经被关闭,禁止交易", account_info.unit_id)).await;
                // if let Ok(log_client) = LogClient::get() {
                //     log_client.push_error(&format!("该账户【{}】的所有交易通道已经被关闭,禁止交易", account_info.unit_id)).await;
                // }
                return Ok(resp);
            } else {
                continue; //如果是所有通道,但是状态又不是禁止交易,则认为无效
            }
        }
        let mut order_results: Vec<PhoenixRiskCheckInfo> = Vec::new();

        // 2) 根据通道拆分订单

        //总下单量
        let mut remain_order_amount = order.order_amount as i64;

        for chn in available_channels.iter() {
            if chn.channel_status == (constant::ChannelStatus::NORMAL as i32) || chn.channel_status == (constant::ChannelStatus::BUY as i32) {
                let ret = aka_client.query_channel_info(chn.channel_id).await;
                if ret.as_ref().is_err() {
                    error!("不能找到通道ID为{}的通道信息", chn.channel_id);
                    continue;
                }
                let chninfo2 = ret.unwrap();
                if chninfo2.channel_id == 0 {
                    error!("不能找到通道ID为{}的通道信息", chn.channel_id);
                    continue;
                }
                if chninfo2.channel_id == -1
                    || chninfo2.channel_state == (constant::ChannelStatus::OFF as i32)
                    || chninfo2.channel_state == (constant::ChannelStatus::CLOSED as i32)
                    || chninfo2.channel_state == (constant::ChannelStatus::SELL as i32)
                {
                    info!("通道id:{}得通道不存在或者不能下卖单", chninfo2.channel_id);
                    log_debug(&format!("通道id:{}得通道不存在或者不能下卖单", chninfo2.channel_id)).await;
                    continue;
                }
                //这里判断是否休市时间
                info!("start to check channel qfstate. channel:{:?}", &chninfo2);
                let mut is_closed = false;
                let market_close = AkaClient::query_market_close_info(aka_client).await;
                if let Ok(close_info) = market_close {
                    info!("所有的临时休市时间信息:{:#?}", &close_info);
                    for v in close_info {
                        if v.market_id == market_info.market_id {
                            let current_time = Local::now();
                            let st = utility::timeutil::build_naive_date_time(&v.start_time);
                            let et = utility::timeutil::build_naive_date_time(&v.end_time);
                            let st_l = Local.from_local_datetime(&st).unwrap();
                            let et_l = Local.from_local_datetime(&et).unwrap();
                            if st_l < current_time && current_time < et_l {
                                error!("临时休市中,不能交易。当前时间:{:?},休市起始时间:{:?},休市结束时间:{:?}", current_time, st_l, et_l);
                                log_error(&format!("临时休市中,不能交易。当前时间:{:?},休市起始时间:{:?},休市结束时间:{:?}", current_time, st_l, et_l)).await;
                                is_closed = true;
                            }
                        }
                    }
                }
                if is_closed == true && chninfo2.qfii_state == constant::YesOrNo::NO as i32 {
                    //如果是休市时间,则继续下一个通道
                    info!("channel id: {} in in close time and qfii_state=0", &chninfo2.channel_id);
                    continue;
                }
                // let close_time_cache = CloseTimeCached::new();
                // let is_closed = close_time_cache.check_currenttime_is_closetime(aka_client, market_info.market_id, market_info.market_type as i32).await;
                // if is_closed && chninfo2.qfii_state == constant::YesOrNo::NO as i32 {
                //   //如果是休市时间,则继续下一个通道
                //   info!("channel id: {} in in close time and qfii_state=0", &chninfo2.channel_id);
                //   continue;
                // }

                //获取品种剩余最大持仓量
                let commidity_available_amount = OrderController::check_commidity_available_amounts(order.stock_id as i64, &stock_positions, total_max_hold);
                if remain_order_amount > commidity_available_amount {
                    error!("下单量+当前股票全部持仓量超过整体最大持仓量，不能下单");
                    resp.ret_code = errors::get_error_code(ErrorCode::CodeChannelClosed).0;
                    resp.ret_msg = errors::get_error_code(ErrorCode::CodeChannelClosed).1;
                    error!("该股票持仓量超过限制,禁止交易");
                    log_error(&format!("该股票持仓量超过限制,禁止交易,stockid:{}", order.stock_id)).await;
                    // if let Ok(log_client) = LogClient::get() {
                    //     log_client.push_error(&format!("该股票持仓量超过限制,禁止交易")).await;
                    // }
                    return Ok(resp);
                }

                //拆单已经完成
                if remain_order_amount <= 0 {
                    break;
                }

                //先生成一个订单,数量为0
                let mut order_detail: PhoenixRiskCheckInfo = PhoenixRiskCheckInfo {
                    unit_id: order.unit_id,
                    stock_id: order.stock_id,
                    order_price: order.order_price,
                    order_amount: 0,
                    order_direction: order.order_direction,
                    order_type: order.order_type,
                    order_channel: chninfo2.channel_id,
                    market_id: order.market_id,
                    trade_mode: order.trade_mode,
                    channel_type: chninfo2.channel_type,
                    agent_account: order.agent_account,
                    is_rq: constant::TradeType::Normal as i32,
                    margin_ratio: marginratio,
                    user_id: order.user_id,
                    base_currency: account_info.currency.clone(),
                };
                let available_amount: i64; // = 0;

                //柜台通道卖
                if chn.channel_type == constant::ChannelType::INTERNAL as i32 {
                    if counter_stockquota.is_some() {
                        let quota = counter_stockquota.as_ref().unwrap();
                        available_amount = (counter_party_positions.amount - counter_party_positions.frozen_amount) - quota.min_hold as i64;
                    } else {
                        continue;
                    }
                    info!("柜台通道可分单额度:{}", available_amount);
                } else {
                    //获取该通道剩余得可用交易量
                    available_amount = OrderController::check_commidity_channel_max_value_exceeded(order.stock_id, chninfo2.channel_id, &commidity_channel_data, &stock_positions);
                }
                //比如可用数量是30000，剩余下单量是1000
                if available_amount <= 0 {
                    continue;
                }
                if remain_order_amount < available_amount {
                    //该通道可以下完全部
                    order_detail.order_amount = remain_order_amount as i32;
                    // order_detail.order_channel = chninfo2.channel_id as i32;
                    // order_detail.order_channel_type = chninfo2.channel_type;
                    remain_order_amount = 0;
                } else {
                    order_detail.order_amount = available_amount as i32;
                    // order_detail.order_channel = chninfo2.channel_id as i32;
                    // order_detail.order_channel_type = chninfo2.channel_type;
                    remain_order_amount -= available_amount; //此时total_order_amount变成-29000，表示全部拆分完成
                }

                order_results.push(order_detail);
            }
        }
        // if commidity_channel_data.total_max_hold > 0 {

        if order_results.len() > 0 && remain_order_amount <= 0 {
            info!("买单拆分完成:{:?}", &order_results);
            resp.ret_code = errors::get_error_code(ErrorCode::CodeOk).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeOk).1;
            resp.retinfo = order_results.to_owned();
            return Ok(resp);
        }

        error!("找不到能满足订单{:?}下单要求的通道", &order);
        log_error(&format!("找不到能满足订单{:?}下单要求的通道", &order)).await;
        resp.ret_code = errors::get_error_code(ErrorCode::CodeChannelClosed).0;
        resp.ret_msg = errors::get_error_code(ErrorCode::CodeChannelClosed).1;
        // if let Ok(log_client) = LogClient::get() {
        //     log_client.push_error(&format!("找不到能满足订单{:?}下单要求的通道", &order)).await;
        // }
        return Ok(resp);
    }

    //卖单处理逻辑:
    // 1. 收到用户卖单,根据卖单信息:unit_id和stock_id到持仓表(tstockposition)里查找所有的持仓信息,
    //      可交易量:current_amount-frozen_amount-tmp_frozen_amount
    // 2. 根据用户ID和商品ID获取所有的通道配置信息
    // 3. 根据通道的反向优先级处理订单
    //  // 1. 如果下单量超过正常通道的可下单量,则报废单
    //  // 2. 如果卖单量超过某个通道的持仓量,则生成多笔订单返回给委托服务。
    //  // 3. 只有一笔持仓,则判断该笔持仓的通道状态
    // 注意,需要拆零碎股
    #[tracing::instrument(name = "处理卖单", skip_all)]
    pub async fn phoenix_handle_sell_order(
        &mut self,
        order: &mut PhoenixRiskCheckInfo,
        account_info: &PhoenixUserAssets,
        market_info: &MarketInfo,
        aka_client: &AkaClient,
        goods_info: &StockInfo,
        account_risk_client: &mut AccountRiskClient,
        counter_party: bool,
        counter_partys: &Vec<SpecialAccount>,
        stockquota: &Option<StockQuota>,
        counter_stockquota: &Option<StockQuota>,
        marginratio: f64,
    ) -> Result<PhoenixRiskCheckResponse, Status> {
        info!("订单信息:{:?}", order);
        let mut resp = PhoenixRiskCheckResponse {
            ret_code: errors::get_error_code(ErrorCode::CodeOk).0,
            ret_msg: errors::get_error_code(ErrorCode::CodeOk).1,
            retinfo: Vec::new(),
        };
        let stock_channel_req = StockChannelReq {
            stock_id: order.stock_id,
            order_direction: order.order_direction,
            user_id: order.user_id,
        };
        let channel_configs_result = aka_client.query_stock_channel(stock_channel_req).await;
        if channel_configs_result.as_ref().is_err() {
            error!(
                "不能根据用户id:{}和品种ID:{}找到相应的通道配置信息:{:?}",
                order.unit_id,
                order.stock_id,
                channel_configs_result.as_ref().err().unwrap()
            );
            resp.ret_code = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeCommidityChannelNotConfigured).1;
            return Ok(resp);
        }
        let channel_configs = channel_configs_result.unwrap();
        info!("通道配置信息:{:?}", channel_configs);
        let mut available_channels: Vec<ChannelConfig>; //= Vec::new();

        //根据用户ID拆分所有可用通道
        //客户通道按优先级优先，在客户通道里存在的产品组通道过滤掉再排序 ab，bc=abc
        let (user_channels, group_channels): (Vec<ChannelConfig>, Vec<ChannelConfig>) = channel_configs.into_iter().partition(|v| v.user_id > 0);
        if user_channels.len() > 0 {
            available_channels = user_channels;
        } else {
            available_channels = group_channels;
        }
        available_channels.sort_by(|a, b| a.channel_level.cmp(&b.channel_level));
        //兜底通道
        let internalchann = ChannelConfig {
            id: 0,
            channel_id: self.bottom_channel, //constant::ChannelID::BOTTOMCHANNEL as i64,
            channel_name: "兜底通道".to_string(),
            user_id: 0,
            channel_level: 99,
            channel_type: constant::ChannelType::INTERNAL as i32,
            channel_status: 1,
            commodity_group: 0,
            qfii_state: 0,
        };
        available_channels.push(internalchann);
        //该用户此券的所有持仓数据
        let mut is_rq = constant::TradeType::Normal as i32;
        let mut user_positions_req = UserPositionReq::default();
        user_positions_req.unit_id = order.unit_id;
        user_positions_req.stock_id = order.stock_id;
        let user_positions_ret = account_risk_client.query_user_positions(&user_positions_req).await;
        if user_positions_ret.as_ref().is_err() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).0;
            resp.ret_msg = format!("获取品种的当前持仓量错误:{}", user_positions_ret.as_ref().err().unwrap());
            error!("获取品种的当前持仓量错误:{}", user_positions_ret.as_ref().unwrap_err());
            log_error(&format!("获取品种的当前持仓量错误:{}", user_positions_ret.as_ref().unwrap_err())).await;
            return Ok(resp);
        }
        info!("从分帐户获取到的分帐户持仓数据:{:?}", &user_positions_ret);
        let userpositions = user_positions_ret.as_ref().unwrap().positions.clone();
        let unit_positions = userpositions.iter().find(|&x| x.unit_id == order.unit_id);
        //没有持仓
        if unit_positions.is_none() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).1;
            return Ok(resp);
        }
        let user_positions = unit_positions.unwrap();
        //是否有融券额度
        let rq_amount: i32 = user_positions.securities_borrow_available;
        info!("融券额度:{}", rq_amount);
        if rq_amount != 0 {
            is_rq = constant::TradeType::Rq as i32;
        }
        info!("该用户持仓数据:{:?}", user_positions);
        //账户下卖单判断下限，柜台通道买max(额度上限-运营二持仓-预买数量，0)
        if stockquota.is_some() {
            let quota = stockquota.clone().unwrap();
            if user_positions.amount as i32 - user_positions.frozen_amount as i32 - order.order_amount < quota.min_hold {
                resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderErrorNumber).0;
                resp.ret_msg = format!("下单后持仓将超下限,下单失败!");
                error!("下单量超过卖出下限,最大可卖量{:?}", quota.min_hold.abs() + user_positions.amount.abs() as i32);
                log_error(&format!("下单量超过卖出下限,最大可卖量{:?}", quota.min_hold.abs() + user_positions.amount.abs() as i32)).await;
                return Ok(resp);
            }
        }
        //该券所有用户持仓
        let mut user_total_positions_req = UserPositionReq::default();
        user_total_positions_req.unit_id = 0;
        user_total_positions_req.stock_id = order.stock_id;
        let user_total_positions_ret = account_risk_client.query_user_positions(&user_total_positions_req).await;
        if user_total_positions_ret.as_ref().is_err() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).0;
            resp.ret_msg = format!("获取品种的当前所有用户持仓量错误:{}", user_total_positions_ret.as_ref().err().unwrap());
            error!("获取品种的当前所有用户持仓量错误:{}", user_total_positions_ret.as_ref().err().unwrap());
            log_error(&format!("获取品种的当前所有用户持仓量错误:{}", user_total_positions_ret.as_ref().err().unwrap())).await;
            return Ok(resp);
        }
        let usertotal_positions = user_total_positions_ret.as_ref().unwrap().positions.clone();
        // let mut user_total_positions: Vec<PhoenixUserPositions>; // = Vec::new();
        //所有空仓
        let mut user_total_positions: Vec<PhoenixUserPositions> = usertotal_positions.clone().iter().filter(|&p| p.amount < 0).map(|f| f.clone()).collect();
        //除对手方外的空仓
        let mut conds_positions: Vec<PhoenixUserPositions> = Vec::new();
        //对手方持仓
        let mut counter_party_positions = &PhoenixUserPositions::default();
        info!("交易对手方账号:{:?}", counter_partys);
        for c in counter_partys {
            let counter_party_pos = usertotal_positions.iter().find(|&x| x.unit_id == c.unit_id);
            if counter_party_pos.is_some() {
                counter_party_positions = counter_party_pos.unwrap();
            }
            conds_positions = user_total_positions.clone().iter().filter(|&x| x.unit_id != c.unit_id).map(|f| f.clone()).collect();
            user_total_positions = conds_positions.clone();
        }
        info!("交易对手方账号持仓信息:{:?}", counter_party_positions);
        let user_total_hold_positions: i64 = conds_positions.iter().map(|x| x.amount).sum();
        let user_total_prebuy_amount: i64 = conds_positions.iter().map(|x| x.prebuy_amount).sum();
        //柜台未配置，默认的额度上限为除柜台所有交易账户空仓之和
        let mut user_all_short_positions = user_total_hold_positions + user_total_prebuy_amount;
        if user_all_short_positions < 0 {
            user_all_short_positions = user_all_short_positions.abs();
        } else {
            user_all_short_positions = 0;
        }
        info!("该券除柜台所有交易账户空仓之和:{:?}", user_all_short_positions);

        //卖单量
        let initial_amount = order.order_amount;
        // info!("该股票的最小交易量:{},下单量:{}", goods_info.min_value, initial_amount);

        let mut available_total_amount = 0;

        info!(
            "该用户持仓数据,持仓量:{},冻结量:{},qfii量:{},融券可用额:{}",
            user_positions.amount, user_positions.frozen_amount, user_positions.qfii_amount, user_positions.securities_borrow_available
        );
        //计算用户可交易量
        if is_rq == constant::TradeType::Rq as i32 {
            available_total_amount += user_positions.amount - user_positions.frozen_amount + user_positions.securities_borrow_available as i64;
            info!("融券可用额度:{}", available_total_amount);
        } else {
            available_total_amount += user_positions.amount - user_positions.frozen_amount;
            info!("非融券可用额度:{}", available_total_amount);
        }

        //没有可交易量
        if available_total_amount <= 0 {
            error!("该用户没有可用额度......");
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).1;
            return Ok(resp);
        }

        info!(
            "开始处理卖单,持仓信息:{:?},用户可交易量:{},最小交易量:{},下单量:{}",
            &user_positions, &available_total_amount, goods_info.min_value, initial_amount
        );
        // log_debug(&format!(
        //     "开始处理卖单,持仓信息:{:?},用户可交易量:{},最小交易量:{},下单量:{}",
        //     &user_positions, &available_total_amount, goods_info.min_value, initial_amount
        // ))
        // .await;

        //下单量小于最小值,但是持仓量超过最小值,则报错
        if available_total_amount > goods_info.min_value as i64 && initial_amount < goods_info.min_value {
            error!("持仓量超过最小值,但是下单量小于最小值,不能下单");
            resp.ret_code = errors::get_error_code(ErrorCode::CodeOrderErrorNumber).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeOrderErrorNumber).1;
            // if let Ok(log_client) = LogClient::get() {
            //   log_client.push_error(&format!("持仓量超过最小值,但是下单量小于最小值,不能下单")).await;
            // }
            return Ok(resp);
        }

        //报单量超过实际可卖量
        if order.order_amount > available_total_amount as i32 {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).1;
            return Ok(resp);
        }

        /* 2. 根据用户配置,获取所有的通道信息*/
        info!("卖单通道优先级匹配顺序:{:?}", &available_channels);

        /*3. 根据通道规则去拆单*/
        //查询分账户通道
        let mut stock_positions_req = PhoenixStockPositionRequest::default();
        stock_positions_req.stock_id = order.stock_id;
        stock_positions_req.channel_id = constant::VALUE_ALL;
        let ret = account_risk_client.query_stock_positions(&stock_positions_req).await;
        if ret.as_ref().is_err() {
            resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).1;
            return Ok(resp);
        }
        let stpositions = ret.unwrap();
        let mut stock_positions = stpositions.data;
        info!("分账户通道持仓信息:{:?}", &stock_positions);

        // info!("该券用户总持仓和分账户总持仓的差值:{:?}", hold_diff);
        if stock_positions.iter().find(|&s| s.channel_id == self.bottom_channel /*constant::ChannelID::BOTTOMCHANNEL as i64*/).is_none() {
            let st_pos = PhoenixStockPositions {
                stock_id: order.stock_id,
                channel_id: self.bottom_channel, //constant::ChannelID::BOTTOMCHANNEL as i64,
                current_amount: 0,
                prebuy_amount: 0,
                frozen_amount: 0,
                frozen_amount_temp: 0,
                total_value: 0.0,
                total_value_hkd: 0.0,
                stock_type: 0,
                is_qfii: 0,
            };
            stock_positions.push(st_pos);
        }
        if stock_positions.iter().find(|&s| s.channel_id == self.count_channel /*constant::ChannelID::INTERNALCHANNEL as i64*/).is_none() {
            let st_pos = PhoenixStockPositions {
                stock_id: order.stock_id,
                channel_id: self.count_channel, //constant::ChannelID::INTERNALCHANNEL as i64,
                current_amount: 0,
                prebuy_amount: 0,
                frozen_amount: 0,
                frozen_amount_temp: 0,
                total_value: 0.0,
                total_value_hkd: 0.0,
                stock_type: 0,
                is_qfii: 0,
            };
            stock_positions.push(st_pos);
        }
        info!("处理后(兜底和柜台)的分账户通道持仓信息:{:?}", &stock_positions);
        let mut sorted_stock_positions: Vec<PhoenixStockPositions> = Vec::new();
        // 首先查找移除通道,就是持仓通道不能从通道配置信息中找到的
        let (mut remove_sp, mut exist_sp): (Vec<PhoenixStockPositions>, Vec<PhoenixStockPositions>) =
            stock_positions.into_iter().partition(|sp| available_channels.iter().find(|&c| c.channel_id == sp.channel_id).is_none());

        //通道已经移除的持仓以及不是虚拟通道的持仓，按照持仓量排序（由大到小）
        exist_sp.sort_by(|a, b| {
            let channel_a = available_channels.iter().find(|&x| x.channel_id == a.channel_id).map_or(ChannelConfig { ..Default::default() }, |f| f.to_owned());
            let channel_b = available_channels.iter().find(|&x| x.channel_id == b.channel_id).map_or(ChannelConfig { ..Default::default() }, |f| f.to_owned());
            channel_a.channel_level.cmp(&channel_b.channel_level)
        });
        remove_sp.sort_by(|a, b| b.current_amount.cmp(&a.current_amount));
        //合并成一个持仓数据（排序后）
        sorted_stock_positions.extend(remove_sp.clone());
        sorted_stock_positions.extend(exist_sp.clone());
        info!("通道配置移除的持仓:{:?},未移除持仓:{:?}", &remove_sp, &exist_sp);
        info!("订单处理顺序:{:?}", sorted_stock_positions);
        /*交易品种 和 通道关系的状态不再考虑, 只考虑通道基本信息的状态 */

        // 从配置的通道已经移除的持仓信息中，除去通道类型内盘（2）的所有持仓信息，优先处理。
        // let mut removed_external_positions: Vec<PhoenixStockPositions> = Vec::new();

        // for val in remove_sp.into_iter() {
        //   let chn_info_ret = aka_client.query_channel_info(val.channel_id).await;
        //   if chn_info_ret.as_ref().is_err() {
        //     error!("不能找到通道ID为{}的通道信息", val.channel_id);
        //     continue;
        //   }
        //   let chn_info = chn_info_ret.unwrap();
        //   if chn_info.channel_type == constant::ChannelType::EXTERNAL as i32 {
        //     removed_external_positions.push(val.to_owned());
        //   }
        // }

        //通道未移除的，则按照通道优先级反向排序
        // available_channels.sort_by(|a, b| a.channel_level.cmp(&b.channel_level));
        // // 查找通道未被移除的订单,通道配置已经根据规定的顺序排序
        // for av_chn in available_channels.iter() {
        //   let sp = exist_sp.iter().find(|p| p.l_channel_id as i64 == av_chn.channel_id);
        //   if sp.is_some() && sorted_stock_positions.iter().find(|ss| ss.l_channel_id == sp.unwrap().l_channel_id).is_none() {
        //     sorted_stock_positions.push(sp.unwrap().clone());
        //   }
        // }

        let mut total_check_result: Vec<PhoenixRiskCheckInfo> = Vec::new();

        let hands_num = goods_info.hands_num.parse::<i32>().unwrap();
        // info!("hands_num:{}", hands_num);
        // if goods_info.stock_type == constant::StockType::HSCY as i32 || goods_info.stock_type == constant::StockType::HSKC as i32 {
        //     info!("该股票是科创版,不需要处理零碎股,手数设置为1");
        //     hands_num = 1;
        // }

        // if stock_total_hold_positions <= 0 {
        //   resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).0;
        //   resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountNoStockPosition).1;
        //   return Ok(resp);
        // }
        // //报单量超过实际可卖量
        // if order.order_amount > stock_total_hold_positions as i32 {
        //   resp.ret_code = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).0;
        //   resp.ret_msg = errors::get_error_code(ErrorCode::CodeAccountExceedPosition).1;
        //   return Ok(resp);
        // }

        for od in sorted_stock_positions.iter_mut() {
            info!("开始根据通道持仓数据处理卖单，当前持仓信息:{:?}", &od);
            //如果订单量小于等于0,则认为订单已经拆分完毕
            if order.order_amount <= 0 {
                break;
            }
            let chn_info_ret = aka_client.query_channel_info(od.channel_id).await;
            if chn_info_ret.as_ref().is_err() {
                error!("不能找到通道ID为{}的通道信息", od.channel_id);
                continue;
            }
            let chn_info = chn_info_ret.unwrap();
            info!("找到通道的基本信息(通道基本信息):{:?}", &chn_info);

            if chn_info.channel_state == (constant::ChannelStatus::CLOSED as i32) || chn_info.channel_state == (constant::ChannelStatus::OFF as i32) || chn_info.channel_state == (constant::ChannelStatus::BUY as i32) {
                info!("通道【{}】已经被关闭", chn_info.channel_name);
                continue;
            }

            //这里判断是否休市时间
            info!("判断通道是否休市时间:{:?}", &chn_info);
            let mut is_closed = false;
            let market_close = AkaClient::query_market_close_info(aka_client).await;
            if let Ok(close_info) = market_close {
                info!("所有的临时休市时间信息:{:?}", &close_info);
                for v in close_info {
                    if v.market_id == market_info.market_id {
                        let current_time = Local::now();
                        let st = utility::timeutil::build_naive_date_time(&v.start_time);
                        let et = utility::timeutil::build_naive_date_time(&v.end_time);
                        let st_l = Local.from_local_datetime(&st).unwrap();
                        let et_l = Local.from_local_datetime(&et).unwrap();
                        if st_l < current_time && current_time < et_l {
                            //如果是休市时间也不是qfii通道,则继续下一个通道，是的话 分账户通道继续卖
                            info!("临时休市中,不能交易.当前时间:{:?},休市起始时间:{:?},休市结束时间:{:?}", current_time, st_l, et_l);
                            log_error(&format!("临时休市中,不能交易.当前时间:{:?},休市起始时间:{:?},休市结束时间:{:?}", current_time, st_l, et_l)).await;
                            is_closed = true;
                            break;
                        }
                    }
                }
            }
            if is_closed == true && chn_info.qfii_state == constant::YesOrNo::NO as i32 {
                //如果是休市时间,则继续下一个通道
                info!("channel id: {} in in close time and qfii_state=0", &chn_info.channel_id);
                continue;
            }
            // let close_time_cache = CloseTimeCached::new();
            // let is_closed = close_time_cache.check_currenttime_is_closetime(aka_client, market_info.market_id, market_info.market_type as i32).await;
            // if is_closed {
            //   if chn_info.qfii_state == constant::YesOrNo::NO as i32 {
            //     //如果是休市时间也不是qfii通道,则继续下一个通道，是的话 分账户通道继续卖
            //     info!("channel id: {} is in close time and qfii_state = 0", &chn_info.channel_id);
            //     continue;
            //   }
            // }
            let mut channel_type = chn_info.channel_type;
            let mut channel_id = chn_info.channel_id;
            //外盘判断通道可平量
            if chn_info.channel_type == constant::ChannelType::EXTERNAL as i32 {
                info!("判断外盘通道可平量");
                if (od.current_amount - od.frozen_amount - od.frozen_amount_temp) <= 0 {
                    info!(
                        "通道[{}:{}]可平量current_amount:{} - frozen_amount:{} - frozen_amount_temp:{} is <= 0",
                        od.channel_id, chn_info.channel_name, od.current_amount, od.frozen_amount, od.frozen_amount_temp
                    );
                    continue;
                } else {
                    if !counter_party {
                        info!(
                            "非交易对手方账号，判断当前数量.可平量current_amount:{} - frozen_amount:{} - frozen_amount_temp:{} ",
                            od.current_amount, od.frozen_amount, od.frozen_amount_temp
                        );
                        let current_amount = od.current_amount - od.frozen_amount - od.frozen_amount_temp;
                        let odd_lots = current_amount % hands_num as i64;
                        if odd_lots != 0 {
                            od.current_amount -= odd_lots;
                        }
                    }
                }
            } else if chn_info.channel_id == self.count_channel {
                // info!("判断柜台通道可平量");
                //柜台通道判断可平量
                if counter_stockquota.is_some() {
                    let quota = counter_stockquota.clone().unwrap();
                    od.current_amount = quota.max_hold as i64 - counter_party_positions.amount - counter_party_positions.prebuy_amount;
                    info!(
                        "柜台通道的额度最大值:{},交易对手方持仓量:{}, 预买量:{}, 可用量:{}",
                        quota.max_hold, counter_party_positions.amount, counter_party_positions.prebuy_amount, od.current_amount
                    );
                } else {
                    od.current_amount = user_all_short_positions - counter_party_positions.amount - counter_party_positions.prebuy_amount;
                    info!(
                        "柜台通道的额度最大值:{},交易对手方持仓量:{}, 预买量:{}, 可用量:{}",
                        user_all_short_positions, counter_party_positions.amount, counter_party_positions.prebuy_amount, od.current_amount
                    );
                }
                info!("柜台通道的可用量:{}", od.current_amount);
                if od.current_amount <= 0 {
                    info!("柜台通道可用分单额度:{}, <= 0", od.current_amount);
                    continue;
                }
            }

            //如果订单量小于等于0,则认为订单已经拆分完毕
            if order.order_amount <= 0 {
                info!("order_amount: {} is <= 0", order.order_amount);
                break;
            }

            // if goods_info.stock_type == constant::StockType::HSCY as i32 || goods_info.stock_type == constant::StockType::HSKC as i32 {
            // if goods_info.stock_type == constant::YesOrNo::YES as i32 {
            //如果是科创版,卖单量超过最小限制,则不处理零碎股,如果小于最小限制,则直接内盘

            //如果下单量小于最小报单量,则直接到内盘成交
            if counter_party == false && initial_amount < goods_info.min_value {
                info!("非交易对手方账号，下单量小于最小报单量,直接到内盘兜底通道成交");
                channel_type = constant::ChannelType::INTERNAL as i32;
                channel_id = self.bottom_channel/*constant::ChannelID::BOTTOMCHANNEL as i64*/;
            }

            //判断通道，如果是内盘通道，则全部往内盘发送
            //**********************
            order.base_currency = account_info.currency.clone();
            total_check_result.extend(
                self.generate_order_check_result(&od, hands_num, order, channel_type, channel_id, &goods_info, counter_party, is_rq, marginratio)
                    .iter()
                    .cloned(),
            );
        }

        //一个单子都未拆分
        if order.order_amount == initial_amount {
            // info!("剩余未拆下单量:{},不能下单", order.order_amount);
            resp.ret_code = errors::get_error_code(ErrorCode::CodeChannelClosed).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeChannelClosed).1;

            return Ok(resp);
        }
        if order.order_amount > 0 && is_rq == constant::TradeType::Rq as i32 {
            info!("剩余未拆下单量:{},不能下融券单", order.order_amount);
            resp.ret_code = errors::get_error_code(ErrorCode::CodeChannelClosed).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeChannelClosed).1;
            if let Ok(log_client) = LogClient::get() {
                log_client.push_error(&format!("拆单失败")).await;
            }
            return Ok(resp);
        }
        if order.order_amount > 0 {
            let od_ret = PhoenixRiskCheckInfo {
                order_amount: order.order_amount,
                order_channel: 0,
                channel_type: 0,
                is_rq: 0,
                ..order.clone()
            };
            total_check_result.push(od_ret);
        }
        // // /* 3.4 再处理通道未变化得持仓*/
        if total_check_result.len() <= 0 {
            //拆单失败
            resp.ret_code = errors::get_error_code(ErrorCode::CodeChannelClosed).0;
            resp.ret_msg = errors::get_error_code(ErrorCode::CodeChannelClosed).1;
            if let Ok(log_client) = LogClient::get() {
                log_client.push_error(&format!("拆单失败")).await;
            }
            return Ok(resp);
        }
        //合并兜底通道拆分的数量
        let (bottom_channels, mut others_channels): (Vec<PhoenixRiskCheckInfo>, Vec<PhoenixRiskCheckInfo>) = total_check_result
            .clone()
            .into_iter()
            .partition(|v| v.order_channel == self.bottom_channel /* constant::ChannelID::BOTTOMCHANNEL as i64 */);
        info!("兜底通道拆分结果:{:?}", &bottom_channels);
        let mut bottom_channel_result = PhoenixRiskCheckInfo::default();
        if bottom_channels.len() > 1 {
            let bottom_amount: i32 = bottom_channels.iter().map(|x| x.order_amount).sum();
            for bottom in bottom_channels.iter() {
                bottom_channel_result = bottom.clone();
            }
            bottom_channel_result.order_amount = bottom_amount;
            others_channels.push(bottom_channel_result);
            total_check_result = others_channels;
        }
        resp.retinfo = total_check_result;
        info!("处理卖单完成,结果:{:?}", &resp);
        Ok(resp)
    }

    fn generate_order_check_result(
        &self,
        sp: &PhoenixStockPositions,
        hands_num: i32,
        order: &mut PhoenixRiskCheckInfo,
        channeltype: i32,
        channelid: i64,
        _goods_info: &StockInfo,
        counter_party: bool,
        is_rq: i32,
        marginratio: f64,
    ) -> Vec<PhoenixRiskCheckInfo> {
        info!("开始处理订单:{:?}", sp);
        let mut check_result: Vec<PhoenixRiskCheckInfo> = Vec::new();

        let mut total_lots: i32; //= 0;
        if channelid == self.bottom_channel
        /*constant::ChannelID::BOTTOMCHANNEL as i64*/
        {
            total_lots = order.order_amount as i32;
            info!("兜底通道，全部分单完毕,total_lots:{}", total_lots);
        } else {
            if channelid == self.count_channel
            /*constant::ChannelID::INTERNALCHANNEL as i64*/
            {
                total_lots = sp.current_amount as i32;
                info!("柜台通道，全部分单完毕,total_lots:{}", total_lots);
            } else {
                total_lots = (sp.current_amount - sp.frozen_amount - sp.frozen_amount_temp) as i32;
                info!("上手通道，当前分单数量:{}", total_lots);
            }
        }
        // info!("持仓可用的交易量:{}", total_lots);
        if total_lots < 0 {
            total_lots = 0;
        }
        info!("当前分账户通道持仓可用的交易量:{},未处理下单量:{}", total_lots, order.order_amount);
        let order_lots: i32; //下单量

        if order.order_amount <= total_lots {
            order_lots = order.order_amount;
            order.order_amount = 0;
        } else {
            order_lots = total_lots;
            order.order_amount -= total_lots; //总下单量减去分账户持仓的可用量
        }

        if counter_party {
            //如果是交易对手方,则不需要处理零碎股
            info!("处理对手方的订单信息,数量:{}", order_lots);
            let od_ret = PhoenixRiskCheckInfo {
                order_amount: order_lots,
                order_channel: sp.channel_id,
                channel_type: channeltype,
                is_rq: is_rq,
                margin_ratio: marginratio,
                ..order.clone()
            };
            check_result.push(od_ret);
            return check_result;
        }

        //如果是自撮合通道,则不需要进行零碎股拆分
        if channeltype == (constant::ChannelType::INTERNAL as i32) && channelid == self.bottom_channel
        /*constant::ChannelID::BOTTOMCHANNEL as i64*/
        {
            let od_ret = PhoenixRiskCheckInfo {
                order_amount: order_lots,
                order_channel: channelid,
                channel_type: channeltype,
                is_rq: is_rq,
                margin_ratio: marginratio,
                ..order.clone()
            };
            if od_ret.order_amount != 0 {
                check_result.push(od_ret);
                return check_result;
            }
        }

        let mut hands_num = hands_num;
        if sp.stock_type == constant::StockType::HSCY as i32 || sp.stock_type == constant::StockType::HSKC as i32 {
            info!("该持仓是科创版,不需要处理零碎股,手数设置为1");
            hands_num = 1;
        }

        // let mut odd_lots: i32 = 0;
        let odd_lots = order_lots % hands_num; //零碎股

        // let mut channel_type = channeltype;
        // let mut channel_id = channelid;
        // if order_lots < goods_info.min_value {
        //   //如果报单量小于最小值,则进行内盘撮合
        //   info!("该下单量小于最小下单量,需要进行内盘撮合");
        //   channel_type = constant::ChannelType::INTERNAL as i32;
        //   channel_id = constant::ChannelID::BOTTOMCHANNEL as i64;
        // }

        if order_lots >= hands_num {
            info!("处理整手的订单信息,数量:{}", order_lots - odd_lots);
            let od_ret = PhoenixRiskCheckInfo {
                order_amount: order_lots - odd_lots,
                order_channel: sp.channel_id,
                channel_type: channeltype,
                is_rq: is_rq,
                margin_ratio: marginratio,
                ..order.clone()
            };
            check_result.push(od_ret);
        }
        if odd_lots > 0 {
            //处理零碎股
            info!("处理零碎股,数量  :{}", odd_lots);
            let odd_ret = PhoenixRiskCheckInfo {
                order_amount: odd_lots,
                order_channel: self.bottom_channel, // constant::ChannelID::BOTTOMCHANNEL as i64,
                channel_type: constant::ChannelType::INTERNAL as i32,
                is_rq: is_rq,
                margin_ratio: marginratio,
                ..order.clone()
            };

            check_result.push(odd_ret);
        }
        info!("生成的订单信息:{:?}", check_result);
        check_result
    }

    fn check_commidity_available_amounts(stockid: i64, positions: &Vec<PhoenixStockPositions>, total_max_hold: i64) -> i64 {
        // let current_total_hold_positions: i64 = positions.iter().map(|x| x.amount).sum();
        // let current_prebuy_total: i64 = positions.iter().map(|x| x.prebuy_amount).sum();

        let (current_total_hold_positions, current_prebuy_total): (i64, i64) = positions.iter().filter(|f| f.stock_id == stockid).fold((0, 0), |(acc1, acc2), v| (acc1 + v.current_amount, acc2 + v.prebuy_amount));

        info!(
            "整体持仓量: stock id:{},  current amount:{}, prebuy amount:{}, max total allowed number:{}",
            stockid, current_total_hold_positions, current_prebuy_total, total_max_hold
        );
        return total_max_hold - (current_prebuy_total + current_total_hold_positions);
        //当前持仓数量+交易下单量+预买量大于最大允许的持仓量，则验证失败
        // if current_total_hold_positions + ordernumber + current_prebuy_total > commax.total_max_hold {
        //     return true;
        // } else {
        //     return false;
        // }
    }

    fn check_commidity_channel_max_value_exceeded(stockid: i64, channelid: i64, commax: &Vec<ChannelHoldLimit>, positions: &Vec<PhoenixStockPositions>) -> i64 {
        let ret = commax.iter().find(|&x| x.stock_id == stockid && x.channel_id == channelid);
        let commidity_channel_info: ChannelHoldLimit;
        if ret.is_none() {
            commidity_channel_info = ChannelHoldLimit::default();
        } else {
            commidity_channel_info = ret.unwrap().clone();
        }
        let mut cur_amount = 0;
        let mut pre_amount: i64 = 0;
        let pos = positions.iter().find(|&x| x.channel_id == channelid && x.stock_id == stockid);
        if pos.is_some() {
            cur_amount = pos.unwrap().current_amount;
            pre_amount = pos.unwrap().prebuy_amount;
        }
        info!(
            "通道持仓量:stock id:{}, channel id:{}, current amount:{}, prebuy amount:{}, max allowed number:{}",
            stockid, channelid, cur_amount, pre_amount, commidity_channel_info.max_holdnum
        );
        let available_amount = commidity_channel_info.max_holdnum - (cur_amount + pre_amount);
        if available_amount <= 0 {
            return 0;
        } else {
            return available_amount;
        }
        // //当前持仓数量+交易下单量大于最大允许的持仓量，则验证失败
        // if cur_amount + ordernumber + pre_amount > commidity_channel_info.max_holdnum {
        //   return true;
        // } else {
        //   return false;
        // }
    }
}
