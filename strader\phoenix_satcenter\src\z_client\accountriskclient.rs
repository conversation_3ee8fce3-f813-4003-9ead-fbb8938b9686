use anyhow::{anyhow, Result};
use common::logclient::LogClient;
use protoes::phoenixaccountriskcenter::account_risk_center_client::AccountRiskCenterClient;
use protoes::phoenixaccountriskcenter::{AccountAvailableAmountRequest, AccountAvailableAmountResponse};
use tonic::transport::Channel;
use tracing::*;

#[derive(Clone)]
pub struct AccountRiskClient {
    pub client: AccountRiskCenterClient<Channel>,
    pub url: String,
}
impl AccountRiskClient {
    pub async fn new(url: &String) -> Self {
        // AccountRiskClient {
        //     client: None,
        //     url: url.to_owned(),
        // }
        loop {
            match AccountRiskCenterClient::connect(url.clone()).await {
                Ok(accountclient) => {
                    return Self {
                        client: accountclient,
                        url: url.to_owned(),
                    }
                }
                Err(err) => {
                    error!("connect to accountriskcenter failed: {:?}", err);
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("accountriskcenter connect faild :{}", err.to_string())).await;
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }
    // pub async fn init_client(&mut self) -> Result<AccountRiskCenterClient<Channel>> {
    //     if self.client.is_some() {
    //         return Ok(self.client.clone().unwrap());
    //     } else {
    //         let ret = AccountRiskCenterClient::connect(self.url.to_owned()).await;
    //         if ret.as_ref().is_err() {
    //             if let Ok(log_client) = LogClient::get() {
    //                 log_client.push_error(&format!("accountriskcenter connect failed :{}", ret.as_ref().err().unwrap().to_string())).await;
    //             }
    //             return Err(anyhow!(format!("accountriskcenter to riskcenter failed")));
    //         }
    //         let client = ret.expect("connect to riskcenter failed");
    //         info!("分账户风控中心连接成功....");
    //         self.client = Some(client);
    //         return Ok(self.client.clone().unwrap())
    //     }
    // }

    pub async fn query_account_available_amount(&mut self, req: &AccountAvailableAmountRequest) -> Result<AccountAvailableAmountResponse> {
        // let ret = self.init_client().await;
        // if ret.as_ref().is_err() {
        //     error!("{:?}", ret.as_ref().err().unwrap());
        //     return Err(anyhow!(ret.as_ref().err().unwrap().to_string()))
        // }
        let mut client = self.client.clone();
        match client.query_account_available_amount(req.to_owned()).await {
            Ok(val) => Ok(val.into_inner()),
            Err(status) => {
                error!("query_account_available_amount error: {:?}", status);
                info!("query_account_available_amount error !");
                let ret = AccountRiskCenterClient::connect(self.url.to_owned()).await;
                if ret.as_ref().is_err() {
                    if let Ok(log_client) = LogClient::get() {
                        log_client.push_error(&format!("accountriskcenter connect faild :{}", ret.as_ref().err().unwrap().to_string())).await;
                    }
                    return Err(anyhow!("try query_account_available_amount error: {:?}", ret.as_ref().err().unwrap().to_string()));
                }
                let mut client = ret.unwrap();
                if let Ok(val) = client.query_account_available_amount(req.to_owned()).await {
                    return Ok(val.into_inner());
                }
                if let Ok(log_client) = LogClient::get() {
                    log_client.push_error(&format!("query_account_available_amount error: {:?}", status)).await;
                }
                return Err(anyhow!("query_account_available_amount error {:?}", status));
            }
        }
    }
}
