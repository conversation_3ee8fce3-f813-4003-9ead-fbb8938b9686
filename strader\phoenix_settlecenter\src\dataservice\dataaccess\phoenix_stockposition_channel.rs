use crate::dataservice::{
    dbsetup::DbConnection,
    entities::{phoenix_stockposition_channel, prelude::*},
};
use anyhow::{anyhow, Result};
use rust_decimal::Decimal;
use sea_orm::sea_query::{BinO<PERSON>, Expr};
use sea_orm::{ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, DbErr, EntityTrait, IntoActiveModel, QueryFilter};
use serde_json::json;
use tracing::*;

impl phoenix_stockposition_channel::Model {
    #[allow(dead_code)]
    pub async fn find_by_unitid(unit_id: i64, db: &DbConnection) -> Result<Option<PhoenixStockpositionChannel>> {
        let ret = PhoenixStockpositionChannelEntity::find()
            .filter(phoenix_stockposition_channel::Column::PAccountUnitId.eq(unit_id))
            .one(db.get_connection())
            .await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        return Ok(ret.unwrap());
    }
    pub async fn find_by_condition<'a, 'async_trait, C>(db: &'a C, condition: Condition) -> Result<Vec<PhoenixStockpositionChannel>>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        let ret_data: Result<Vec<PhoenixStockpositionChannel>, DbErr> = PhoenixStockpositionChannelEntity::find().filter(condition).all(db).await;
        if ret_data.is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        Ok(ret_data.unwrap())
    }
    #[allow(dead_code)]
    pub async fn query_many<'a, 'async_trait, C>(unit_id: i64, db: &'a C) -> Result<Vec<PhoenixStockpositionChannel>>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        let mut conditions = Condition::all();
        if unit_id > 0 {
            conditions = conditions.add(phoenix_stockposition_channel::Column::PAccountUnitId.eq(unit_id));
        }
        let ret = PhoenixStockpositionChannelEntity::find().filter(conditions).all(db).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        Ok(ret.unwrap())
    }
    #[allow(dead_code)]
    pub async fn insert(data: &PhoenixStockpositionChannel, db: &DbConnection) -> Result<i64> {
        let newval = data.to_owned().into_active_model();

        let ret = newval.insert(db.get_connection()).await;

        //let ret=PhoenixAstUnitasset::insert(newval, db.get_connection()).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let id = ret.unwrap().id;
        return Ok(id);
    }

    #[allow(dead_code)]
    pub async fn update(data: &PhoenixStockpositionChannel, db: &DbConnection) -> Result<()> {
        let ret = phoenix_stockposition_channel::ActiveModel::from_json(json!(data));
        if ret.is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let newval = ret.unwrap();
        info!("{:?}", newval);
        if newval.id.is_not_set() {
            return Err(anyhow!("id不能为空"));
        }
        let ret = newval.update(db.get_connection()).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }

        return Ok(());
    }

    pub async fn update_total_value<'a, 'async_trait, C>(db: &'a C, unit_id: i32, stock_code: String, channel_id: i32, current_cost: Decimal) -> Result<usize>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        let ret_data = PhoenixStockpositionChannelEntity::update_many()
            .col_expr(phoenix_stockposition_channel::Column::PTotalValue, Expr::col(phoenix_stockposition_channel::Column::PTotalValue).sub(current_cost))
            .filter(phoenix_stockposition_channel::Column::PAccountUnitId.eq(unit_id))
            .filter(phoenix_stockposition_channel::Column::PStockCode.eq(stock_code))
            .filter(phoenix_stockposition_channel::Column::PChannelId.eq(channel_id))
            .exec(db)
            .await;

        if ret_data.as_ref().is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        return Ok(ret_data.unwrap().rows_affected as usize);
    }

    pub async fn update_current_amount<'a, 'async_trait, C>(db: &'a C, unit_id: i32, stock_code: String, channel_id: i32, current_amount: i32) -> Result<usize>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        let ret_data = PhoenixStockpositionChannelEntity::update_many()
            .col_expr(
                phoenix_stockposition_channel::Column::PAvgPrice,
                Expr::col(phoenix_stockposition_channel::Column::PAvgPrice).binary(BinOper::Mul, Expr::col(phoenix_stockposition_channel::Column::PCurrentAmount)),
            )
            .col_expr(
                phoenix_stockposition_channel::Column::PCurrentAmount,
                Expr::col(phoenix_stockposition_channel::Column::PCurrentAmount).add(current_amount),
            )
            .col_expr(
                phoenix_stockposition_channel::Column::PAvgPrice,
                Expr::col(phoenix_stockposition_channel::Column::PAvgPrice).binary(BinOper::Div, Expr::col(phoenix_stockposition_channel::Column::PCurrentAmount)),
            )
            .filter(phoenix_stockposition_channel::Column::PChannelId.eq(channel_id))
            .filter(phoenix_stockposition_channel::Column::PAccountUnitId.eq(unit_id))
            .filter(phoenix_stockposition_channel::Column::PStockCode.eq(stock_code))
            .exec(db)
            .await;

        if ret_data.as_ref().is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        return Ok(ret_data.unwrap().rows_affected as usize);
    }

    pub async fn update_frozen_amount<'a, 'async_trait, C>(db: &'a C, unit_id: i32, stock_code: String, channel_id: i32, frozen_amount: i32) -> Result<usize>
    where
        C: ConnectionTrait,
        'a: 'async_trait,
        C: 'async_trait,
    {
        let ret_data = PhoenixStockpositionChannelEntity::update_many()
            .col_expr(
                phoenix_stockposition_channel::Column::PFrozenAmount,
                Expr::col(phoenix_stockposition_channel::Column::PFrozenAmount).sub(frozen_amount),
            )
            .filter(phoenix_stockposition_channel::Column::PChannelId.eq(channel_id))
            .filter(phoenix_stockposition_channel::Column::PAccountUnitId.eq(unit_id))
            .filter(phoenix_stockposition_channel::Column::PStockCode.eq(stock_code))
            .exec(db)
            .await;

        if ret_data.as_ref().is_err() {
            return Err(anyhow!(ret_data.as_ref().err().unwrap().to_string()));
        }

        return Ok(ret_data.unwrap().rows_affected as usize);
    }
}
